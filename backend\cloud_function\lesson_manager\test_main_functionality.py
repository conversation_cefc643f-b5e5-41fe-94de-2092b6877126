#!/usr/bin/env python3
"""
Test main.py startup without running server
"""

import sys
import os

# Setup environment
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Set required environment variables
os.environ['FLASK_ENV'] = 'development'
os.environ['GEMINI_API_KEY'] = 'test-key-for-startup'

try:
    print("=== TESTING MAIN.PY FUNCTIONALITY ===")
    
    # Import main module
    print("Importing main module...")
    import main
    print("✓ Main module imported successfully")
    
    # Check if app is available
    if hasattr(main, 'app'):
        print("✓ Flask app is available")
        
        # Test with test client
        with main.app.test_client() as client:
            print("✓ Test client created")
            
            # Test health endpoint
            try:
                response = client.get('/api/health')
                print(f"✓ Health endpoint responded with status: {response.status_code}")
                if response.status_code == 200:
                    print("✓ Health endpoint is working correctly")
                else:
                    print(f"⚠ Health endpoint returned {response.status_code}")
                    print(f"Response: {response.get_data(as_text=True)}")
            except Exception as health_error:
                print(f"✗ Health endpoint error: {health_error}")
            
            # Test enhance-content endpoint (minimal test)
            try:
                test_data = {
                    "content": "Test content",
                    "user_id": "test_user",
                    "lesson_id": "test_lesson"
                }
                response = client.post('/api/enhance-content', 
                                     json=test_data,
                                     headers={'Content-Type': 'application/json'})
                print(f"✓ Enhance-content endpoint responded with status: {response.status_code}")
            except Exception as enhance_error:
                print(f"✗ Enhance-content endpoint error: {enhance_error}")
    
    else:
        print("✗ Flask app not found in main module")
    
    print("=== TEST COMPLETED ===")
    
except Exception as e:
    print(f"✗ Error during test: {e}")
    import traceback
    traceback.print_exc()

print("Test finished successfully")
