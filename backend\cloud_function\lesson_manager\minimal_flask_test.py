#!/usr/bin/env python3
"""
Minimal server test - just start Flask with minimal configuration
"""

import sys
import os

# Setup environment
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Set minimal environment
os.environ['FLASK_ENV'] = 'development'
os.environ['GEMINI_API_KEY'] = 'test-key-for-startup'

try:
    print("Importing Flask...")
    from flask import Flask, jsonify
    from flask_cors import CORS
    
    # Create minimal Flask app
    app = Flask(__name__)
    CORS(app)
    
    @app.route('/api/health')
    def health():
        return jsonify({"status": "ok", "message": "Minimal server working"})
    
    @app.route('/api/test')
    def test():
        return jsonify({"status": "test", "message": "Test endpoint working"})
    
    print("Flask app created successfully")
    print("Starting server on http://127.0.0.1:5002")
    
    # Start server
    app.run(
        host="127.0.0.1",
        port=5002,
        debug=False,
        use_reloader=False,
        threaded=True
    )
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
