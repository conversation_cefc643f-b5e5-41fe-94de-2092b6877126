#!/usr/bin/env python3
"""
Simple server test using the main.py Flask app but with controlled startup
"""

import sys
import os

# Set up environment
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Set required environment variables to prevent errors
os.environ['FLASK_ENV'] = 'development'
os.environ['GEMINI_API_KEY'] = 'test-key-placeholder'

def run_simple_server():
    try:
        print("=" * 50)
        print("SIMPLE SERVER TEST")
        print("=" * 50)
        
        # Import the main Flask app
        print("Importing main module...")
        import main
        print("✓ Main module imported")
        
        # Check if we have the Flask app
        if not hasattr(main, 'app'):
            print("✗ Flask app not found")
            return False
            
        print("✓ Flask app found")
        
        # Test with test client first
        print("Testing endpoints with test client...")
        with main.app.test_client() as client:
            # Test health endpoint
            response = client.get('/api/health')
            print(f"Health endpoint: {response.status_code}")
            
        # Now try to run the server in a controlled way
        print("Starting Flask server...")
        
        # Use simpler Flask startup to avoid Windows socket issues
        main.app.run(
            host="localhost",  # Use localhost instead of 0.0.0.0
            port=5003,         # Use a different port
            debug=False,       # Disable debug mode
            use_reloader=False, # Disable reloader
            threaded=True      # Enable threading
        )
        
    except KeyboardInterrupt:
        print("\nServer stopped by user")
        return True
    except Exception as e:
        print(f"Server error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = run_simple_server()
    print(f"Server test {'succeeded' if success else 'failed'}")
