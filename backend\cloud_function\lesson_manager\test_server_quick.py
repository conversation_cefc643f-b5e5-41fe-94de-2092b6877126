#!/usr/bin/env python3
"""
Windows-compatible server startup test
"""

import sys
import os
import time

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_server_startup():
    try:
        print("=== TESTING SERVER STARTUP ===")
        
        # Import main (should be fast now)
        print("Importing main module...")
        start_time = time.time()
        import main
        import_time = time.time() - start_time
        print(f"✓ Import completed in {import_time:.2f} seconds")
        
        # Test basic Flask functionality
        if hasattr(main, 'app'):
            print("✓ Flask app available")
            
            # Test with test client (no actual server binding)
            with main.app.test_client() as client:
                print("✓ Test client created")
                
                # Quick health check
                response = client.get('/api/health')
                print(f"✓ Health endpoint: {response.status_code}")
                if response.status_code == 200:
                    print("✓ Health endpoint working properly")
                else:
                    print(f"⚠ Health endpoint returned {response.status_code}")
        
        print("=== TEST COMPLETED SUCCESSFULLY ===")
        return True
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_server_startup()
    sys.exit(0 if success else 1)
