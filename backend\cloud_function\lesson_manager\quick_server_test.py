#!/usr/bin/env python3
"""
Quick server start test - just verify server can bind to port
"""

import sys
import os
import threading
import time

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def quick_server_test():
    try:
        print("=== QUICK SERVER TEST ===")
        
        # Import main after path setup
        import main
        
        print("✓ Main module imported successfully")
        print("✓ Flask app available:", hasattr(main, 'app'))
        
        if hasattr(main, 'app'):
            # Test if we can create a test client (doesn't require actual server startup)
            with main.app.test_client() as client:
                print("✓ Flask test client created successfully")
                
                # Test health endpoint
                response = client.get('/api/health')
                print(f"✓ Health endpoint response: {response.status_code}")
                
        print("=== SERVER TEST COMPLETE ===")
        
    except Exception as e:
        print(f"✗ Error during server test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    quick_server_test()
