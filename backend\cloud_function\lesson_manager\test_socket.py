import socket
import sys

print("Testing Windows socket functionality...")

try:
    # Test basic socket creation
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    print("✓ Socket created successfully")
    
    # Test binding to localhost
    sock.bind(('127.0.0.1', 0))  # Let system choose port
    port = sock.getsockname()[1]
    print(f"✓ Socket bound to 127.0.0.1:{port}")
    
    # Test listening
    sock.listen(1)
    print("✓ Socket listening successfully")
    
    sock.close()
    print("✓ Socket closed successfully")
    
    print("Windows socket functionality is working correctly")
    
except Exception as e:
    print(f"✗ Socket error: {e}")
    import traceback
    traceback.print_exc()
    
print("Socket test completed")
