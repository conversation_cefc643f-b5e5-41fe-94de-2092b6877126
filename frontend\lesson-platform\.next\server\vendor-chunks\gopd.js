"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/gopd";
exports.ids = ["vendor-chunks/gopd"];
exports.modules = {

/***/ "(ssr)/./node_modules/gopd/gOPD.js":
/*!***********************************!*\
  !*** ./node_modules/gopd/gOPD.js ***!
  \***********************************/
/***/ ((module) => {

eval("\r\n\r\n/** @type {import('./gOPD')} */\r\nmodule.exports = Object.getOwnPropertyDescriptor;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZ29wZC9nT1BELmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQSxXQUFXLGtCQUFrQjtBQUM3QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxwY1xcT25lRHJpdmVcXERlc2t0b3BcXERlc2t0b3BcXFNvbHludGFfV2Vic2l0ZVxcZnJvbnRlbmRcXGxlc3Nvbi1wbGF0Zm9ybVxcbm9kZV9tb2R1bGVzXFxnb3BkXFxnT1BELmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcclxuXHJcbi8qKiBAdHlwZSB7aW1wb3J0KCcuL2dPUEQnKX0gKi9cclxubW9kdWxlLmV4cG9ydHMgPSBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yO1xyXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/gopd/gOPD.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/gopd/index.js":
/*!************************************!*\
  !*** ./node_modules/gopd/index.js ***!
  \************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\r\n\r\n/** @type {import('.')} */\r\nvar $gOPD = __webpack_require__(/*! ./gOPD */ \"(ssr)/./node_modules/gopd/gOPD.js\");\r\n\r\nif ($gOPD) {\r\n\ttry {\r\n\t\t$gOPD([], 'length');\r\n\t} catch (e) {\r\n\t\t// IE 8 has a broken gOPD\r\n\t\t$gOPD = null;\r\n\t}\r\n}\r\n\r\nmodule.exports = $gOPD;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZ29wZC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0EsV0FBVyxhQUFhO0FBQ3hCLFlBQVksbUJBQU8sQ0FBQyxpREFBUTtBQUM1QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccGNcXE9uZURyaXZlXFxEZXNrdG9wXFxEZXNrdG9wXFxTb2x5bnRhX1dlYnNpdGVcXGZyb250ZW5kXFxsZXNzb24tcGxhdGZvcm1cXG5vZGVfbW9kdWxlc1xcZ29wZFxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xyXG5cclxuLyoqIEB0eXBlIHtpbXBvcnQoJy4nKX0gKi9cclxudmFyICRnT1BEID0gcmVxdWlyZSgnLi9nT1BEJyk7XHJcblxyXG5pZiAoJGdPUEQpIHtcclxuXHR0cnkge1xyXG5cdFx0JGdPUEQoW10sICdsZW5ndGgnKTtcclxuXHR9IGNhdGNoIChlKSB7XHJcblx0XHQvLyBJRSA4IGhhcyBhIGJyb2tlbiBnT1BEXHJcblx0XHQkZ09QRCA9IG51bGw7XHJcblx0fVxyXG59XHJcblxyXG5tb2R1bGUuZXhwb3J0cyA9ICRnT1BEO1xyXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/gopd/index.js\n");

/***/ })

};
;