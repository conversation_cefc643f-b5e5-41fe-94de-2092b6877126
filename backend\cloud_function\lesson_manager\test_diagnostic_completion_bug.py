#!/usr/bin/env python3

"""
Quick test to reproduce the diagnostic completion issue
"""

import requests
import json
import time

def test_diagnostic_completion_issue():
    """Test the diagnostic completion bug where 6 answers don't trigger teaching phase"""
    
    base_url = "http://127.0.0.1:5000"
    
    # Session data that should have 6 answers already
    test_request = {
        "query": "I'm ready to learn!", 
        "session_id": "session_6374daf8-82e8-49d6-811c-4b78ee81a0a4",
        "student_id": "student_test_123",
        "subject": "Mathematics",
        "topic": "Fractions",
        "grade": 5,
        "country": "USA",
        "curriculum": "US Common Core",
        "level": 5,
        "module_id": "fractions_basic_comparison"
    }
    
    print(f"🧪 Testing diagnostic completion issue...")
    print(f"📋 Request: {json.dumps(test_request, indent=2)}")
    
    try:
        response = requests.post(
            f"{base_url}/api/enhance-content",
            json=test_request,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"🌐 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ SUCCESS Response:")
            print(f"📄 Session ID: {data.get('session_id', 'N/A')}")
            print(f"🎯 Phase: {data.get('phase', 'N/A')}")
            print(f"📝 Content Preview: {data.get('content', '')[:200]}...")
            
            # Check if diagnostic completed correctly
            if 'teaching_start_level_' in data.get('phase', ''):
                print(f"✅ SUCCESS: Diagnostic completed, transitioned to teaching phase!")
                print(f"🎓 Teaching Level: {data.get('phase', '').replace('teaching_start_level_', '')}")
            elif 'diagnostic_probing_L5_ask_q1' in data.get('phase', ''):
                print(f"❌ BUG REPRODUCED: Still in diagnostic_probing_L5_ask_q1 despite having 6 answers")
                print(f"🔍 This confirms the diagnostic completion bug")
            else:
                print(f"⚠️  Unexpected phase: {data.get('phase', 'N/A')}")
                
        else:
            print(f"❌ ERROR: {response.status_code}")
            print(f"📄 Response: {response.text}")
            
    except Exception as e:
        print(f"💥 EXCEPTION: {str(e)}")

if __name__ == "__main__":
    test_diagnostic_completion_issue()
