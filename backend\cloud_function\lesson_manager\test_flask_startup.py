#!/usr/bin/env python3
"""
Test Flask server startup with different configurations
"""

import sys
import os

# Setup environment to avoid import errors
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Clear any problematic environment variables that might cause socket issues
problematic_vars = [
    'WERKZEUG_RUN_MAIN',
    'WERKZEUG_SERVER_FD', 
    'FLASK_RUN_FROM_CLI',
    'FLASK_ENV',
    'FLASK_DEBUG'
]

for var in problematic_vars:
    if var in os.environ:
        print(f"Removing environment variable: {var}={os.environ[var]}")
        del os.environ[var]

# Set safe environment variables
os.environ['GEMINI_API_KEY'] = 'test-key-for-startup'
os.environ['FLASK_ENV'] = 'development'

def test_flask_startup():
    try:
        print("=" * 50)
        print("TESTING FLASK STARTUP")
        print("=" * 50)
        
        # Import Flask directly first
        from flask import Flask
        print("✓ Flask imported successfully")
        
        # Create a minimal Flask app
        test_app = Flask(__name__)
        
        @test_app.route('/test')
        def test_route():
            return "Test successful"
        
        print("✓ Test Flask app created")
        
        # Try to run the test app
        print("Starting test Flask app...")
        test_app.run(
            host='127.0.0.1',
            port=5004,
            debug=False,
            use_reloader=False,
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\nTest Flask app stopped by user")
        return True
    except Exception as e:
        print(f"✗ Flask startup error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_flask_startup()
    print(f"Flask test {'passed' if success else 'failed'}")
