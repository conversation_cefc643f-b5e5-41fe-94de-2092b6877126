#!/usr/bin/env python3
"""
Test the diagnostic loop fix - ensure "I'm ready" after diagnostic completion doesn't trigger reset
"""

import sys
import os
import json
import time

# Setup environment
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Set required environment variables
os.environ['GEMINI_API_KEY'] = 'test-key-for-testing'
os.environ['FLASK_ENV'] = 'development'

def test_diagnostic_loop_fix():
    """Test that the diagnostic loop issue is fixed"""
    print("=" * 60)
    print("TESTING DIAGNOSTIC LOOP FIX")
    print("=" * 60)
    
    try:
        # Import main module
        print("Importing main module...")
        import main
        print("✓ Main module imported successfully")
        
        # Create test client
        with main.app.test_client() as client:
            print("✓ Test client created")
            
            # Test data - simulate a session where diagnostic is already completed
            test_data = {
                "content": "I'm ready, let's start the lesson",  # This should NOT trigger reset
                "user_id": "test_user_diagnostic_fix",
                "lesson_id": "test_lesson_diagnostic_fix",
                "student_name": "Test Student"
            }
            
            print("=" * 40)
            print("TEST CASE 1: User says 'I'm ready' after diagnostic completion")
            print("=" * 40)
            
            # First, let's simulate a completed diagnostic state
            # We'll make a request that should establish a teaching_start_level phase
            print("Step 1: Simulating diagnostic completion...")
            
            # Mock session data that represents completed diagnostic
            completed_diagnostic_data = {
                "content": "This is a test to establish completed diagnostic state",
                "user_id": "test_user_diagnostic_fix",
                "lesson_id": "test_lesson_diagnostic_fix",
                "student_name": "Test Student"
            }
            
            # This would normally be done by the diagnostic flow, but we'll simulate it
            response1 = client.post('/api/enhance-content', 
                                   json=completed_diagnostic_data,
                                   headers={'Content-Type': 'application/json'})
            
            print(f"Initial response status: {response1.status_code}")
            if response1.status_code == 200:
                print("✓ Initial request successful")
            else:
                print(f"⚠ Initial request returned {response1.status_code}")
                print(f"Response: {response1.get_data(as_text=True)}")
            
            print("\nStep 2: Testing 'I'm ready' message after diagnostic completion...")
            
            # Now test the actual fix - this should NOT cause a diagnostic reset
            response2 = client.post('/api/enhance-content', 
                                   json=test_data,
                                   headers={'Content-Type': 'application/json'})
            
            print(f"'I'm ready' response status: {response2.status_code}")
            
            if response2.status_code == 200:
                print("✓ Request successful")
                
                try:
                    response_data = response2.get_json()
                    if response_data:
                        print("✓ Response data received")
                        
                        # Check if the response indicates a diagnostic reset (which would be wrong)
                        content = response_data.get('content', '').lower()
                        
                        # These phrases would indicate a diagnostic reset (bad)
                        reset_indicators = [
                            'let me assess your current understanding',
                            'i need to understand your current level',
                            'let\'s start with a diagnostic',
                            'diagnostic assessment'
                        ]
                        
                        # These phrases would indicate continuation to teaching (good)
                        continuation_indicators = [
                            'let\'s begin',
                            'let\'s start',
                            'ready to learn',
                            'continue with'
                        ]
                        
                        has_reset_indicator = any(indicator in content for indicator in reset_indicators)
                        has_continuation_indicator = any(indicator in content for indicator in continuation_indicators)
                        
                        print(f"Response content preview: {content[:200]}...")
                        
                        if has_reset_indicator:
                            print("✗ DIAGNOSTIC LOOP DETECTED: Response indicates diagnostic reset")
                            print("   This means the fix didn't work - the system is still resetting")
                            return False
                        elif has_continuation_indicator:
                            print("✓ DIAGNOSTIC LOOP FIX WORKING: Response indicates teaching continuation")
                            return True
                        else:
                            print("? UNCLEAR: Response doesn't clearly indicate reset or continuation")
                            print("   This may require manual review of the full response")
                            return None
                            
                    else:
                        print("⚠ No response data received")
                        return None
                        
                except Exception as json_error:
                    print(f"⚠ Error parsing response JSON: {json_error}")
                    return None
                    
            else:
                print(f"✗ Request failed with status {response2.status_code}")
                print(f"Response: {response2.get_data(as_text=True)}")
                return False
                
    except Exception as e:
        print(f"✗ Test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_legitimate_start_lesson():
    """Test that legitimate 'start lesson' requests still work"""
    print("\n" + "=" * 60)
    print("TESTING LEGITIMATE START LESSON REQUESTS")
    print("=" * 60)
    
    try:
        import main
        
        with main.app.test_client() as client:
            # Test data - brand new user saying "start lesson"
            test_data = {
                "content": "start lesson",  # This SHOULD trigger diagnostic
                "user_id": "brand_new_user_test",
                "lesson_id": "new_lesson_test",
                "student_name": "New Student"
            }
            
            print("Testing brand new user saying 'start lesson'...")
            
            response = client.post('/api/enhance-content', 
                                 json=test_data,
                                 headers={'Content-Type': 'application/json'})
            
            print(f"Response status: {response.status_code}")
            
            if response.status_code == 200:
                response_data = response.get_json()
                if response_data:
                    content = response_data.get('content', '').lower()
                    
                    # For new users, we SHOULD see diagnostic indicators
                    diagnostic_indicators = [
                        'assess your current understanding',
                        'understand your current level',
                        'diagnostic',
                        'let me ask you some questions'
                    ]
                    
                    has_diagnostic_indicator = any(indicator in content for indicator in diagnostic_indicators)
                    
                    print(f"Response content preview: {content[:200]}...")
                    
                    if has_diagnostic_indicator:
                        print("✓ LEGITIMATE START LESSON WORKING: New user gets diagnostic")
                        return True
                    else:
                        print("⚠ UNCLEAR: New user didn't get obvious diagnostic indicators")
                        return None
                else:
                    print("⚠ No response data received")
                    return None
            else:
                print(f"✗ Request failed with status {response.status_code}")
                return False
                
    except Exception as e:
        print(f"✗ Test error: {e}")
        return False

def main():
    """Run all diagnostic loop fix tests"""
    print("DIAGNOSTIC LOOP FIX VERIFICATION")
    print("=" * 60)
    
    # Test 1: Ensure completed diagnostic doesn't reset
    result1 = test_diagnostic_loop_fix()
    
    # Test 2: Ensure legitimate start lesson still works
    result2 = test_legitimate_start_lesson()
    
    print("\n" + "=" * 60)
    print("TEST RESULTS SUMMARY")
    print("=" * 60)
    
    if result1 is True:
        print("✓ Diagnostic loop fix: WORKING")
    elif result1 is False:
        print("✗ Diagnostic loop fix: FAILED")
    else:
        print("? Diagnostic loop fix: UNCLEAR")
    
    if result2 is True:
        print("✓ Legitimate start lesson: WORKING")
    elif result2 is False:
        print("✗ Legitimate start lesson: FAILED")
    else:
        print("? Legitimate start lesson: UNCLEAR")
    
    overall_success = (result1 is True and result2 is True)
    print(f"\nOverall test result: {'PASS' if overall_success else 'NEEDS REVIEW'}")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
