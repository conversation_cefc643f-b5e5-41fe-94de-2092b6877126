#!/usr/bin/env python3
"""
Test actual server binding with timeout
"""

import sys
import os
import time
import threading

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def start_server_with_timeout():
    """Start server and stop after a few seconds to test if it can bind"""
    try:
        print("=== TESTING ACTUAL SERVER BINDING ===")
        
        # Import main
        import main
        
        print("✓ Main module imported successfully")
        
        # Set test mode environment
        os.environ['FLASK_ENV'] = 'development'
        os.environ['PORT'] = '5001'  # Use different port to avoid conflicts
        
        # Create a thread to stop the server after a few seconds
        def stop_server():
            time.sleep(3)  # Wait 3 seconds
            print("\n=== STOPPING TEST SERVER ===")
            os._exit(0)  # Force exit
        
        stop_thread = threading.Thread(target=stop_server, daemon=True)
        stop_thread.start()
        
        print("Starting Flask server for 3 seconds...")
        
        # Try to start the server
        main.app.run(
            host="127.0.0.1",  # Windows-compatible
            port=5001,
            debug=False,
            use_reloader=False,
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\nServer test stopped by user")
    except Exception as e:
        print(f"Server test error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    start_server_with_timeout()
