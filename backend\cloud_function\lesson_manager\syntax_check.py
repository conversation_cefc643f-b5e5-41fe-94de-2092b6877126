#!/usr/bin/env python3
"""
Simple syntax check for main.py
"""

try:
    print("Testing main.py syntax...")
    import sys
    sys.path.append('.')
    
    # Try to compile the file
    with open('main.py', 'r') as f:
        source = f.read()
    
    compile(source, 'main.py', 'exec')
    print("✅ main.py syntax is valid")
    
except SyntaxError as e:
    print(f"❌ SYNTAX ERROR in main.py:")
    print(f"   Line {e.lineno}: {e.text}")
    print(f"   Error: {e.msg}")
    print(f"   Position: {' ' * (e.offset - 1) if e.offset else ''}^")
    
except Exception as e:
    print(f"❌ OTHER ERROR: {e}")
