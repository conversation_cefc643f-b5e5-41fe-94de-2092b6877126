#!/usr/bin/env python3
"""
Fixed server startup script for Windows to address WinError 10038
"""

import sys
import os
import logging
import signal
from datetime import datetime

def setup_windows_environment():
    """Setup environment variables and paths for Windows compatibility"""
    # Ensure proper Python path
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)
    
    # Set default environment variables if not set
    os.environ.setdefault('FLASK_ENV', 'development')
    os.environ.setdefault('PYTHONUNBUFFERED', '1')
    
    # Windows-specific fixes
    if os.name == 'nt':
        # Fix for Windows console encoding
        import codecs
        sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
        sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

def start_server_safe():
    """Start the server with Windows-specific error handling"""
    try:
        setup_windows_environment()
        
        # Import after environment setup
        from main import app, logger
        
        print("=" * 80)
        print("SOLYNTA BACKEND - WINDOWS COMPATIBLE STARTUP")
        print(f"Timestamp: {datetime.now().isoformat()}")
        print("=" * 80)
        
        # Get port from environment or default
        port = int(os.environ.get('PORT', 5000))
        
        # Use Flask's built-in development server for Windows compatibility
        print(f"Starting Flask development server on port {port}...")
        print("Health check: http://localhost:5000/api/health")
        print("Enhance content: http://localhost:5000/api/enhance-content")
        print("=" * 80)
        
        # Signal handler for graceful shutdown
        def signal_handler(sig, frame):
            print('\nShutting down server gracefully...')
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        if hasattr(signal, 'SIGTERM'):
            signal.signal(signal.SIGTERM, signal_handler)
        
        # Start server with Windows-compatible settings
        app.run(
            host="127.0.0.1",  # Use localhost instead of 0.0.0.0 for Windows
            port=port,
            debug=True,
            use_reloader=False,  # Disable reloader to avoid socket issues
            threaded=True,
            use_debugger=True
        )
        
    except KeyboardInterrupt:
        print("\nServer stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"STARTUP ERROR: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    start_server_safe()
