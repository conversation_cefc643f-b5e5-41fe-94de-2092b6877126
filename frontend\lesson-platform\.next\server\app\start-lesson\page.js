/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/start-lesson/page";
exports.ids = ["app/start-lesson/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fstart-lesson%2Fpage&page=%2Fstart-lesson%2Fpage&appPaths=%2Fstart-lesson%2Fpage&pagePath=private-next-app-dir%2Fstart-lesson%2Fpage.tsx&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fstart-lesson%2Fpage&page=%2Fstart-lesson%2Fpage&appPaths=%2Fstart-lesson%2Fpage&pagePath=private-next-app-dir%2Fstart-lesson%2Fpage.tsx&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/start-lesson/page.tsx */ \"(rsc)/./src/app/start-lesson/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'start-lesson',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/start-lesson/page\",\n        pathname: \"/start-lesson\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fstart-lesson%2Fpage&page=%2Fstart-lesson%2Fpage&appPaths=%2Fstart-lesson%2Fpage&pagePath=private-next-app-dir%2Fstart-lesson%2Fpage.tsx&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cclient-providers.tsx%22%2C%22ids%22%3A%5B%22ClientProviders%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cproviders%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cclient-providers.tsx%22%2C%22ids%22%3A%5B%22ClientProviders%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cproviders%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/client-providers.tsx */ \"(rsc)/./src/app/client-providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/providers/ThemeProvider.tsx */ \"(rsc)/./src/app/providers/ThemeProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3BjJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDRGVza3RvcCU1QyU1Q1NvbHludGFfV2Vic2l0ZSU1QyU1Q2Zyb250ZW5kJTVDJTVDbGVzc29uLXBsYXRmb3JtJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDY2xpZW50LXByb3ZpZGVycy50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJDbGllbnRQcm92aWRlcnMlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDcGMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNEZXNrdG9wJTVDJTVDU29seW50YV9XZWJzaXRlJTVDJTVDZnJvbnRlbmQlNUMlNUNsZXNzb24tcGxhdGZvcm0lNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNwYyU1QyU1Q09uZURyaXZlJTVDJTVDRGVza3RvcCU1QyU1Q0Rlc2t0b3AlNUMlNUNTb2x5bnRhX1dlYnNpdGUlNUMlNUNmcm9udGVuZCU1QyU1Q2xlc3Nvbi1wbGF0Zm9ybSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3Byb3ZpZGVycyU1QyU1Q1RoZW1lUHJvdmlkZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0tBQStMO0FBQy9MO0FBQ0Esc0xBQStMIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJDbGllbnRQcm92aWRlcnNcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxwY1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXERlc2t0b3BcXFxcU29seW50YV9XZWJzaXRlXFxcXGZyb250ZW5kXFxcXGxlc3Nvbi1wbGF0Zm9ybVxcXFxzcmNcXFxcYXBwXFxcXGNsaWVudC1wcm92aWRlcnMudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxccGNcXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxEZXNrdG9wXFxcXFNvbHludGFfV2Vic2l0ZVxcXFxmcm9udGVuZFxcXFxsZXNzb24tcGxhdGZvcm1cXFxcc3JjXFxcXGFwcFxcXFxwcm92aWRlcnNcXFxcVGhlbWVQcm92aWRlci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cclient-providers.tsx%22%2C%22ids%22%3A%5B%22ClientProviders%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cproviders%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cstart-lesson%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cstart-lesson%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/start-lesson/page.tsx */ \"(rsc)/./src/app/start-lesson/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3BjJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDRGVza3RvcCU1QyU1Q1NvbHludGFfV2Vic2l0ZSU1QyU1Q2Zyb250ZW5kJTVDJTVDbGVzc29uLXBsYXRmb3JtJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDc3RhcnQtbGVzc29uJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBLQUE0SiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxccGNcXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxEZXNrdG9wXFxcXFNvbHludGFfV2Vic2l0ZVxcXFxmcm9udGVuZFxcXFxsZXNzb24tcGxhdGZvcm1cXFxcc3JjXFxcXGFwcFxcXFxzdGFydC1sZXNzb25cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cstart-lesson%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccGNcXE9uZURyaXZlXFxEZXNrdG9wXFxEZXNrdG9wXFxTb2x5bnRhX1dlYnNpdGVcXGZyb250ZW5kXFxsZXNzb24tcGxhdGZvcm1cXHNyY1xcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/client-providers.tsx":
/*!**************************************!*\
  !*** ./src/app/client-providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ClientProviders: () => (/* binding */ ClientProviders)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ClientProviders = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ClientProviders() from the server but ClientProviders is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\Desktop\\Solynta_Website\\frontend\\lesson-platform\\src\\app\\client-providers.tsx",
"ClientProviders",
);

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"7af86ea72fdb\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHBjXFxPbmVEcml2ZVxcRGVza3RvcFxcRGVza3RvcFxcU29seW50YV9XZWJzaXRlXFxmcm9udGVuZFxcbGVzc29uLXBsYXRmb3JtXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI3YWY4NmVhNzJmZGJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _client_providers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./client-providers */ \"(rsc)/./src/app/client-providers.tsx\");\n/* harmony import */ var _providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./providers/ThemeProvider */ \"(rsc)/./src/app/providers/ThemeProvider.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: 'Solynta Academy',\n    description: 'Digital learning platform for Nigerian students'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_client_providers__WEBPACK_IMPORTED_MODULE_3__.ClientProviders, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/providers/ThemeProvider.tsx":
/*!*********************************************!*\
  !*** ./src/app/providers/ThemeProvider.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js\");\n/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call the default export of \\\"C:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\OneDrive\\\\\\\\Desktop\\\\\\\\Desktop\\\\\\\\Solynta_Website\\\\\\\\frontend\\\\\\\\lesson-platform\\\\\\\\src\\\\\\\\app\\\\\\\\providers\\\\\\\\ThemeProvider.tsx\\\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers\\\\ThemeProvider.tsx\",\n\"default\",\n));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/providers/ThemeProvider.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/start-lesson/page.tsx":
/*!***************************************!*\
  !*** ./src/app/start-lesson/page.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\Desktop\\Solynta_Website\\frontend\\lesson-platform\\src\\app\\start-lesson\\page.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cclient-providers.tsx%22%2C%22ids%22%3A%5B%22ClientProviders%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cproviders%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cclient-providers.tsx%22%2C%22ids%22%3A%5B%22ClientProviders%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cproviders%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/client-providers.tsx */ \"(ssr)/./src/app/client-providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/providers/ThemeProvider.tsx */ \"(ssr)/./src/app/providers/ThemeProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3BjJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDRGVza3RvcCU1QyU1Q1NvbHludGFfV2Vic2l0ZSU1QyU1Q2Zyb250ZW5kJTVDJTVDbGVzc29uLXBsYXRmb3JtJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDY2xpZW50LXByb3ZpZGVycy50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJDbGllbnRQcm92aWRlcnMlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDcGMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNEZXNrdG9wJTVDJTVDU29seW50YV9XZWJzaXRlJTVDJTVDZnJvbnRlbmQlNUMlNUNsZXNzb24tcGxhdGZvcm0lNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNwYyU1QyU1Q09uZURyaXZlJTVDJTVDRGVza3RvcCU1QyU1Q0Rlc2t0b3AlNUMlNUNTb2x5bnRhX1dlYnNpdGUlNUMlNUNmcm9udGVuZCU1QyU1Q2xlc3Nvbi1wbGF0Zm9ybSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3Byb3ZpZGVycyU1QyU1Q1RoZW1lUHJvdmlkZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0tBQStMO0FBQy9MO0FBQ0Esc0xBQStMIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJDbGllbnRQcm92aWRlcnNcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxwY1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXERlc2t0b3BcXFxcU29seW50YV9XZWJzaXRlXFxcXGZyb250ZW5kXFxcXGxlc3Nvbi1wbGF0Zm9ybVxcXFxzcmNcXFxcYXBwXFxcXGNsaWVudC1wcm92aWRlcnMudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxccGNcXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxEZXNrdG9wXFxcXFNvbHludGFfV2Vic2l0ZVxcXFxmcm9udGVuZFxcXFxsZXNzb24tcGxhdGZvcm1cXFxcc3JjXFxcXGFwcFxcXFxwcm92aWRlcnNcXFxcVGhlbWVQcm92aWRlci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cclient-providers.tsx%22%2C%22ids%22%3A%5B%22ClientProviders%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cproviders%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cstart-lesson%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cstart-lesson%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/start-lesson/page.tsx */ \"(ssr)/./src/app/start-lesson/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3BjJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDRGVza3RvcCU1QyU1Q1NvbHludGFfV2Vic2l0ZSU1QyU1Q2Zyb250ZW5kJTVDJTVDbGVzc29uLXBsYXRmb3JtJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDc3RhcnQtbGVzc29uJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBLQUE0SiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxccGNcXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxEZXNrdG9wXFxcXFNvbHludGFfV2Vic2l0ZVxcXFxmcm9udGVuZFxcXFxsZXNzb24tcGxhdGZvcm1cXFxcc3JjXFxcXGFwcFxcXFxzdGFydC1sZXNzb25cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cstart-lesson%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/client-providers.tsx":
/*!**************************************!*\
  !*** ./src/app/client-providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClientProviders: () => (/* binding */ ClientProviders)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./providers */ \"(ssr)/./src/app/providers.tsx\");\n/* __next_internal_client_entry_do_not_use__ ClientProviders auto */ \n\n // Handles all providers including SessionProvider\n// NOTE: Removed duplicate imports to avoid conflicts\nfunction ClientProviders({ children }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ClientProviders.useEffect\": ()=>{\n            const handleRejection = {\n                \"ClientProviders.useEffect.handleRejection\": (event)=>{\n                    console.error(\"!!!! GLOBAL UNHANDLED REJECTION !!!!\", event.reason);\n                // You could potentially show a generic error toast here\n                }\n            }[\"ClientProviders.useEffect.handleRejection\"];\n            window.addEventListener('unhandledrejection', handleRejection);\n            return ({\n                \"ClientProviders.useEffect\": ()=>{\n                    window.removeEventListener('unhandledrejection', handleRejection);\n                }\n            })[\"ClientProviders.useEffect\"];\n        }\n    }[\"ClientProviders.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-screen\",\n            children: \"Loading application components...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\client-providers.tsx\",\n            lineNumber: 22,\n            columnNumber: 25\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\client-providers.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\client-providers.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/client-providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _nextui_org_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @nextui-org/react */ \"(ssr)/./node_modules/@nextui-org/system/dist/chunk-MNMJVVXA.mjs\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var _providers_AuthProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./providers/AuthProvider */ \"(ssr)/./src/app/providers/AuthProvider.tsx\");\n/* harmony import */ var _providers_SessionProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./providers/SessionProvider */ \"(ssr)/./src/app/providers/SessionProvider.tsx\");\n/* harmony import */ var _providers_ClientToastWrapper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./providers/ClientToastWrapper */ \"(ssr)/./src/app/providers/ClientToastWrapper.tsx\");\n/* harmony import */ var _hooks_useSessionSimple__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useSessionSimple */ \"(ssr)/./src/hooks/useSessionSimple.tsx\");\n/* harmony import */ var _lib_authService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/authService */ \"(ssr)/./src/lib/authService.ts\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\n\n// Import your AuthProvider etc.\n\n // NextAuth wrapper\n\n\n // Import the real auth function\n// Simplified SessionProvider that provides the interface expected by useSession\nfunction SimpleSessionProvider({ children }) {\n    const [backendSessionId, setBackendSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isReady, setIsReady] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const value = {\n        backendSessionId,\n        user,\n        setUserSession: setUser,\n        setBackendSessionId,\n        clearSession: ()=>{\n            setUser(null);\n            setBackendSessionId(null);\n        },\n        isReady,\n        isLoading,\n        getAuthHeaders: ()=>(0,_lib_authService__WEBPACK_IMPORTED_MODULE_7__.getAuthHeaders)(backendSessionId),\n        userRole: user?.role || null\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_hooks_useSessionSimple__WEBPACK_IMPORTED_MODULE_6__.SessionContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_SessionProvider__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleSessionProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                    attribute: \"class\",\n                    defaultTheme: \"light\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_nextui_org_react__WEBPACK_IMPORTED_MODULE_8__.NextUIProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_ClientToastWrapper__WEBPACK_IMPORTED_MODULE_5__.ClientToastWrapper, {\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers.tsx\",\n                lineNumber: 46,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers.tsx\",\n            lineNumber: 45,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/providers/AuthProvider.tsx":
/*!********************************************!*\
  !*** ./src/app/providers/AuthProvider.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_6__);\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    isAuthenticated: false,\n    studentSession: null,\n    userRole: null,\n    manualSyncWithLocalStorage: ()=>{},\n    user: null,\n    userData: null,\n    childrenData: null,\n    loading: true,\n    error: null,\n    refreshUserData: async ()=>{},\n    refreshChildrenData: async ()=>{},\n    handleLoginSuccess: async ()=>{},\n    logout: async ()=>{}\n});\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [childrenData, setChildrenData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [studentSession, setStudentSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userRole, setUserRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_6__.useSession)(); // session will be of type Session | null\n    console.log('AuthProvider: Initial state', {\n        loading,\n        isAuthenticated,\n        userRole,\n        session\n    });\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const manualSyncWithLocalStorage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[manualSyncWithLocalStorage]\": ()=>{\n            const storedUserData = localStorage.getItem('user_data');\n            if (storedUserData) {\n                setUserData(JSON.parse(storedUserData));\n            }\n            const storedChildrenData = localStorage.getItem('children_data');\n            if (storedChildrenData) {\n                setChildrenData(JSON.parse(storedChildrenData));\n            }\n        }\n    }[\"AuthProvider.useCallback[manualSyncWithLocalStorage]\"], []);\n    const refreshUserData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[refreshUserData]\": async ()=>{\n            if (!user?.uid) {\n                console.log(\"AuthProvider: refreshUserData - No user.uid, exiting.\");\n                setUserData(null); // Ensure data is cleared if no user\n                setUserRole(null);\n                return;\n            }\n            console.log(`AuthProvider: Refreshing user data for UID: ${user.uid}`);\n            setError(null); // Clear previous errors\n            // Determine the most likely role based on login method and localStorage hints\n            // This is an initial guess before confirming with fetched data\n            let likelyRole = localStorage.getItem('user_role') || null;\n            const loginMethodHint = user.providerData?.[0]?.providerId; // 'password', 'google.com', 'custom'\n            console.log(`AuthProvider: refreshUserData - Login method hint: ${loginMethodHint}, localStorage role hint: ${likelyRole}`);\n            if (!likelyRole) {\n                if (loginMethodHint === 'custom') {\n                    likelyRole = 'student'; // Custom tokens are used for student logins in your setup\n                    console.log(\"AuthProvider: refreshUserData - Guessed role 'student' based on custom token login.\");\n                } else if (loginMethodHint === 'password' || loginMethodHint === 'google.com') {\n                    likelyRole = 'parent'; // Email/Google logins are parents in your setup\n                    console.log(\"AuthProvider: refreshUserData - Guessed role 'parent' based on email/Google login.\");\n                } else {\n                    console.log(\"AuthProvider: refreshUserData - Could not reliably guess initial role.\");\n                    // Proceed cautiously, maybe try student first as it seems more common? Or try both carefully.\n                    // Let's try student first for safety in this scenario.\n                    likelyRole = 'student';\n                }\n            }\n            try {\n                // --- Logic based on likely role ---\n                let roleConfirmed = false;\n                // Try fetching data based on the likely role first\n                if (likelyRole === 'parent') {\n                    console.log(\"AuthProvider: refreshUserData - Prioritizing parent fetch.\");\n                    // 1. Try Parent Dashboard Endpoint\n                    try {\n                        const dashboardResponse = await fetch(`/api/parent/dashboard?parentId=${user.uid}`);\n                        if (dashboardResponse.ok) {\n                            const dashboardData = await dashboardResponse.json();\n                            console.log(\"AuthProvider: Parent dashboard response:\", dashboardData);\n                            if (dashboardData.success && dashboardData.data?.parent) {\n                                console.log(\"AuthProvider: Parent data found via dashboard.\");\n                                const parentData = {\n                                    ...dashboardData.data.parent,\n                                    uid: user.uid,\n                                    role: 'parent'\n                                };\n                                setUserData(parentData);\n                                localStorage.setItem('user_data', JSON.stringify(parentData));\n                                setUserRole('parent'); // Confirm role\n                                console.log(\"AuthProvider: Set user data/role to parent (dashboard).\");\n                                roleConfirmed = true;\n                                return; // Success\n                            } else {\n                                console.log(\"AuthProvider: Parent dashboard endpoint didn't provide valid parent data. Will try token verification.\");\n                            }\n                        } else {\n                            console.warn(`AuthProvider: Parent dashboard fetch failed (${dashboardResponse.status}). Will try token verification.`);\n                        }\n                    } catch (e) {\n                        console.error(\"AuthProvider: Error with parent dashboard fetch:\", e);\n                    }\n                    // 2. Fallback: Verify Firebase Token (for parents)\n                    if (!roleConfirmed) {\n                        try {\n                            console.log(\"AuthProvider: Attempting parent fallback: verify-firebase-token\");\n                            const token = await user.getIdToken(true);\n                            const verifyResponse = await fetch('/api/auth/verify-firebase-token', {\n                                method: 'POST',\n                                headers: {\n                                    'Content-Type': 'application/json'\n                                },\n                                body: JSON.stringify({\n                                    token\n                                })\n                            });\n                            if (verifyResponse.ok) {\n                                const verifyData = await verifyResponse.json();\n                                console.log(\"AuthProvider: verify-firebase-token response:\", verifyData);\n                                if (verifyData.success && verifyData.user) {\n                                    console.log(\"AuthProvider: Parent data found via token verification.\");\n                                    const parentData = {\n                                        ...verifyData.user,\n                                        uid: user.uid,\n                                        role: 'parent'\n                                    };\n                                    setUserData(parentData);\n                                    localStorage.setItem('user_data', JSON.stringify(parentData));\n                                    setUserRole('parent'); // Confirm role\n                                    console.log(\"AuthProvider: Set user data/role to parent (token verify).\");\n                                    roleConfirmed = true;\n                                    return; // Success\n                                } else {\n                                    console.log(\"AuthProvider: Token verification failed to provide parent data.\");\n                                }\n                            } else {\n                                console.warn(`AuthProvider: verify-firebase-token fetch failed (${verifyResponse.status}).`);\n                            }\n                        } catch (e) {\n                            console.error(\"AuthProvider: Error with token verification:\", e);\n                        }\n                    }\n                }\n                // If parent fetches failed or role is student, try student fetch\n                if (!roleConfirmed) {\n                    console.log(\"AuthProvider: refreshUserData - Trying student fetch.\");\n                    try {\n                        const studentIdForFetch = localStorage.getItem('student_id') || user.uid; // Use consistent variable\n                        console.log(`AuthProvider: Fetching student data with studentId: ${studentIdForFetch}`);\n                        const studentResponse = await fetch(`/api/student-data?studentId=${studentIdForFetch}`);\n                        // --- ADD ROBUST RESPONSE HANDLING ---\n                        if (!studentResponse.ok) {\n                            const errorText = await studentResponse.text(); // Get raw error text\n                            console.error(`AuthProvider: API call to /student-data failed with status ${studentResponse.status}. Response: ${errorText}`);\n                            // Try to parse as JSON only if content-type suggests it, otherwise use text\n                            let backendError = `Failed to fetch student data (status: ${studentResponse.status})`;\n                            try {\n                                if (studentResponse.headers.get(\"content-type\")?.includes(\"application/json\")) {\n                                    const errorJson = JSON.parse(errorText); // This might fail if errorText is HTML\n                                    backendError = errorJson.error || errorJson.message || backendError;\n                                } else if (errorText.length > 0 && errorText.length < 300) {\n                                    backendError = errorText;\n                                }\n                            } catch (parseError) {\n                                console.warn(\"AuthProvider: Could not parse error response from /student-data as JSON. Raw text was:\", errorText);\n                            }\n                            throw new Error(backendError);\n                        }\n                        // --- END ROBUST RESPONSE HANDLING ---\n                        // If response.ok is true, then try to parse JSON\n                        const studentData = await studentResponse.json(); // This is where the SyntaxError was happening\n                        console.log(\"AuthProvider: Student data API response received:\", studentData);\n                        if (studentData.success && studentData.student) {\n                            const studentInfo = studentData.student;\n                            console.log(\"AuthProvider: Student data found and valid.\");\n                            // ... (rest of your logic for successful student data) ...\n                            // Add code to set claims if not already set\n                            try {\n                                const idTokenResult = await user.getIdTokenResult();\n                                const hasCorrectClaims = idTokenResult.claims.role === 'student' && idTokenResult.claims.student_id === studentInfo.id;\n                                if (!hasCorrectClaims) {\n                                    console.log(\"AuthProvider: Student claims missing or incorrect, setting them now\");\n                                    const idToken = await user.getIdToken();\n                                    const claimResponse = await fetch('/api/auth/set-student-claims', {\n                                        method: 'POST',\n                                        headers: {\n                                            'Content-Type': 'application/json',\n                                            'Authorization': `Bearer ${idToken}`\n                                        },\n                                        body: JSON.stringify({\n                                            studentId: studentInfo.id\n                                        })\n                                    });\n                                    if (claimResponse.ok) {\n                                        console.log(\"AuthProvider: Student claims set successfully\");\n                                        // Force token refresh to get the new claims\n                                        await user.getIdToken(true);\n                                    } else {\n                                        console.error(\"AuthProvider: Failed to set student claims:\", await claimResponse.json());\n                                    }\n                                }\n                            } catch (e) {\n                                console.error(\"AuthProvider: Error checking/setting student claims:\", e);\n                            }\n                            const studentUserData = {\n                                ...studentInfo,\n                                role: 'student',\n                                enrollments: studentData.enrollments || [],\n                                timetable: studentData.timetable || null\n                            };\n                            const studentId = studentInfo.id;\n                            setUserData(studentUserData);\n                            localStorage.setItem('user_data', JSON.stringify(studentUserData));\n                            setUserRole('student');\n                            setStudentSession(studentId);\n                            localStorage.setItem('student_id', studentId);\n                            if (localStorage.getItem('current_session') === studentId) {\n                                localStorage.removeItem('current_session');\n                            }\n                            localStorage.setItem('user_role', 'student');\n                            console.log(`AuthProvider: Set user data/role to student. Student ID: ${studentId}`);\n                            roleConfirmed = true; // Make sure to set this on success\n                            return; // Exit refreshUserData successfully\n                        } else {\n                            console.warn(`AuthProvider: Student data fetch was 'ok' but API reported not success or missing data. Message: ${studentData?.error || studentData?.message}`);\n                            throw new Error(studentData?.error || studentData?.message || 'Student data fetch failed (API level).');\n                        }\n                    } catch (e) {\n                        console.error(\"AuthProvider: Error block for student data fetch:\", e);\n                        // If we are here, it means student fetch failed.\n                        // The specific error (e.g., SyntaxError, or the error thrown from !response.ok) will be 'e'.\n                        // You might want to set a specific error or attempt recovery.\n                        // The outer catch block will handle setting the general error state.\n                        throw e; // Re-throw to be handled by the main catch in refreshUserData\n                    }\n                }\n                // If we get here without returning, no role was confirmed\n                if (!roleConfirmed) {\n                    throw new Error('Could not determine user role. Please try logging in again.');\n                }\n            } catch (err) {\n                console.error('AuthProvider: Error during refreshUserData:', err);\n                // Provide a specific error message based on the type of error if possible\n                let specificErrorMessage = 'Failed to refresh user data. Please try logging in again.';\n                if (err instanceof Error) {\n                    specificErrorMessage = `Failed to refresh user data: ${err.message}`;\n                    if (err.message.includes('Failed to fetch valid student data')) {\n                        // Attempt recovery logic as you have\n                        const studentIdHint = localStorage.getItem('student_id') || localStorage.getItem('current_session');\n                        if (studentIdHint) {\n                            // your recovery logic ...\n                            console.log(`AuthProvider: Recovering with minimal student data using ID: ${studentIdHint}`);\n                            const minimalStudentData = {\n                                id: studentIdHint,\n                                uid: user.uid,\n                                role: 'student',\n                                name: localStorage.getItem('student_name') || 'Student',\n                                recoveryMode: true\n                            };\n                            setUserData(minimalStudentData);\n                            setUserRole('student');\n                            setStudentSession(studentIdHint);\n                            localStorage.setItem('user_data', JSON.stringify(minimalStudentData));\n                            // If recovery is successful, set state and RETURN here to avoid setting generic error.\n                            console.log(\"AuthProvider: Recovered with minimal student data.\");\n                            setError(null); // Clear any previous errors if recovery was successful\n                            return;\n                        } else {\n                            specificErrorMessage = \"Could not retrieve critical student information. Please re-login.\";\n                        }\n                    } else if (err.message.includes('Could not determine user role')) {\n                        specificErrorMessage = \"Could not confirm your user role. Please try logging in again.\";\n                    }\n                }\n                setError(specificErrorMessage); // Set a user-friendly error message\n                // Clear sensitive/stale data on critical refresh failure\n                setUserData(null);\n                setUserRole(null);\n                setStudentSession(null);\n                localStorage.removeItem('user_data');\n                localStorage.removeItem('user_role');\n                localStorage.removeItem('student_id');\n                localStorage.removeItem('current_session'); // and other relevant keys\n                setIsAuthenticated(false); // Consider if a refresh failure means unauthenticated\n            // setLoading(false); // Ensure loading is false if refresh fails - This is handled by onAuthStateChanged\n            // Consider a more drastic action like redirecting to login if refresh fails consistently\n            // router.push('/login?error=session_refresh_failed');\n            }\n        // setLoading(false); // Ensure loading is set to false at the end of the function\n        // This is now handled by onAuthStateChanged's setLoading(false)\n        }\n    }[\"AuthProvider.useCallback[refreshUserData]\"], [\n        user\n    ]);\n    const refreshChildrenData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[refreshChildrenData]\": async ()=>{\n            if (!user?.uid) return;\n            try {\n                // First try the parent dashboard endpoint which includes all child data\n                const dashboardResponse = await fetch(`/api/parent/dashboard?parentId=${user.uid}`);\n                const dashboardData = await dashboardResponse.json();\n                if (dashboardData.success) {\n                    const children = dashboardData.data.children || [];\n                    setChildrenData(children);\n                    localStorage.setItem('children_data', JSON.stringify(children));\n                    // Also update Firestore to ensure consistency\n                    if (children.length > 0) {\n                        const parentRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_5__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, 'users', user.uid);\n                        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_5__.updateDoc)(parentRef, {\n                            children: children.map({\n                                \"AuthProvider.useCallback[refreshChildrenData]\": (child)=>child.id\n                            }[\"AuthProvider.useCallback[refreshChildrenData]\"])\n                        });\n                    }\n                    return;\n                }\n                // Fallback to get-children endpoint if dashboard fails\n                const childrenResponse = await fetch(`/api/parent/get-children?parentId=${user.uid}`);\n                const childrenData = await childrenResponse.json();\n                if (childrenData.success) {\n                    const children = childrenData.children || [];\n                    setChildrenData(children);\n                    localStorage.setItem('children_data', JSON.stringify(children));\n                    // Also update Firestore to ensure consistency\n                    if (children.length > 0) {\n                        const parentRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_5__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, 'users', user.uid);\n                        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_5__.updateDoc)(parentRef, {\n                            children: children.map({\n                                \"AuthProvider.useCallback[refreshChildrenData]\": (child)=>child.id\n                            }[\"AuthProvider.useCallback[refreshChildrenData]\"])\n                        });\n                    }\n                } else {\n                    setError(childrenData.error || 'Failed to fetch children data');\n                }\n            } catch (err) {\n                console.error('Error fetching children data:', err);\n                setError('Failed to fetch children data');\n            }\n        }\n    }[\"AuthProvider.useCallback[refreshChildrenData]\"], [\n        user\n    ]);\n    const handleLoginSuccess = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[handleLoginSuccess]\": async (customToken, studentIdHint)=>{\n            // *** REMOVE ALERT ***\n            // alert(\"AuthProvider: handleLoginSuccess CALLED!\"); \n            // *** END REMOVE ALERT ***\n            console.log(\"AuthProvider: handleLoginSuccess called.\"); // Log start\n            setLoading(true);\n            setError(null);\n            try {\n                // Validate token format\n                if (!customToken || typeof customToken !== 'string') {\n                    throw new Error('Invalid token format: Token is missing or not a string.');\n                }\n                if (!customToken.includes('.')) {\n                    throw new Error('Invalid token format: Token does not appear to be a valid JWT.');\n                }\n                if (customToken.length < 50) {\n                    throw new Error('Invalid token format: Token is too short to be valid.');\n                } // Sign in using the custom token with timeout\n                console.log(\"AuthProvider: Calling signInWithCustomToken...\"); // Log before call\n                const signInPromise = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signInWithCustomToken)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth, customToken);\n                const timeoutPromise = new Promise({\n                    \"AuthProvider.useCallback[handleLoginSuccess]\": (_, reject)=>setTimeout({\n                            \"AuthProvider.useCallback[handleLoginSuccess]\": ()=>reject(new Error('Sign-in timeout: The operation took too long to complete'))\n                        }[\"AuthProvider.useCallback[handleLoginSuccess]\"], 30000)\n                }[\"AuthProvider.useCallback[handleLoginSuccess]\"]);\n                const userCredential = await Promise.race([\n                    signInPromise,\n                    timeoutPromise\n                ]);\n                const loggedInUser = userCredential.user;\n                console.log(\"AuthProvider: signInWithCustomToken successful. User:\", loggedInUser.uid);\n                // *** ADD LOGGING: Check auth.currentUser immediately after sign-in ***\n                console.log(`AuthProvider: auth.currentUser?.uid immediately after signInWithCustomToken: ${_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth.currentUser?.uid}`);\n                // Force token refresh to get custom claims\n                console.log(\"AuthProvider: Forcing token refresh (getIdToken(true))...\"); // Log before refresh\n                await loggedInUser.getIdToken(true);\n                console.log(\"AuthProvider: Token refresh complete.\"); // Log after refresh\n                // *** ADD LOGGING: Check auth.currentUser after token refresh ***\n                console.log(`AuthProvider: auth.currentUser?.uid after token refresh: ${_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth.currentUser?.uid}`);\n                // Use provided studentId or look in localStorage - BUT DO NOT SET AS SESSION ID\n                const studentId = studentIdHint || localStorage.getItem('student_id');\n                if (studentId) {\n                    // Only store the student ID, not as a session ID\n                    localStorage.setItem('student_id', studentId);\n                    // Remove any existing incorrect session ID that might be the student ID\n                    if (localStorage.getItem('current_session') === studentId) {\n                        console.log(\"AuthProvider: Removing incorrect session ID (was set to student ID)\");\n                        localStorage.removeItem('current_session');\n                    }\n                    // Set claims via API immediately after login\n                    try {\n                        const idToken = await loggedInUser.getIdToken();\n                        const claimResponse = await fetch('/api/auth/set-student-claims', {\n                            method: 'POST',\n                            headers: {\n                                'Content-Type': 'application/json',\n                                'Authorization': `Bearer ${idToken}`\n                            },\n                            body: JSON.stringify({\n                                studentId: studentId\n                            })\n                        });\n                        if (claimResponse.ok) {\n                            console.log(\"AuthProvider: Student claims set successfully\");\n                            // Force another token refresh to get the new claims\n                            await loggedInUser.getIdToken(true);\n                        } else {\n                            console.error(\"AuthProvider: Failed to set student claims:\", await claimResponse.json());\n                        }\n                    } catch (e) {\n                        console.error(\"AuthProvider: Error setting student claims:\", e);\n                    }\n                }\n                // Clear any parent-related flags first\n                localStorage.removeItem('parent_id');\n                localStorage.removeItem('parent_name');\n                localStorage.removeItem('parent_role');\n                localStorage.removeItem('viewing_as_child');\n                localStorage.removeItem('is_parent');\n                // Set user role and auth state\n                localStorage.setItem('user_role', 'student');\n                setUserRole('student');\n                setIsAuthenticated(true);\n                // Clear the progress flag after successful sign-in\n                localStorage.removeItem('login_in_progress');\n                console.log(\"AuthProvider: Login successful, cleared login_in_progress flag.\");\n            } catch (err) {\n                console.error(\"AuthProvider: Error signing in with custom token:\", err);\n                let errorMessage = \"Failed to sign in with custom token.\";\n                if (err instanceof Error) {\n                    if (err.message.includes('auth/quota-exceeded')) {\n                        errorMessage = \"Authentication quota exceeded. Please try again later.\";\n                    } else if (err.message.includes('auth/invalid-custom-token')) {\n                        errorMessage = \"Invalid authentication token. Please try logging in again.\";\n                    } else if (err.message.includes('auth/custom-token-mismatch')) {\n                        errorMessage = \"Authentication token mismatch. Please try logging in again.\";\n                    } else if (err.message.includes('auth/network-request-failed')) {\n                        errorMessage = \"Network error. Please check your internet connection and try again.\";\n                    } else {\n                        errorMessage = err.message;\n                    }\n                }\n                setError(errorMessage);\n                setUser(null);\n                setUserData(null);\n                setChildrenData(null);\n                setUserRole(null);\n                setStudentSession(null);\n                // Clear flags/storage on error\n                localStorage.removeItem('auth_token');\n                localStorage.removeItem('student_id');\n                localStorage.removeItem('user_role');\n                localStorage.removeItem('current_session');\n                localStorage.removeItem('login_in_progress');\n                // Re-throw the error so it can be caught by the caller\n                throw err;\n            } finally{\n                // setLoading(false); // Loading should be set to false by the onAuthStateChanged listener handling\n                console.log(\"AuthProvider: handleLoginSuccess finally block.\"); // Log finally\n            }\n        }\n    }[\"AuthProvider.useCallback[handleLoginSuccess]\"], []); // Removed auth dependency as it's globally available\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            console.log(\"AuthProvider: Setting up onAuthStateChanged listener.\"); // Log listener setup\n            const unsubscribe = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.onAuthStateChanged)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth, {\n                \"AuthProvider.useEffect.unsubscribe\": async (user)=>{\n                    // *** ADD DETAILED LOGGING for onAuthStateChanged ***\n                    const timestamp = new Date().toISOString();\n                    console.log(`AuthProvider: onAuthStateChanged fired at ${timestamp}. User object:`, user ? {\n                        uid: user.uid,\n                        email: user.email\n                    } : null);\n                    setUser(user); // Update the user state\n                    if (user) {\n                        console.log(`AuthProvider: onAuthStateChanged - User is present (UID: ${user.uid}).`);\n                        // Try to refresh token to get latest claims\n                        try {\n                            console.log(\"AuthProvider: onAuthStateChanged - Refreshing token...\");\n                            await user.getIdToken(true);\n                            console.log(\"AuthProvider: onAuthStateChanged - Token refreshed.\");\n                            // ... check claims ...\n                            const idTokenResult = await user.getIdTokenResult();\n                            console.log(\"AuthProvider: onAuthStateChanged - Token claims:\", idTokenResult.claims);\n                        // ... set role/session from claims ...\n                        } catch (e) {\n                            console.error(\"AuthProvider: onAuthStateChanged - Error refreshing token:\", e);\n                        }\n                        // Try to load from localStorage first for faster initial render\n                        const storedUserData = localStorage.getItem('user_data');\n                        if (storedUserData) {\n                            setUserData(JSON.parse(storedUserData));\n                        }\n                        const storedChildrenData = localStorage.getItem('children_data');\n                        if (storedChildrenData) {\n                            setChildrenData(JSON.parse(storedChildrenData));\n                        }\n                        // Then refresh from server\n                        console.log(\"AuthProvider: onAuthStateChanged - Calling refreshUserData...\");\n                        await refreshUserData();\n                        console.log(\"AuthProvider: onAuthStateChanged - refreshUserData complete.\");\n                        setIsAuthenticated(true);\n                        console.log(\"AuthProvider: onAuthStateChanged - Set isAuthenticated = true.\");\n                        // Only refresh children data if user is a parent\n                        if (userData?.role === 'parent' || JSON.parse(storedUserData || '{}')?.role === 'parent') {\n                            await refreshChildrenData();\n                        }\n                    } else {\n                        console.log(\"AuthProvider: onAuthStateChanged - User is null.\");\n                        // ... clear user state and localStorage ...\n                        setUserData(null);\n                        setChildrenData(null);\n                        setUserRole(null);\n                        setStudentSession(null);\n                        setIsAuthenticated(false);\n                        localStorage.removeItem('user_data');\n                        localStorage.removeItem('children_data');\n                        localStorage.removeItem('CURRENT_SESSION_KEY'); // Ensure correct key if used elsewhere\n                        localStorage.removeItem('user_role');\n                        localStorage.removeItem('student_id');\n                        console.log(\"AuthProvider: onAuthStateChanged - Cleared state and localStorage.\");\n                    }\n                    console.log(`AuthProvider: onAuthStateChanged - Setting loading = false at ${new Date().toISOString()}.`);\n                    setLoading(false);\n                }\n            }[\"AuthProvider.useEffect.unsubscribe\"]);\n            // Cleanup function\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    console.log(\"AuthProvider: Cleaning up onAuthStateChanged listener.\"); // Log cleanup\n                    unsubscribe();\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        refreshUserData,\n        refreshChildrenData\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Skip if still loading\n            if (loading) return;\n            // Handle user authentication state\n            if (user) {\n                // If user is authenticated but no role is set, attempt to determine role\n                if (!userRole) {\n                    console.log(\"AuthProvider: User authenticated but no role set. Attempting to determine role...\");\n                    // Check for role in localStorage first\n                    const storedRole = localStorage.getItem('user_role');\n                    if (storedRole) {\n                        console.log(`AuthProvider: Found role in localStorage: ${storedRole}`);\n                        setUserRole(storedRole);\n                        // If this is a student, check for student ID\n                        if (storedRole === 'student') {\n                            const storedStudentId = localStorage.getItem('student_id') || localStorage.getItem('current_session');\n                            if (storedStudentId) {\n                                console.log(`AuthProvider: Found student ID in localStorage: ${storedStudentId}`);\n                                setStudentSession(storedStudentId);\n                            }\n                        }\n                    } else {\n                        // Check for parent indicators\n                        const isParent = localStorage.getItem('parent_id') || localStorage.getItem('parent_name');\n                        // Check for temporary student session (parent viewing student dashboard)\n                        const tempStudentSession = localStorage.getItem('temp_student_session');\n                        if (isParent) {\n                            console.log(\"AuthProvider: User appears to be a parent based on localStorage\");\n                            setUserRole('parent');\n                            // If parent is viewing a student dashboard, set the student session\n                            if (tempStudentSession) {\n                                console.log(`AuthProvider: Parent viewing student dashboard for: ${tempStudentSession}`);\n                                setStudentSession(tempStudentSession);\n                            }\n                        } else {\n                            // Default to student role if no other indicators\n                            console.log(\"AuthProvider: No role indicators found, defaulting to student\");\n                            setUserRole('student');\n                            // Try to find student ID\n                            const storedStudentId = localStorage.getItem('student_id') || localStorage.getItem('current_session');\n                            if (storedStudentId) {\n                                console.log(`AuthProvider: Found student ID: ${storedStudentId}`);\n                                setStudentSession(storedStudentId);\n                            } else {\n                                console.log(`AuthProvider: No student ID found, using UID: ${user.uid}`);\n                                setStudentSession(user.uid);\n                                localStorage.setItem('student_id', user.uid);\n                                localStorage.setItem('current_session', user.uid);\n                            }\n                        }\n                    }\n                }\n            }\n        }\n    }[\"AuthProvider.useEffect\"], [\n        loading,\n        user,\n        userRole,\n        setUserRole,\n        setStudentSession\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Skip if still loading auth state OR NextAuth session is loading\n            if (loading || status === 'loading') {\n                console.log(\"AuthProvider: Nav Effect - Waiting for loading state or session status...\");\n                return;\n            }\n            // Determine authentication status based on Firebase OR NextAuth\n            const authenticated = !!user && !!user.uid || status === 'authenticated';\n            // Determine effective role and session for this effect run, prioritizing session if state is lagging\n            let effectiveRole = userRole;\n            let effectiveStudentSession = studentSession;\n            // Use SessionUser type for session check\n            const sessionUser = session?.user;\n            if (authenticated && !effectiveRole && status === 'authenticated' && sessionUser?.role) {\n                console.log(\"AuthProvider: Nav Effect - Using role directly from NextAuth session as state is not yet updated.\");\n                effectiveRole = sessionUser.role;\n            }\n            if (effectiveRole === 'student' && !effectiveStudentSession && status === 'authenticated' && sessionUser?.id) {\n                console.log(\"AuthProvider: Nav Effect - Using student ID directly from NextAuth session as state is not yet updated.\");\n                effectiveStudentSession = sessionUser.id;\n            }\n            console.log(\"AuthProvider: Nav Effect - Running Checks\", {\n                timestamp: new Date().toISOString(),\n                loading,\n                status,\n                authenticated,\n                user: user ? {\n                    uid: user.uid,\n                    email: user.email,\n                    provider: user.providerData?.[0]?.providerId\n                } : null,\n                sessionUser,\n                // Log component state\n                stateUserRole: userRole,\n                stateStudentSession: studentSession,\n                // Log effective values used for logic\n                effectiveRole,\n                effectiveStudentSession,\n                userDataExists: !!userData,\n                currentPath:  false ? 0 : ''\n            });\n            try {\n                // Get current path and query parameters safely\n                const currentPath =  false ? 0 : '';\n                const searchParams =  false ? 0 : new URLSearchParams();\n                const returnTo = searchParams.get('returnTo');\n                const resetParam = searchParams.get('reset') === 'true';\n                // --- DETAILED LOGGING ---\n                // console.log(\"AuthProvider: Nav Effect - Running Checks\", { // Moved up and enhanced\n                //   timestamp: new Date().toISOString(),\n                //   loading,\n                //   status,\n                //   authenticated,\n                //   user: user ? { uid: user.uid, email: user.email, provider: user.providerData?.[0]?.providerId } : null,\n                //   sessionUser,\n                //   stateUserRole: userRole, // Log state value\n                //   stateStudentSession: studentSession, // Log state value\n                //   effectiveRole, // Log derived value\n                //   effectiveStudentSession, // Log derived value\n                //   userDataExists: !!userData,\n                //   currentPath,\n                //   returnTo,\n                //   resetParam\n                // });\n                // --- END LOGGING ---\n                // Handle reset parameter first\n                if (resetParam && currentPath !== '/login') {\n                    console.log(\"AuthProvider: Nav Effect - Reset parameter detected, redirecting to /login?reset=true\");\n                    // Clear local state immediately\n                    setUser(null);\n                    setUserData(null);\n                    setChildrenData(null);\n                    setUserRole(null);\n                    setStudentSession(null);\n                    // Clear storage\n                    localStorage.clear();\n                    sessionStorage.clear();\n                    // Use replace to avoid adding reset=true to history\n                    router.replace('/login?reset=true');\n                    return;\n                }\n                // Use the 'authenticated' variable derived above\n                if (authenticated) {\n                    // User is authenticated (either Firebase or NextAuth)\n                    // If role or necessary session info is missing EVEN after checking session, log and wait\n                    // Use the effectiveRole and effectiveStudentSession derived above\n                    if (!effectiveRole) {\n                        console.log(\"AuthProvider: Nav Effect - User authenticated but effective role could not be determined. Waiting.\");\n                        // Potentially trigger refresh again if stuck? Or rely on initial refresh.\n                        // refreshUserData(); // Be cautious adding this here - could cause loops\n                        return; // Don't navigate yet\n                    }\n                    if (effectiveRole === 'student' && !effectiveStudentSession) {\n                        console.log(\"AuthProvider: Nav Effect - User is student but effective student session could not be determined. Waiting.\");\n                        // Potentially trigger refresh again?\n                        // refreshUserData();\n                        return; // Don't navigate yet\n                    }\n                    // --- REDIRECTION LOGIC ---\n                    const getDestination = {\n                        \"AuthProvider.useEffect.getDestination\": ()=>{\n                            // Log the state being used for decision making\n                            console.log(`AuthProvider: getDestination - Effective Role: ${effectiveRole}, Effective Session: ${effectiveStudentSession}`);\n                            switch(effectiveRole){\n                                case 'parent':\n                                    return '/dashboard'; // Parent main dashboard\n                                case 'student':\n                                    // Ensure effectiveStudentSession has a value before constructing the path\n                                    if (!effectiveStudentSession) {\n                                        console.error(\"AuthProvider: getDestination - Student role but no effective studentSession! Falling back to /login.\");\n                                        return '/login'; // Fallback to login if session missing unexpectedly\n                                    }\n                                    return `/student-dashboard/${effectiveStudentSession}`; // Use effectiveStudentSession\n                                // Add cases for teacher/admin if needed\n                                default:\n                                    console.warn(`AuthProvider: Nav Effect - Unknown or null effective role (${effectiveRole}), defaulting to /dashboard`);\n                                    return '/dashboard';\n                            }\n                        }\n                    }[\"AuthProvider.useEffect.getDestination\"];\n                    // Determine destination *before* checking path\n                    const destination = getDestination();\n                    // If destination calculation failed (e.g., student without session), handle it\n                    if (destination === '/login') {\n                        console.error(\"AuthProvider: Nav Effect - Calculated destination is /login even though user should be authenticated. State:\", {\n                            effectiveRole,\n                            effectiveStudentSession\n                        });\n                        // Avoid redirecting to login if authenticated but destination calculation failed\n                        // Maybe logout or show an error page?\n                        // For now, just return to prevent redirect loop\n                        return;\n                    }\n                    // 1. If user is on the login page, redirect them away\n                    if (currentPath === '/login' || currentPath === '/login/') {\n                        const redirectTarget = returnTo || destination;\n                        console.log(`AuthProvider: Nav Effect - User on login page. Redirecting to: ${redirectTarget}`);\n                        // Use replace to avoid adding login page to history after successful login\n                        router.replace(redirectTarget);\n                        return;\n                    }\n                    // 2. If user is authenticated but on the WRONG page for their role, redirect them\n                    const isCorrectPath = {\n                        \"AuthProvider.useEffect.isCorrectPath\": ()=>{\n                            if (!effectiveRole) return false; // Cannot determine correctness without an effective role\n                            if (effectiveRole === 'parent') {\n                                // Parents can be on /dashboard or /student-dashboard/*\n                                return currentPath === '/dashboard' || currentPath.startsWith('/student-dashboard/');\n                            }\n                            if (effectiveRole === 'student') {\n                                // Students should be on their specific dashboard or related pages\n                                // Ensure effectiveStudentSession is checked\n                                if (!effectiveStudentSession) return false; // Cannot be correct path if session is missing\n                                return currentPath.startsWith(`/student-dashboard/${effectiveStudentSession}`) || currentPath.startsWith('/classroom') || currentPath.startsWith('/subjects'); // Allow base /subjects and /subjects/*\n                            }\n                            // Add logic for other roles if necessary\n                            return false; // Default to false if role is unknown\n                        }\n                    }[\"AuthProvider.useEffect.isCorrectPath\"];\n                    if (!isCorrectPath()) {\n                        console.log(`AuthProvider: Nav Effect - User on wrong page (${currentPath}) for effective role (${effectiveRole}). Redirecting to: ${destination}`);\n                        router.push(destination); // Use push here, maybe they navigated manually\n                        return;\n                    }\n                    console.log(`AuthProvider: Nav Effect - User is authenticated and on an appropriate page (${currentPath}) for effective role (${effectiveRole}).`);\n                } else {\n                    // User is not authenticated\n                    console.log(\"AuthProvider: Nav Effect - User not authenticated.\");\n                    const publicPaths = [\n                        '/login',\n                        '/register',\n                        '/forgot-password',\n                        '/reset'\n                    ];\n                    // const currentPath = typeof window !== 'undefined' ? window.location.pathname : ''; // Already defined above\n                    if (!publicPaths.includes(currentPath)) {\n                        console.log(`AuthProvider: Nav Effect - User not authenticated and not on public page (${currentPath}). Redirecting to /login.`);\n                        router.push('/login');\n                        return;\n                    } else {\n                        console.log(`AuthProvider: Nav Effect - User not authenticated, already on public page (${currentPath}). No redirect needed.`);\n                    }\n                }\n            } catch (error) {\n                console.error(\"AuthProvider: Nav Effect - Error:\", error);\n            // router.push('/login'); // Consider fallback\n            }\n        // Add 'status' and 'session' to the dependency array\n        }\n    }[\"AuthProvider.useEffect\"], [\n        loading,\n        user,\n        userRole,\n        studentSession,\n        router,\n        userData,\n        status,\n        session\n    ]); // Added status and session\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Skip if still loading\n            if (loading) return;\n            // If we have a user but no role, try to determine the role\n            if (user && !userRole) {\n                console.log(\"AuthProvider: User authenticated but no role set. Attempting to determine role...\");\n                // Check for parent indicators first\n                const isParent = localStorage.getItem('parent_id') || localStorage.getItem('is_parent') === 'true' || localStorage.getItem('user_role') === 'parent';\n                if (isParent) {\n                    console.log(\"AuthProvider: Parent indicators found in localStorage. Setting role to parent.\");\n                    setUserRole('parent');\n                    localStorage.setItem('user_role', 'parent');\n                    return;\n                }\n                // Check localStorage for role\n                const storedRole = localStorage.getItem('user_role');\n                if (storedRole) {\n                    console.log(`AuthProvider: Found role in localStorage: ${storedRole}`);\n                    setUserRole(storedRole); // Assuming storedRole is 'parent' or 'student'\n                    return;\n                }\n                // Check if we have a student ID in localStorage\n                const storedStudentId = localStorage.getItem('student_id') || localStorage.getItem('current_session');\n                if (storedStudentId) {\n                    console.log(`AuthProvider: Found student ID in localStorage: ${storedStudentId}. Setting role to student.`);\n                    setUserRole('student');\n                    setStudentSession(storedStudentId); // Assuming storedStudentId is a string\n                    localStorage.setItem('user_role', 'student');\n                    return;\n                }\n                // Check if user has custom claims\n                user.getIdTokenResult(true).then({\n                    \"AuthProvider.useEffect\": (idTokenResult)=>{\n                        const claims = idTokenResult.claims;\n                        console.log(\"AuthProvider: Checking user claims:\", claims);\n                        // Ensure claims.role is treated as a string or null\n                        const claimRole = claims.role;\n                        const claimStudentId = claims.student_id;\n                        const claimParentId = claims.parent_id;\n                        if (claimRole === 'parent') {\n                            console.log(`AuthProvider: Found parent role in claims. Setting role to parent.`);\n                            setUserRole('parent');\n                            localStorage.setItem('user_role', 'parent');\n                            localStorage.setItem('is_parent', 'true');\n                        } else if (claimRole) {\n                            console.log(`AuthProvider: Found role in claims: ${claimRole}`);\n                            setUserRole(claimRole);\n                            localStorage.setItem('user_role', claimRole);\n                        } else if (claimStudentId) {\n                            console.log(`AuthProvider: Found student_id in claims: ${claimStudentId}. Setting role to student.`);\n                            setUserRole('student');\n                            setStudentSession(claimStudentId);\n                            localStorage.setItem('user_role', 'student');\n                            localStorage.setItem('student_id', claimStudentId);\n                        // Avoid setting current_session directly from claims unless absolutely necessary\n                        // localStorage.setItem('current_session', claimStudentId);\n                        } else if (claimParentId) {\n                            console.log(`AuthProvider: Found parent_id in claims: ${claimParentId}. Setting role to parent.`);\n                            setUserRole('parent');\n                            localStorage.setItem('user_role', 'parent');\n                            localStorage.setItem('parent_id', claimParentId);\n                            localStorage.setItem('is_parent', 'true');\n                        } else {\n                            // Check email domain for role hints\n                            const email = user.email || '';\n                            if (email.includes('parent') || email.includes('guardian')) {\n                                console.log(\"AuthProvider: Email suggests parent role. Setting role to parent.\");\n                                setUserRole('parent');\n                                localStorage.setItem('user_role', 'parent');\n                                localStorage.setItem('is_parent', 'true');\n                            } else {\n                                // Default to student role if we can't determine\n                                console.log(\"AuthProvider: No role information found. Defaulting to student role.\");\n                                setUserRole('student');\n                                localStorage.setItem('user_role', 'student');\n                            }\n                        }\n                    }\n                }[\"AuthProvider.useEffect\"]).catch({\n                    \"AuthProvider.useEffect\": (err)=>{\n                        console.error(\"AuthProvider: Error getting token claims:\", err);\n                        // Default to student role on error\n                        setUserRole('student');\n                        localStorage.setItem('user_role', 'student');\n                    }\n                }[\"AuthProvider.useEffect\"]);\n            }\n        }\n    }[\"AuthProvider.useEffect\"], [\n        loading,\n        user,\n        userRole,\n        setUserRole,\n        setStudentSession\n    ]);\n    // *** NEW EFFECT: Update state based on NextAuth session ***\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Update role and session based on NextAuth session changes\n            if (status === 'authenticated' && session?.user) {\n                console.log(\"AuthProvider: NextAuth session authenticated. Updating role/session state from session:\", session.user);\n                // Use the defined SessionUser interface for type safety\n                const sessionUser = session.user;\n                const roleFromSession = sessionUser?.role;\n                const idFromSession = sessionUser?.id;\n                console.log(`AuthProvider: Extracted from session - Role: ${roleFromSession}, ID: ${idFromSession}`);\n                if (roleFromSession && roleFromSession !== userRole) {\n                    setUserRole(roleFromSession);\n                    localStorage.setItem('user_role', roleFromSession);\n                    console.log(`AuthProvider: Set userRole state to '${roleFromSession}' from session.`);\n                }\n                if (roleFromSession === 'student' && idFromSession && idFromSession !== studentSession) {\n                    setStudentSession(idFromSession);\n                    localStorage.setItem('student_id', idFromSession);\n                    if (localStorage.getItem('current_session') === idFromSession) {\n                        localStorage.removeItem('current_session');\n                    }\n                    console.log(`AuthProvider: Set studentSession state to '${idFromSession}' from session.`);\n                }\n            } else if (status === 'unauthenticated') {\n                console.log(\"AuthProvider: NextAuth session status is not 'authenticated'. Current status:\", status);\n            }\n        }\n    }[\"AuthProvider.useEffect\"], [\n        status,\n        session,\n        userRole,\n        studentSession,\n        setUserRole,\n        setStudentSession\n    ]);\n    // Navigation Effect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Add detailed logging at the start of the effect\n            console.log(\"AuthProvider: Nav Effect - START\", {\n                timestamp: new Date().toISOString(),\n                loading,\n                status,\n                user: user ? {\n                    uid: user.uid\n                } : null,\n                session: session ? {\n                    user: session.user,\n                    expires: session.expires\n                } : null,\n                stateUserRole: userRole,\n                stateStudentSession: studentSession // Log current state value\n            });\n            if (loading || status === 'loading') {\n                console.log(\"AuthProvider: Nav Effect - Still loading Firebase auth or NextAuth session.\");\n                return;\n            }\n            const firebaseUserPresent = !!user && !!user.uid;\n            const nextAuthSessionAuthenticated = status === 'authenticated' && !!session?.user; // Ensure session.user exists\n            const overallAuthenticated = firebaseUserPresent || nextAuthSessionAuthenticated;\n            let derivedRole = null;\n            let derivedStudentId = null;\n            // Use SessionUser type for session check\n            const sessionUser = session?.user;\n            if (nextAuthSessionAuthenticated && sessionUser) {\n                derivedRole = sessionUser?.role || null;\n                if (derivedRole === 'student') {\n                    derivedStudentId = sessionUser?.id || null;\n                }\n                console.log(`AuthProvider: Nav Effect - Derived from NextAuth: Role='${derivedRole}', StudentID='${derivedStudentId}'`);\n            }\n            // Fallback to component state if NextAuth session didn't provide info or isn't primary\n            if (!derivedRole && userRole) {\n                derivedRole = userRole;\n                console.log(`AuthProvider: Nav Effect - Using Role from state: '${derivedRole}'`);\n            }\n            if (derivedRole === 'student' && !derivedStudentId && studentSession) {\n                derivedStudentId = studentSession;\n                console.log(`AuthProvider: Nav Effect - Using StudentID from state: '${derivedStudentId}'`);\n            }\n            // Fallback to localStorage as a last resort for role if still unknown (use with caution)\n            if (!derivedRole) {\n                const storedRole = localStorage.getItem('user_role');\n                if (storedRole) {\n                    derivedRole = storedRole;\n                    console.log(`AuthProvider: Nav Effect - Using Role from localStorage: '${derivedRole}'`);\n                    if (derivedRole === 'student' && !derivedStudentId) {\n                        const storedStudentId = localStorage.getItem('student_id');\n                        if (storedStudentId) {\n                            derivedStudentId = storedStudentId;\n                            console.log(`AuthProvider: Nav Effect - Using StudentID from localStorage: '${derivedStudentId}'`);\n                        }\n                    }\n                }\n            }\n            console.log(\"AuthProvider: Nav Effect - Final derived values:\", {\n                overallAuthenticated,\n                derivedRole,\n                derivedStudentId\n            });\n            // Get current path and query parameters safely\n            const currentPath =  false ? 0 : '';\n            const searchParams =  false ? 0 : new URLSearchParams();\n            const returnTo = searchParams.get('returnTo');\n            const resetParam = searchParams.get('reset') === 'true';\n            // Handle reset parameter first\n            if (resetParam && currentPath !== '/login') {\n                console.log(\"AuthProvider: Nav Effect - Reset parameter detected, redirecting to /login?reset=true\");\n                // Clear local state immediately\n                setUser(null);\n                setUserData(null);\n                setChildrenData(null);\n                setUserRole(null);\n                setStudentSession(null);\n                // Clear storage\n                localStorage.clear();\n                sessionStorage.clear();\n                // Use replace to avoid adding reset=true to history\n                router.replace('/login?reset=true');\n                return;\n            }\n            try {\n                if (overallAuthenticated) {\n                    if (!derivedRole) {\n                        console.log(\"AuthProvider: Nav Effect - Authenticated, but final derived role is null. Waiting for role resolution. Current states:\", {\n                            userRole,\n                            studentSession,\n                            sessionUser: session?.user\n                        });\n                        // Potentially call refreshUserData if it seems stuck, but be careful of loops.\n                        // refreshUserData(); // Might be too aggressive.\n                        return; // Wait for role to be set.\n                    }\n                    if (derivedRole === 'student' && !derivedStudentId) {\n                        console.log(\"AuthProvider: Nav Effect - Authenticated as student, but final derived student ID is null. Waiting. Current states:\", {\n                            userRole,\n                            studentSession,\n                            sessionUser: session?.user\n                        });\n                        return; // Wait for student ID.\n                    }\n                    // --- REDIRECTION LOGIC (using derivedRole, derivedStudentId) ---\n                    const getDestination = {\n                        \"AuthProvider.useEffect.getDestination\": ()=>{\n                            // Uses derivedRole and derivedStudentId ...\n                            switch(derivedRole){\n                                case 'parent':\n                                    return '/dashboard';\n                                case 'student':\n                                    if (!derivedStudentId) {\n                                        console.error(\"AuthProvider: getDestination - Student role but no derivedStudentId!\");\n                                        return '/login';\n                                    }\n                                    return `/student-dashboard/${derivedStudentId}`;\n                                default:\n                                    console.warn(`AuthProvider: Nav Effect - Unknown or null derived role (${derivedRole}), defaulting to /dashboard`);\n                                    return '/dashboard'; // Default destination\n                            }\n                        }\n                    }[\"AuthProvider.useEffect.getDestination\"];\n                    const destination = getDestination();\n                    // Ensure this logic is robust\n                    if (destination === '/login' && overallAuthenticated) {\n                        console.warn(\"AuthProvider: Nav Effect - Authenticated user is being directed to /login. This shouldn't happen. Destination calc issue or state inconsistency.\");\n                        return; // Prevent redirect to login if authenticated.\n                    }\n                    if (currentPath === '/login' || currentPath === '/login/') {\n                        const redirectTarget = returnTo || destination;\n                        console.log(`AuthProvider: Nav Effect - User on login. Redirecting to: ${redirectTarget}`);\n                        router.replace(redirectTarget);\n                        return;\n                    }\n                    // Simplified isCorrectPath\n                    let onCorrectPath = false;\n                    if (derivedRole === 'parent' && (currentPath === '/dashboard' || currentPath.startsWith('/student-dashboard/'))) {\n                        onCorrectPath = true;\n                    } else if (derivedRole === 'student' && derivedStudentId && (currentPath.startsWith(`/student-dashboard/${derivedStudentId}`) || currentPath.startsWith('/classroom') || currentPath.startsWith('/subjects'))) {\n                        onCorrectPath = true;\n                    } else if (derivedRole && ![\n                        'parent',\n                        'student'\n                    ].includes(derivedRole) && currentPath === '/dashboard') {\n                        onCorrectPath = true;\n                    }\n                    if (!onCorrectPath && destination !== currentPath) {\n                        console.log(`AuthProvider: Nav Effect - User on wrong page (${currentPath}) for role (${derivedRole}). Redirecting to: ${destination}`);\n                        router.push(destination);\n                        return;\n                    }\n                    console.log(`AuthProvider: Nav Effect - Authenticated and on appropriate page or no redirect needed.`);\n                } else {\n                    console.log(\"AuthProvider: Nav Effect - User not authenticated overall.\");\n                    const publicPaths = [\n                        '/login',\n                        '/register',\n                        '/forgot-password',\n                        '/reset'\n                    ];\n                    if (!publicPaths.includes(currentPath)) {\n                        console.log(`AuthProvider: Nav Effect - Not authenticated and not on public page (${currentPath}). Redirecting to /login.`);\n                        router.push('/login');\n                    } else {\n                        console.log(`AuthProvider: Nav Effect - User not authenticated, already on public page (${currentPath}). No redirect needed.`);\n                    }\n                }\n            } catch (error) {\n                console.error(\"AuthProvider: Nav Effect - Unhandled Error:\", error);\n            // router.push('/login'); // Consider fallback\n            }\n        // Add 'status' and 'session' to the dependency array\n        }\n    }[\"AuthProvider.useEffect\"], [\n        loading,\n        user,\n        userRole,\n        studentSession,\n        router,\n        userData,\n        status,\n        session\n    ]); // Added status and session\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            userData,\n            childrenData,\n            loading,\n            error,\n            isAuthenticated: !!user && !!user.uid || status === 'authenticated',\n            // Use SessionUser type for fallbacks\n            studentSession: studentSession || (session?.user?.role === 'student' ? session?.user?.id : null),\n            userRole: userData?.role || userRole || session?.user?.role || null,\n            manualSyncWithLocalStorage,\n            refreshUserData,\n            refreshChildrenData,\n            handleLoginSuccess,\n            logout: async ()=>{\n                console.log(\"AuthProvider: Logging out...\");\n                try {\n                    await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signOut)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth);\n                    // Clear local state and storage\n                    setUser(null);\n                    setUserData(null);\n                    setChildrenData(null);\n                    setUserRole(null);\n                    setStudentSession(null);\n                    localStorage.clear(); // Or remove specific items\n                    sessionStorage.clear();\n                    console.log(\"AuthProvider: Logout successful. Redirecting to login.\");\n                    router.push('/login');\n                } catch (err) {\n                    console.error(\"AuthProvider: Logout failed:\", err);\n                    setError(\"Logout failed. Please try again.\");\n                    // Still attempt to clear state/storage even if signOut fails\n                    localStorage.clear();\n                    sessionStorage.clear();\n                    router.push('/login'); // Redirect even on error\n                }\n            }\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers\\\\AuthProvider.tsx\",\n        lineNumber: 1157,\n        columnNumber: 5\n    }, this);\n}\nconst useAuth = ()=>(0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/providers/AuthProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/providers/ClientToastWrapper.tsx":
/*!**************************************************!*\
  !*** ./src/app/providers/ClientToastWrapper.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClientToastWrapper: () => (/* binding */ ClientToastWrapper)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(ssr)/./node_modules/next/dist/api/app-dynamic.js\");\n/* __next_internal_client_entry_do_not_use__ ClientToastWrapper auto */ \n\n\n// Dynamically import ClientToastWrapper implementation\nconst ToastImplementation = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(async ()=>{\n     true && /*require.resolve*/(/*! ./ToasterProvider */ \"(ssr)/./src/app/providers/ToasterProvider.tsx\");\n}, {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\providers\\\\ClientToastWrapper.tsx -> \" + \"./ToasterProvider\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>null\n});\nfunction ClientToastWrapper({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastImplementation, {\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers\\\\ClientToastWrapper.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3Byb3ZpZGVycy9DbGllbnRUb2FzdFdyYXBwZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFFMEI7QUFDUztBQUVuQyx1REFBdUQ7QUFDdkQsTUFBTUUsc0JBQXNCRCx3REFBT0E7Ozs7Ozs7O0lBRS9CRSxLQUFLO0lBQU9DLFNBQVMsSUFBTTs7QUFHeEIsU0FBU0MsbUJBQW1CLEVBQUVDLFFBQVEsRUFBaUM7SUFDNUUscUJBQ0UsOERBQUNKO2tCQUNFSTs7Ozs7O0FBR1AiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccGNcXE9uZURyaXZlXFxEZXNrdG9wXFxEZXNrdG9wXFxTb2x5bnRhX1dlYnNpdGVcXGZyb250ZW5kXFxsZXNzb24tcGxhdGZvcm1cXHNyY1xcYXBwXFxwcm92aWRlcnNcXENsaWVudFRvYXN0V3JhcHBlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xyXG5cclxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IGR5bmFtaWMgZnJvbSAnbmV4dC9keW5hbWljJztcclxuXHJcbi8vIER5bmFtaWNhbGx5IGltcG9ydCBDbGllbnRUb2FzdFdyYXBwZXIgaW1wbGVtZW50YXRpb25cclxuY29uc3QgVG9hc3RJbXBsZW1lbnRhdGlvbiA9IGR5bmFtaWMoXHJcbiAgKCkgPT4gaW1wb3J0KCcuL1RvYXN0ZXJQcm92aWRlcicpLnRoZW4obW9kID0+IG1vZC5DbGllbnRUb2FzdFdyYXBwZXIpLFxyXG4gIHsgc3NyOiBmYWxzZSwgbG9hZGluZzogKCkgPT4gbnVsbCB9XHJcbik7XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gQ2xpZW50VG9hc3RXcmFwcGVyKHsgY2hpbGRyZW4gfTogeyBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlIH0pIHtcclxuICByZXR1cm4gKFxyXG4gICAgPFRvYXN0SW1wbGVtZW50YXRpb24+XHJcbiAgICAgIHtjaGlsZHJlbn1cclxuICAgIDwvVG9hc3RJbXBsZW1lbnRhdGlvbj5cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsImR5bmFtaWMiLCJUb2FzdEltcGxlbWVudGF0aW9uIiwic3NyIiwibG9hZGluZyIsIkNsaWVudFRvYXN0V3JhcHBlciIsImNoaWxkcmVuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/providers/ClientToastWrapper.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/providers/SessionProvider.tsx":
/*!***********************************************!*\
  !*** ./src/app/providers/SessionProvider.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SessionProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction SessionProvider({ children }) {\n    // Always wrap children in NextAuthSessionProvider\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers\\\\SessionProvider.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3Byb3ZpZGVycy9TZXNzaW9uUHJvdmlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUU2RTtBQUU5RCxTQUFTQSxnQkFBZ0IsRUFBRUUsUUFBUSxFQUFpQztJQUNqRixrREFBa0Q7SUFDbEQscUJBQ0UsOERBQUNELDREQUF1QkE7a0JBQ3JCQzs7Ozs7O0FBR1AiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccGNcXE9uZURyaXZlXFxEZXNrdG9wXFxEZXNrdG9wXFxTb2x5bnRhX1dlYnNpdGVcXGZyb250ZW5kXFxsZXNzb24tcGxhdGZvcm1cXHNyY1xcYXBwXFxwcm92aWRlcnNcXFNlc3Npb25Qcm92aWRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xyXG5cclxuaW1wb3J0IHsgU2Vzc2lvblByb3ZpZGVyIGFzIE5leHRBdXRoU2Vzc2lvblByb3ZpZGVyIH0gZnJvbSAnbmV4dC1hdXRoL3JlYWN0JztcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFNlc3Npb25Qcm92aWRlcih7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZSB9KSB7XHJcbiAgLy8gQWx3YXlzIHdyYXAgY2hpbGRyZW4gaW4gTmV4dEF1dGhTZXNzaW9uUHJvdmlkZXJcclxuICByZXR1cm4gKFxyXG4gICAgPE5leHRBdXRoU2Vzc2lvblByb3ZpZGVyPlxyXG4gICAgICB7Y2hpbGRyZW59XHJcbiAgICA8L05leHRBdXRoU2Vzc2lvblByb3ZpZGVyPlxyXG4gICk7XHJcbn0iXSwibmFtZXMiOlsiU2Vzc2lvblByb3ZpZGVyIiwiTmV4dEF1dGhTZXNzaW9uUHJvdmlkZXIiLCJjaGlsZHJlbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/providers/SessionProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/providers/ThemeProvider.tsx":
/*!*********************************************!*\
  !*** ./src/app/providers/ThemeProvider.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n// Theme provider with hydration-safe implementation\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ThemeProvider({ children }) {\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('light'); // Default theme\n    const [hasMounted, setHasMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Effect to run once on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            setHasMounted(true); // Mark as mounted\n        }\n    }[\"ThemeProvider.useEffect\"], []);\n    // Effect to apply theme once mounted\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            if (hasMounted) {\n                const savedTheme = localStorage.getItem('theme') || 'light';\n                setTheme(savedTheme); // Update state for potential context consumers\n                // Apply attributes directly to <html> tag\n                document.documentElement.className = savedTheme; // Set class for CSS targeting\n                document.documentElement.setAttribute('color-pick-mode', savedTheme);\n                document.documentElement.style.setProperty('color-scheme', savedTheme); // Set color-scheme style\n            }\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        hasMounted\n    ]); // Rerun if hasMounted changes (which is only once)\n    // Avoid rendering children until mounted to prevent mismatch\n    if (!hasMounted) {\n        // Render nothing or a placeholder/loader on the server and initial client render\n        // Returning children directly here could still cause mismatches if they depend on the theme\n        return null;\n    // Alternatively, render children but without theme-specific wrapper/context:\n    // return <>{children}</>;\n    }\n    // Once mounted, render children. The theme is applied via useEffect directly to documentElement.\n    // No need for a wrapper div with data-theme here.\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/providers/ThemeProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/start-lesson/page.tsx":
/*!***************************************!*\
  !*** ./src/app/start-lesson/page.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StartLessonPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/LoadingSpinner */ \"(ssr)/./src/components/ui/LoadingSpinner.tsx\");\n/* harmony import */ var _components_shadcn_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/shadcn/button */ \"(ssr)/./src/components/shadcn/button.tsx\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n // Import useEffect\n\n\n\n// --- Corrected Firebase import path ---\n\n// -------------------------------------\n // Import onAuthStateChanged\nfunction StartLessonPage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    // Extract all required lesson/session params from query string\n    const studentId = searchParams.get(\"studentId\") || searchParams.get(\"student_id\") || \"\";\n    const lessonRef = searchParams.get(\"lessonRef\") || \"\";\n    const country = searchParams.get(\"country\") || \"\";\n    const curriculum = searchParams.get(\"curriculum\") || \"\";\n    const grade = searchParams.get(\"grade\") || \"\";\n    const level = searchParams.get(\"level\") || \"\";\n    const subject = searchParams.get(\"subject\") || \"\";\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isAuthReady, setIsAuthReady] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // State to track auth readiness\n    // Wait for Firebase Auth to initialize\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StartLessonPage.useEffect\": ()=>{\n            const unsubscribe = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_6__.onAuthStateChanged)(_lib_firebase__WEBPACK_IMPORTED_MODULE_5__.auth, {\n                \"StartLessonPage.useEffect.unsubscribe\": (user)=>{\n                    setIsAuthReady(true); // Mark auth as ready once the state is known\n                    if (!user) {\n                        console.warn(\"[StartLessonPage] Firebase user not logged in on mount.\");\n                    // Optionally redirect immediately, or let handleStartLesson handle it\n                    // router.push('/login'); \n                    } else {\n                        console.log(\"[StartLessonPage] Firebase user detected:\", user.uid);\n                    }\n                }\n            }[\"StartLessonPage.useEffect.unsubscribe\"]);\n            return ({\n                \"StartLessonPage.useEffect\": ()=>unsubscribe()\n            })[\"StartLessonPage.useEffect\"]; // Cleanup subscription on unmount\n        }\n    }[\"StartLessonPage.useEffect\"], [\n        router\n    ]);\n    // Helper for exponential backoff retry\n    async function fetchWithRetry(url, options, retries = 3, delay = 500) {\n        try {\n            const response = await fetch(url, options);\n            if (!response.ok) {\n                // Log more details on failure\n                let errorBody = `Server responded with ${response.status}`;\n                try {\n                    const bodyText = await response.text();\n                    errorBody += `: ${bodyText}`;\n                } catch (e) {}\n                console.error(`Fetch failed with status ${response.status}: ${errorBody}`);\n                throw new Error(errorBody);\n            }\n            return response;\n        } catch (error) {\n            console.error(`fetchWithRetry error (retries left: ${retries}):`, error.message);\n            if (retries > 0) {\n                await new Promise((res)=>setTimeout(res, delay));\n                return fetchWithRetry(url, options, retries - 1, delay * 2);\n            }\n            throw error; // Re-throw the final error\n        }\n    }\n    async function handleStartLesson() {\n        if (!isAuthReady) {\n            setError(\"Authentication check is still in progress. Please wait a moment.\");\n            return;\n        }\n        if (!studentId || !lessonRef || !subject || !grade || !level || !country || !curriculum) {\n            const missing = [\n                !studentId ? 'studentId' : null,\n                !lessonRef ? 'lessonRef' : null,\n                !country ? 'country' : null,\n                !curriculum ? 'curriculum' : null,\n                !grade ? 'grade' : null,\n                !level ? 'level' : null,\n                !subject ? 'subject' : null\n            ].filter(Boolean).join(', ');\n            setError(`Missing required parameters: ${missing}`);\n            return;\n        }\n        setLoading(true);\n        setError(null);\n        let idToken = null;\n        try {\n            const user = _lib_firebase__WEBPACK_IMPORTED_MODULE_5__.auth.currentUser;\n            if (!user) {\n                throw new Error(\"No authenticated user found. Please log in.\");\n            }\n            // --- Get fresh ID token ---\n            try {\n                console.log(\"[StartLessonPage] Attempting to get ID token (force refresh)...\");\n                idToken = await user.getIdToken(true); // forceRefresh = true\n                if (!idToken) {\n                    throw new Error(\"Failed to obtain a valid ID token (getIdToken returned null/undefined).\");\n                }\n                console.log(\"[StartLessonPage] Successfully obtained ID token.\");\n            } catch (tokenError) {\n                console.error(\"[StartLessonPage] Error getting ID token:\", tokenError);\n                throw new Error(`Failed to get authentication token: ${tokenError.message}. Please try logging out and back in.`);\n            }\n            // ------------------------\n            console.log(\"[StartLessonPage] ID Token obtained (first 20 chars):\", idToken ? idToken.substring(0, 20) + '...' : 'null/undefined');\n            // Call /api/lesson-content to initialize the session\n            console.log(\"[StartLessonPage] Calling session initialization API (/api/lesson-content)...\");\n            const sessionInitResponse = await fetchWithRetry(\"/api/lesson-content\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": `Bearer ${idToken}`\n                },\n                body: JSON.stringify({\n                    student_id: studentId,\n                    lessonRef: lessonRef,\n                    subject: subject,\n                    country: country,\n                    curriculum: curriculum,\n                    grade: grade,\n                    level: level,\n                    current_phase: \"diagnostic_start_probe\",\n                    student_name: localStorage.getItem('student_name') || undefined\n                })\n            });\n            console.log(\"[StartLessonPage] /api/lesson-content response status:\", sessionInitResponse.status);\n            let sessionInitData;\n            try {\n                sessionInitData = await sessionInitResponse.json();\n                // VERY IMPORTANT LOG:\n                console.log(\"[StartLessonPage] RAW response from /lesson-content proxy:\", JSON.stringify(sessionInitData, null, 2));\n            } catch (jsonError) {\n                console.error(\"[StartLessonPage] Failed to parse JSON response from /lesson-content proxy:\", jsonError.message);\n                const responseText = await sessionInitResponse.text().catch(()=>\"Could not read response text.\");\n                console.error(\"[StartLessonPage] Raw response text from proxy was:\", responseText);\n                throw new Error(`Received invalid JSON response from proxy. Response text: ${responseText.substring(0, 200)}`);\n            }\n            // Log individual parts\n            console.log(\"[StartLessonPage] sessionInitData.success:\", sessionInitData.success);\n            console.log(\"[StartLessonPage] sessionInitData.message:\", sessionInitData.message);\n            console.log(\"[StartLessonPage] sessionInitData.sessionId (top-level):\", sessionInitData.sessionId);\n            console.log(\"[StartLessonPage] sessionInitData.data:\", sessionInitData.data);\n            if (sessionInitData.data) {\n                console.log(\"[StartLessonPage] sessionInitData.data.session_id (nested):\", sessionInitData.data.session_id);\n            }\n            const successFlag = sessionInitData.success;\n            const messageFromServer = sessionInitData.message || \"Unknown server message.\";\n            const extractedSessionId = sessionInitData.sessionId || sessionInitData.data && sessionInitData.data.session_id || null;\n            console.log(`[StartLessonPage] Evaluation: successFlag=${successFlag}, messageFromServer='${messageFromServer}', extractedSessionId='${extractedSessionId}'`);\n            if (!successFlag || !extractedSessionId) {\n                const errorMessage = `API call was logically successful (message: '${messageFromServer}') but session ID was missing or invalid (extracted: '${extractedSessionId}').`;\n                console.error(\"[StartLessonPage] Critical logic failure:\", errorMessage, \"Full Proxy Response:\", sessionInitData);\n                // Throw a more specific error to distinguish from actual API call failures\n                throw new Error(`LogicError: ${errorMessage}`);\n            }\n            const newSessionId = extractedSessionId;\n            console.log(\"[StartLessonPage] Obtained session ID:\", newSessionId);\n            // On success, redirect to classroom page with necessary params\n            const redirectParams = new URLSearchParams({\n                session_id: newSessionId,\n                lessonRef: lessonRef,\n                studentId: studentId,\n                level: level,\n                subject: subject,\n                country: country,\n                curriculum: curriculum,\n                grade: grade\n            });\n            const redirectUrl = `/classroom?${redirectParams.toString()}`;\n            console.log(\"[StartLessonPage] Redirecting to:\", redirectUrl);\n            router.push(redirectUrl);\n        } catch (err) {\n            console.error(\"[StartLessonPage] Error in handleStartLesson:\", err);\n            // Provide more context in error messages\n            let displayError = err.message || \"An unexpected error occurred while starting the lesson.\";\n            if (displayError.includes(\"status 401\") || displayError.includes(\"authentication\")) {\n                displayError += \" Please try logging out and back in.\";\n            } else if (displayError.includes(\"status 404\")) {\n                displayError = `Could not find the requested lesson (${lessonRef}). Please check the lesson details.`;\n            } else if (displayError.includes(\"status 500\")) {\n                displayError = \"A server error occurred. Please try again later or contact support.\";\n            }\n            setError(displayError);\n        } finally{\n            setLoading(false);\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center p-4 bg-gray-100\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white shadow-lg rounded-lg p-8 w-full max-w-md\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold mb-6 text-center text-gray-800\",\n                    children: \"Start Your Lesson\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                    lineNumber: 217,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 space-y-1 text-gray-700\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                    children: \"Student ID:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 14\n                                }, this),\n                                \" \",\n                                studentId || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-red-500\",\n                                    children: \"Missing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 47\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                    children: \"Lesson Ref:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 14\n                                }, this),\n                                \" \",\n                                lessonRef || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-red-500\",\n                                    children: \"Missing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 47\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                    children: \"Subject:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 14\n                                }, this),\n                                \" \",\n                                subject || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-red-500\",\n                                    children: \"Missing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 42\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                    children: \"Grade:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 14\n                                }, this),\n                                \" \",\n                                grade || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-red-500\",\n                                    children: \"Missing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 38\n                                }, this),\n                                \" (\",\n                                level || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-red-500\",\n                                    children: \"Missing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 96\n                                }, this),\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                    children: \"Curriculum:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 14\n                                }, this),\n                                \" \",\n                                curriculum || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-red-500\",\n                                    children: \"Missing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 48\n                                }, this),\n                                \" (\",\n                                country || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-red-500\",\n                                    children: \"Missing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 108\n                                }, this),\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                    lineNumber: 218,\n                    columnNumber: 9\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4\",\n                    role: \"alert\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            className: \"font-bold\",\n                            children: \"Error: \"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 17\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"block sm:inline\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 17\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 13\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                    className: \"w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition duration-150 ease-in-out disabled:opacity-50\",\n                    onClick: handleStartLesson,\n                    disabled: loading || !isAuthReady || !studentId || !lessonRef || !subject || !grade || !level || !country || !curriculum,\n                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        size: \"small\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 22\n                    }, this) : isAuthReady ? \"Start Lesson\" : \"Initializing...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 9\n                }, this),\n                !isAuthReady && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xs text-center text-gray-500 mt-2\",\n                    children: \"Waiting for authentication...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 27\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n            lineNumber: 216,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n        lineNumber: 215,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/start-lesson/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/shadcn/button.tsx":
/*!******************************************!*\
  !*** ./src/components/shadcn/button.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n// components/shadcn/button.tsx\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-gray-400 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-white\", {\n    variants: {\n        variant: {\n            default: \"bg-blue-600 text-white hover:bg-blue-700\",\n            destructive: \"bg-red-500 text-white hover:bg-red-600\",\n            outline: \"border border-gray-200 hover:bg-gray-100\",\n            secondary: \"bg-gray-200 text-gray-900 hover:bg-gray-300\",\n            ghost: \"hover:bg-gray-100\",\n            link: \"underline-offset-4 hover:underline text-blue-600\"\n        },\n        size: {\n            default: \"h-10 py-2 px-4\",\n            sm: \"h-9 px-3 rounded-md\",\n            lg: \"h-11 px-8 rounded-md\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\shadcn\\\\button.tsx\",\n        lineNumber: 42,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9zaGFkY24vYnV0dG9uLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFBLCtCQUErQjs7QUFDRDtBQUNtQztBQUVqQztBQUVoQyxNQUFNRyxpQkFBaUJGLDZEQUFHQSxDQUN4Qix1UUFDQTtJQUNFRyxVQUFVO1FBQ1JDLFNBQVM7WUFDUEMsU0FBUztZQUNUQyxhQUFhO1lBQ2JDLFNBQVM7WUFDVEMsV0FBVztZQUNYQyxPQUFPO1lBQ1BDLE1BQU07UUFDUjtRQUNBQyxNQUFNO1lBQ0pOLFNBQVM7WUFDVE8sSUFBSTtZQUNKQyxJQUFJO1lBQ0pDLE1BQU07UUFDUjtJQUNGO0lBQ0FDLGlCQUFpQjtRQUNmWCxTQUFTO1FBQ1RPLE1BQU07SUFDUjtBQUNGO0FBU0YsTUFBTUssdUJBQVNqQiw2Q0FBZ0IsQ0FDN0IsQ0FBQyxFQUFFbUIsU0FBUyxFQUFFZCxPQUFPLEVBQUVPLElBQUksRUFBRVEsVUFBVSxLQUFLLEVBQUUsR0FBR0MsT0FBTyxFQUFFQztJQUN4RCxxQkFDRSw4REFBQ0M7UUFDQ0osV0FBV2pCLDhDQUFFQSxDQUFDQyxlQUFlO1lBQUVFO1lBQVNPO1lBQU1PO1FBQVU7UUFDeERHLEtBQUtBO1FBQ0osR0FBR0QsS0FBSzs7Ozs7O0FBR2Y7QUFFRkosT0FBT08sV0FBVyxHQUFHO0FBRVkiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccGNcXE9uZURyaXZlXFxEZXNrdG9wXFxEZXNrdG9wXFxTb2x5bnRhX1dlYnNpdGVcXGZyb250ZW5kXFxsZXNzb24tcGxhdGZvcm1cXHNyY1xcY29tcG9uZW50c1xcc2hhZGNuXFxidXR0b24udHN4Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIGNvbXBvbmVudHMvc2hhZGNuL2J1dHRvbi50c3hcclxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcclxuaW1wb3J0IHsgdHlwZSBWYXJpYW50UHJvcHMsIGN2YSB9IGZyb20gXCJjbGFzcy12YXJpYW5jZS1hdXRob3JpdHlcIlxyXG5cclxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxyXG5cclxuY29uc3QgYnV0dG9uVmFyaWFudHMgPSBjdmEoXHJcbiAgXCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcm91bmRlZC1tZCB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tY29sb3JzIGZvY3VzLXZpc2libGU6b3V0bGluZS1ub25lIGZvY3VzLXZpc2libGU6cmluZy0yIGZvY3VzLXZpc2libGU6cmluZy1ncmF5LTQwMCBmb2N1cy12aXNpYmxlOnJpbmctb2Zmc2V0LTIgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpwb2ludGVyLWV2ZW50cy1ub25lIHJpbmctb2Zmc2V0LXdoaXRlXCIsXHJcbiAge1xyXG4gICAgdmFyaWFudHM6IHtcclxuICAgICAgdmFyaWFudDoge1xyXG4gICAgICAgIGRlZmF1bHQ6IFwiYmctYmx1ZS02MDAgdGV4dC13aGl0ZSBob3ZlcjpiZy1ibHVlLTcwMFwiLFxyXG4gICAgICAgIGRlc3RydWN0aXZlOiBcImJnLXJlZC01MDAgdGV4dC13aGl0ZSBob3ZlcjpiZy1yZWQtNjAwXCIsXHJcbiAgICAgICAgb3V0bGluZTogXCJib3JkZXIgYm9yZGVyLWdyYXktMjAwIGhvdmVyOmJnLWdyYXktMTAwXCIsXHJcbiAgICAgICAgc2Vjb25kYXJ5OiBcImJnLWdyYXktMjAwIHRleHQtZ3JheS05MDAgaG92ZXI6YmctZ3JheS0zMDBcIixcclxuICAgICAgICBnaG9zdDogXCJob3ZlcjpiZy1ncmF5LTEwMFwiLFxyXG4gICAgICAgIGxpbms6IFwidW5kZXJsaW5lLW9mZnNldC00IGhvdmVyOnVuZGVybGluZSB0ZXh0LWJsdWUtNjAwXCIsXHJcbiAgICAgIH0sXHJcbiAgICAgIHNpemU6IHtcclxuICAgICAgICBkZWZhdWx0OiBcImgtMTAgcHktMiBweC00XCIsXHJcbiAgICAgICAgc206IFwiaC05IHB4LTMgcm91bmRlZC1tZFwiLFxyXG4gICAgICAgIGxnOiBcImgtMTEgcHgtOCByb3VuZGVkLW1kXCIsXHJcbiAgICAgICAgaWNvbjogXCJoLTEwIHctMTBcIixcclxuICAgICAgfSxcclxuICAgIH0sXHJcbiAgICBkZWZhdWx0VmFyaWFudHM6IHtcclxuICAgICAgdmFyaWFudDogXCJkZWZhdWx0XCIsXHJcbiAgICAgIHNpemU6IFwiZGVmYXVsdFwiLFxyXG4gICAgfSxcclxuICB9XHJcbilcclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgQnV0dG9uUHJvcHNcclxuICBleHRlbmRzIFJlYWN0LkJ1dHRvbkhUTUxBdHRyaWJ1dGVzPEhUTUxCdXR0b25FbGVtZW50PixcclxuICAgIFZhcmlhbnRQcm9wczx0eXBlb2YgYnV0dG9uVmFyaWFudHM+IHtcclxuICBhc0NoaWxkPzogYm9vbGVhblxyXG59XHJcblxyXG5jb25zdCBCdXR0b24gPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxCdXR0b25FbGVtZW50LCBCdXR0b25Qcm9wcz4oXHJcbiAgKHsgY2xhc3NOYW1lLCB2YXJpYW50LCBzaXplLCBhc0NoaWxkID0gZmFsc2UsIC4uLnByb3BzIH0sIHJlZikgPT4ge1xyXG4gICAgcmV0dXJuIChcclxuICAgICAgPGJ1dHRvblxyXG4gICAgICAgIGNsYXNzTmFtZT17Y24oYnV0dG9uVmFyaWFudHMoeyB2YXJpYW50LCBzaXplLCBjbGFzc05hbWUgfSkpfVxyXG4gICAgICAgIHJlZj17cmVmfVxyXG4gICAgICAgIHsuLi5wcm9wc31cclxuICAgICAgLz5cclxuICAgIClcclxuICB9XHJcbilcclxuQnV0dG9uLmRpc3BsYXlOYW1lID0gXCJCdXR0b25cIlxyXG5cclxuZXhwb3J0IHsgQnV0dG9uLCBidXR0b25WYXJpYW50cyB9Il0sIm5hbWVzIjpbIlJlYWN0IiwiY3ZhIiwiY24iLCJidXR0b25WYXJpYW50cyIsInZhcmlhbnRzIiwidmFyaWFudCIsImRlZmF1bHQiLCJkZXN0cnVjdGl2ZSIsIm91dGxpbmUiLCJzZWNvbmRhcnkiLCJnaG9zdCIsImxpbmsiLCJzaXplIiwic20iLCJsZyIsImljb24iLCJkZWZhdWx0VmFyaWFudHMiLCJCdXR0b24iLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwiYXNDaGlsZCIsInByb3BzIiwicmVmIiwiYnV0dG9uIiwiZGlzcGxheU5hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/shadcn/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/LoadingSpinner.tsx":
/*!**********************************************!*\
  !*** ./src/components/ui/LoadingSpinner.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoadingSpinner)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction LoadingSpinner({ size = 'medium' }) {\n    const sizeClasses = {\n        small: 'w-4 h-4 border-2',\n        medium: 'w-8 h-8 border-3',\n        large: 'w-12 h-12 border-4'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex justify-center items-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `${sizeClasses[size]} border-gray-300 border-t-primary rounded-full animate-spin`\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ui\\\\LoadingSpinner.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ui\\\\LoadingSpinner.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9Mb2FkaW5nU3Bpbm5lci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRTBCO0FBTVgsU0FBU0MsZUFBZSxFQUFFQyxPQUFPLFFBQVEsRUFBdUI7SUFDN0UsTUFBTUMsY0FBYztRQUNsQkMsT0FBTztRQUNQQyxRQUFRO1FBQ1JDLE9BQU87SUFDVDtJQUVBLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVO2tCQUNiLDRFQUFDRDtZQUFJQyxXQUFXLEdBQUdMLFdBQVcsQ0FBQ0QsS0FBSyxDQUFDLDJEQUEyRCxDQUFDOzs7Ozs7Ozs7OztBQUd2RyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxwY1xcT25lRHJpdmVcXERlc2t0b3BcXERlc2t0b3BcXFNvbHludGFfV2Vic2l0ZVxcZnJvbnRlbmRcXGxlc3Nvbi1wbGF0Zm9ybVxcc3JjXFxjb21wb25lbnRzXFx1aVxcTG9hZGluZ1NwaW5uZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcclxuXHJcbmltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XHJcblxyXG5pbnRlcmZhY2UgTG9hZGluZ1NwaW5uZXJQcm9wcyB7XHJcbiAgc2l6ZT86ICdzbWFsbCcgfCAnbWVkaXVtJyB8ICdsYXJnZSc7XHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIExvYWRpbmdTcGlubmVyKHsgc2l6ZSA9ICdtZWRpdW0nIH06IExvYWRpbmdTcGlubmVyUHJvcHMpIHtcclxuICBjb25zdCBzaXplQ2xhc3NlcyA9IHtcclxuICAgIHNtYWxsOiAndy00IGgtNCBib3JkZXItMicsXHJcbiAgICBtZWRpdW06ICd3LTggaC04IGJvcmRlci0zJyxcclxuICAgIGxhcmdlOiAndy0xMiBoLTEyIGJvcmRlci00J1xyXG4gIH07XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1jZW50ZXIgaXRlbXMtY2VudGVyXCI+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPXtgJHtzaXplQ2xhc3Nlc1tzaXplXX0gYm9yZGVyLWdyYXktMzAwIGJvcmRlci10LXByaW1hcnkgcm91bmRlZC1mdWxsIGFuaW1hdGUtc3BpbmB9PjwvZGl2PlxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufSJdLCJuYW1lcyI6WyJSZWFjdCIsIkxvYWRpbmdTcGlubmVyIiwic2l6ZSIsInNpemVDbGFzc2VzIiwic21hbGwiLCJtZWRpdW0iLCJsYXJnZSIsImRpdiIsImNsYXNzTmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/LoadingSpinner.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useSessionSimple.tsx":
/*!****************************************!*\
  !*** ./src/hooks/useSessionSimple.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionContext: () => (/* binding */ SessionContext),\n/* harmony export */   useSession: () => (/* binding */ useSession)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_authService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/authService */ \"(ssr)/./src/lib/authService.ts\");\n/* __next_internal_client_entry_do_not_use__ useSession,SessionContext auto */ \n // Import real auth service\n// Simplified session context for the frontend diagnostic fix\nconst SessionContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(undefined);\nfunction useSession() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(SessionContext);\n    if (context === undefined) {\n        // Instead of throwing an error immediately, return a fallback object\n        console.warn('useSession called outside of SessionProvider, returning fallback values');\n        return {\n            backendSessionId: null,\n            user: null,\n            setUserSession: ()=>{},\n            setBackendSessionId: ()=>{},\n            clearSession: ()=>{},\n            isReady: false,\n            isLoading: true,\n            getAuthHeaders: ()=>(0,_lib_authService__WEBPACK_IMPORTED_MODULE_1__.getAuthHeaders)(null),\n            userRole: null\n        };\n    }\n    return context;\n}\n// Export the context for providers to use\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useSessionSimple.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/authService.ts":
/*!********************************!*\
  !*** ./src/lib/authService.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CURRENT_SESSION_KEY: () => (/* binding */ CURRENT_SESSION_KEY),\n/* harmony export */   clearAuthData: () => (/* binding */ clearAuthData),\n/* harmony export */   findUserByUserId: () => (/* binding */ findUserByUserId),\n/* harmony export */   getAuthHeaders: () => (/* binding */ getAuthHeaders),\n/* harmony export */   getFreshAuthHeaders: () => (/* binding */ getFreshAuthHeaders),\n/* harmony export */   getUserRole: () => (/* binding */ getUserRole),\n/* harmony export */   getUserSession: () => (/* binding */ getUserSession),\n/* harmony export */   refreshAuthToken: () => (/* binding */ refreshAuthToken),\n/* harmony export */   saveUserSession: () => (/* binding */ saveUserSession),\n/* harmony export */   setupAuthListener: () => (/* binding */ setupAuthListener),\n/* harmony export */   setupAuthStateListener: () => (/* binding */ setupAuthStateListener),\n/* harmony export */   signInWithEmailAndPassword: () => (/* binding */ signInWithEmailAndPassword),\n/* harmony export */   signOut: () => (/* binding */ signOut),\n/* harmony export */   syncAuthState: () => (/* binding */ syncAuthState)\n/* harmony export */ });\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./firebase */ \"(ssr)/./src/lib/firebase.ts\");\n// lib/authService.ts\n/* __next_internal_client_entry_do_not_use__ CURRENT_SESSION_KEY,saveUserSession,getUserSession,clearAuthData,signInWithEmailAndPassword,signOut,setupAuthStateListener,setupAuthListener,getAuthHeaders,getFreshAuthHeaders,refreshAuthToken,findUserByUserId,getUserRole,syncAuthState auto */ \n\n\n// Constants\nconst SESSION_KEY = 'user_session';\nconst CURRENT_SESSION_KEY = 'current_session'; // Export this constant\nconst TOKEN_KEY = 'token';\n/**\r\n * Save user session to localStorage with consistent keys\r\n */ const saveUserSession = (session)=>{\n    if (!session || !session.uid) return;\n    try {\n        // Add timestamp before saving\n        const sessionToSave = {\n            ...session,\n            tokenTimestamp: Date.now()\n        };\n        // Save the full session object with timestamp\n        localStorage.setItem(SESSION_KEY, JSON.stringify(sessionToSave));\n        // CURRENT_SESSION_KEY should be set explicitly elsewhere when the *backend* session ID is known.\n        // Do not automatically set it to the Firebase UID here.\n        // localStorage.setItem(CURRENT_SESSION_KEY, session.uid); // Removed this line\n        localStorage.setItem(TOKEN_KEY, session.token); // Keep saving the token\n        console.log('Session object saved for UID:', session.uid);\n    } catch (error) {\n        console.error('Error saving user session:', error);\n    }\n};\n/**\r\n * Get the current user session from localStorage\r\n */ const getUserSession = ()=>{\n    try {\n        const sessionStr = localStorage.getItem(SESSION_KEY);\n        if (!sessionStr) return null;\n        return JSON.parse(sessionStr);\n    } catch (error) {\n        console.error('Failed to parse user session:', error);\n        return null;\n    }\n};\n/**\r\n * Clear all auth-related data from localStorage\r\n */ const clearAuthData = ()=>{\n    try {\n        localStorage.removeItem(SESSION_KEY);\n        localStorage.removeItem(TOKEN_KEY);\n        localStorage.removeItem(CURRENT_SESSION_KEY);\n        localStorage.removeItem('authMethod');\n        localStorage.removeItem('viewing_as_child');\n        localStorage.removeItem('parent_id');\n        localStorage.removeItem('parent_name');\n        localStorage.removeItem('user_name');\n        localStorage.removeItem('parentEnrollmentMessage');\n    } catch (error) {\n        console.error('Error clearing auth data:', error);\n    }\n};\n/**\r\n * Sign in with email and password\r\n */ const signInWithEmailAndPassword = async (email, password)=>{\n    // Clear any previous auth state first\n    await signOut();\n    console.log(\"Attempting email/password sign-in\");\n    try {\n        // Use Firebase's email/password auth\n        const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.signInWithEmailAndPassword)(_firebase__WEBPACK_IMPORTED_MODULE_2__.auth, email, password);\n        const user = userCredential.user;\n        // Get fresh token with custom claims\n        const additionalClaims = {\n            student_id: localStorage.getItem('viewing_as_child') || undefined\n        };\n        const tokenResult = await user.getIdTokenResult(true);\n        const token = await _firebase__WEBPACK_IMPORTED_MODULE_2__.auth.currentUser?.getIdToken(true, additionalClaims);\n        // Get user details from Firestore\n        const userDetails = await getUserDetailsFromFirestore(user);\n        // Create session\n        const userSession = {\n            uid: user.uid,\n            email: user.email,\n            name: user.displayName || userDetails?.name || null,\n            token: token,\n            role: userDetails?.role\n        };\n        // Save session\n        saveUserSession(userSession);\n        console.log(\"Authentication successful\");\n        return userSession;\n    } catch (error) {\n        console.error(\"Authentication error:\", error);\n        throw error;\n    }\n};\n/**\r\n * Sign out the current user\r\n */ const signOut = async ()=>{\n    try {\n        await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.signOut)(_firebase__WEBPACK_IMPORTED_MODULE_2__.auth);\n        clearAuthData();\n        console.log(\"User signed out\");\n    } catch (error) {\n        console.error(\"Sign out error:\", error);\n        throw error;\n    }\n};\n/**\r\n * Set up a listener for auth state changes\r\n */ const setupAuthStateListener = (callback)=>{\n    return (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.onAuthStateChanged)(_firebase__WEBPACK_IMPORTED_MODULE_2__.auth, callback);\n};\n/**\r\n * Setup auth listener used by the session provider\r\n * This matches the signature expected by useSession\r\n */ const setupAuthListener = (setSession, setError, setIsLoading)=>{\n    return (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.onAuthStateChanged)(_firebase__WEBPACK_IMPORTED_MODULE_2__.auth, async (user)=>{\n        console.log(\"Auth state changed:\", user ? `User ${user.uid}` : \"No user\");\n        if (!user) {\n            setSession(null);\n            setIsLoading(false);\n            return;\n        }\n        try {\n            // Get fresh token for signed-in user\n            // Get custom claims including student_id if viewing as parent\n            const additionalClaims = {\n                student_id: localStorage.getItem('viewing_as_child') || undefined\n            };\n            const tokenResult = await user.getIdTokenResult(true);\n            const tokenString = await _firebase__WEBPACK_IMPORTED_MODULE_2__.auth.currentUser?.getIdToken(true, additionalClaims);\n            if (!tokenString) {\n                throw new Error('Failed to get authentication token');\n            }\n            // Get user details from Firestore\n            const userDetails = await getUserDetailsFromFirestore(user);\n            // Create session object with token string\n            const userSession = {\n                uid: user.uid,\n                email: user.email || '',\n                name: user.displayName || userDetails?.name || '',\n                token: tokenString,\n                tokenResult,\n                role: userDetails?.role\n            };\n            // Set session and store backend session ID if available\n            setSession(userSession);\n            // If this is a new login response with sessionId, store it\n            const responseSessionId = user.sessionId;\n            if (responseSessionId) {\n                localStorage.setItem(CURRENT_SESSION_KEY, responseSessionId);\n            }\n        } catch (error) {\n            console.error(\"Error getting auth token:\", error);\n            setError(\"Failed to authenticate session\");\n            setSession(null);\n        } finally{\n            setIsLoading(false);\n        }\n    });\n};\n/**\r\n * Get auth headers for API requests\r\n * Accepts backendSessionId from context to avoid localStorage race conditions.\r\n */ const getAuthHeaders = (backendSessionIdFromContext)=>{\n    const headers = {\n        'Content-Type': 'application/json'\n    };\n    const currentUser = _firebase__WEBPACK_IMPORTED_MODULE_2__.auth.currentUser;\n    let currentToken = null;\n    let currentUid = null;\n    if (currentUser) {\n        currentUid = currentUser.uid;\n        // Attempt to get token from Firebase auth state first\n        currentToken = currentUser.stsTokenManager?.accessToken || null;\n    }\n    const storedSession = getUserSession();\n    // Prefer the ID passed from context, fall back to localStorage only if necessary (e.g., during initial load before context is ready)\n    const effectiveBackendSessionId = backendSessionIdFromContext || localStorage.getItem(CURRENT_SESSION_KEY);\n    // Use the effective backend session ID for the Session-ID header\n    if (effectiveBackendSessionId) {\n        headers['Session-ID'] = effectiveBackendSessionId;\n    } else {\n        // Fallback to UID only if backend session ID isn't available anywhere\n        const effectiveUid = currentUid || storedSession?.uid;\n        if (effectiveUid) {\n            console.warn(`Using UID (${effectiveUid}) as Session-ID header fallback. Backend session ID not found in context or localStorage ('${CURRENT_SESSION_KEY}').`);\n            headers['Session-ID'] = effectiveUid; // Still might be wrong, but it's the last resort\n        } else {\n            console.error(\"Cannot set Session-ID header: No backend session ID or user UID found.\");\n        }\n    }\n    // Prefer token from context's stored session if Firebase token is missing\n    const effectiveToken = currentToken || storedSession?.token;\n    if (effectiveToken) {\n        headers['Authorization'] = `Bearer ${effectiveToken}`;\n    } else {\n        console.warn(\"Authorization token not found in Firebase state or stored session. This may cause authentication errors.\");\n        // Instead of completely failing, let's try to get token from localStorage as last resort\n        const fallbackToken = localStorage.getItem('token');\n        if (fallbackToken) {\n            console.warn(\"Using fallback token from localStorage\");\n            headers['Authorization'] = `Bearer ${fallbackToken}`;\n        } else {\n            console.error(\"No authentication token available from any source.\");\n        }\n    }\n    // Get role from stored session if available\n    const effectiveRole = storedSession?.role || 'student'; // Default role if not found\n    headers['X-User-Role'] = effectiveRole;\n    return headers;\n};\n/**\r\n * Get fresh auth headers with token refresh\r\n * Accepts backendSessionId from context.\r\n */ const getFreshAuthHeaders = async (backendSessionIdFromContext)=>{\n    const headers = {\n        'Content-Type': 'application/json'\n    };\n    const currentUser = _firebase__WEBPACK_IMPORTED_MODULE_2__.auth.currentUser;\n    if (currentUser) {\n        try {\n            const token = await currentUser.getIdToken(true); // Force refresh\n            headers['Authorization'] = `Bearer ${token}`;\n            // Use the effective backend session ID for the Session-ID header\n            const effectiveBackendSessionId = backendSessionIdFromContext || localStorage.getItem(CURRENT_SESSION_KEY);\n            if (effectiveBackendSessionId) {\n                headers['Session-ID'] = effectiveBackendSessionId;\n            } else {\n                // Fallback to UID only if backend session ID isn't available anywhere\n                console.warn(`Using UID (${currentUser.uid}) as Session-ID header fallback during fresh token request. Backend session ID not found.`);\n                headers['Session-ID'] = currentUser.uid; // Last resort\n            }\n            const storedSession = getUserSession();\n            headers['X-User-Role'] = storedSession?.role || 'student'; // Default role\n        } catch (error) {\n            console.error(\"Error getting fresh token:\", error);\n            // Fallback to non-fresh headers if refresh fails\n            return getAuthHeaders(backendSessionIdFromContext);\n        }\n    } else {\n        // If no current user, return standard (likely unauthenticated) headers\n        return getAuthHeaders(backendSessionIdFromContext);\n    }\n    return headers;\n};\n/**\r\n * Refresh the auth token\r\n */ const refreshAuthToken = async ()=>{\n    try {\n        const currentUser = _firebase__WEBPACK_IMPORTED_MODULE_2__.auth.currentUser;\n        if (!currentUser) {\n            console.error(\"No current user found for token refresh\");\n            return null;\n        }\n        // Force refresh the token\n        // Maintain custom claims during refresh\n        const additionalClaims = {\n            student_id: localStorage.getItem('viewing_as_child') || undefined\n        };\n        const newToken = await currentUser.getIdToken(true, additionalClaims);\n        // Update the stored session with new token\n        const storedSession = getUserSession();\n        if (storedSession) {\n            const updatedSession = {\n                ...storedSession,\n                token: newToken\n            };\n            saveUserSession(updatedSession);\n        }\n        return newToken;\n    } catch (error) {\n        console.error(\"Failed to refresh authentication token:\", error);\n        return null;\n    }\n};\n/**\r\n * Get user details from Firestore\r\n */ async function getUserDetailsFromFirestore(user) {\n    try {\n        const db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getFirestore)();\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(db, 'users', user.uid);\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)(userRef);\n        if (userDoc.exists()) {\n            const data = userDoc.data();\n            return {\n                name: data.name,\n                role: data.role,\n                children: data.children || [],\n                parents: data.parents || []\n            };\n        }\n        // Fallback to check parents collection if not found in users\n        const parentRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(db, 'parents', user.uid);\n        const parentDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)(parentRef);\n        if (parentDoc.exists()) {\n            const data = parentDoc.data();\n            return {\n                name: data.name,\n                role: 'parent',\n                children: data.children || []\n            };\n        }\n        // Fallback to check students collection\n        const studentRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(db, 'students', user.uid);\n        const studentDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)(studentRef);\n        if (studentDoc.exists()) {\n            const data = studentDoc.data();\n            return {\n                name: data.name,\n                role: 'student',\n                parents: data.parents || []\n            };\n        }\n        return null;\n    } catch (error) {\n        console.error(\"Error fetching user details:\", error);\n        return null;\n    }\n}\n/**\r\n * Find user by userId (for child accounts)\r\n */ const findUserByUserId = async (userId)=>{\n    try {\n        const db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getFirestore)();\n        const usersRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(db, 'users');\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)(usersRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('userId', '==', userId));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(q);\n        if (querySnapshot.empty) {\n            return null;\n        }\n        return querySnapshot.docs[0].data().email;\n    } catch (error) {\n        console.error(\"Error finding user by userId:\", error);\n        return null;\n    }\n};\n/**\r\n * Get user role from Firestore\r\n */ const getUserRole = async (uid)=>{\n    try {\n        const db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getFirestore)();\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(db, 'users', uid);\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)(userRef);\n        if (userDoc.exists() && userDoc.data().role) {\n            return userDoc.data().role;\n        }\n        return null;\n    } catch (error) {\n        console.error(\"Error getting user role:\", error);\n        return null;\n    }\n};\n/**\r\n * Sync Firebase auth state with local storage\r\n * This is crucial to fix the state mismatch issues\r\n */ const syncAuthState = async ()=>{\n    const currentUser = _firebase__WEBPACK_IMPORTED_MODULE_2__.auth.currentUser;\n    const storedSession = getUserSession();\n    // Case 1: Firebase has user but localStorage doesn't\n    if (currentUser && (!storedSession || storedSession.uid !== currentUser.uid)) {\n        console.log(\"Syncing: Firebase has user but localStorage doesn't match\");\n        const token = await currentUser.getIdToken(true);\n        const userDetails = await getUserDetailsFromFirestore(currentUser);\n        const userSession = {\n            uid: currentUser.uid,\n            email: currentUser.email,\n            name: currentUser.displayName || userDetails?.name || null,\n            token: token,\n            role: userDetails?.role\n        };\n        saveUserSession(userSession);\n        return userSession;\n    }\n    // Case 2: Firebase has no user but localStorage does\n    if (!currentUser && storedSession) {\n        console.log(\"Syncing: Firebase has no user but localStorage does\");\n        clearAuthData();\n        return null;\n    }\n    // Case 3: Both have matching user, check if token needs refresh\n    if (currentUser && storedSession && currentUser.uid === storedSession.uid) {\n        console.log(\"Syncing: Both have matching user\");\n        // Token is older than 30 minutes, refresh it\n        const tokenDate = new Date(storedSession.tokenTimestamp || 0);\n        const now = new Date();\n        const diffMinutes = (now.getTime() - tokenDate.getTime()) / (1000 * 60);\n        if (diffMinutes > 30) {\n            console.log(\"Token is older than 30 minutes, refreshing\");\n            await refreshAuthToken();\n        }\n    }\n    return storedSession;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/authService.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/firebase.ts":
/*!*****************************!*\
  !*** ./src/lib/firebase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   app: () => (/* binding */ app),\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   storage: () => (/* binding */ storage)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"(ssr)/./node_modules/firebase/app/dist/index.mjs\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(ssr)/./node_modules/firebase/storage/dist/index.mjs\");\n// lib/firebase.ts\n/* __next_internal_client_entry_do_not_use__ app,auth,db,storage auto */ \n\n\n\n// Default Firebase configuration for development\nconst devConfig = {\n    apiKey: \"AIzaSyDWVM8PvcWD4nAkpsI7FuDKCvpp_PEnPlU\",\n    authDomain: \"solynta-academy.firebaseapp.com\",\n    projectId: \"solynta-academy\",\n    storageBucket: \"solynta-academy.firebasestorage.app\",\n    messagingSenderId: \"914922463191\",\n    appId: \"1:914922463191:web:b6e9c737dba77a26643592\",\n    measurementId: \"G-ZVC7R06Y33\"\n};\n// Firebase configuration - try environment variables first, then fallback to dev config\nconst firebaseConfig = {\n    apiKey: \"AIzaSyDWVM8PvcWD4nAkpsI7FuDKCvpp_PEnPlU\" || 0,\n    authDomain: \"solynta-academy.firebaseapp.com\" || 0,\n    projectId: \"solynta-academy\" || 0,\n    storageBucket: \"solynta-academy.firebasestorage.app\" || 0,\n    messagingSenderId: \"914922463191\" || 0,\n    appId: \"1:914922463191:web:b6e9c737dba77a26643592\" || 0,\n    measurementId: \"G-ZVC7R06Y33\" || 0\n};\nconsole.log('Using Firebase config with project ID:', firebaseConfig.projectId);\n// Initialize Firebase app (Singleton pattern)\nconst app = !(0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApps)().length ? (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig) : (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApp)();\n// Initialize services - these will be initialized client-side\nlet auth;\nlet db;\nlet storage;\n// Check if running in a browser environment\nif (false) {} else {\n    // Provide non-functional placeholders for SSR/server environments\n    // This prevents errors during import but these services won't work server-side\n    // Assigning {} as Type might cause issues if methods are called server-side.\n    // A more robust approach might involve providing mock implementations or\n    // ensuring these exports are only used in client components.\n    auth = {};\n    db = {};\n    storage = {};\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/firebase.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   extractContextFromRequest: () => (/* binding */ extractContextFromRequest),\n/* harmony export */   fetchWithErrorHandling: () => (/* binding */ fetchWithErrorHandling),\n/* harmony export */   formatGradeLevelForDisplay: () => (/* binding */ formatGradeLevelForDisplay),\n/* harmony export */   getParentId: () => (/* binding */ getParentId),\n/* harmony export */   mapFrontendToBackendFields: () => (/* binding */ mapFrontendToBackendFields),\n/* harmony export */   normalizeGradeLevel: () => (/* binding */ normalizeGradeLevel),\n/* harmony export */   prepareBackendRequest: () => (/* binding */ prepareBackendRequest),\n/* harmony export */   returnToParentAccount: () => (/* binding */ returnToParentAccount),\n/* harmony export */   switchToChildAccount: () => (/* binding */ switchToChildAccount)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.m.js\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\n/**\r\n * Utility function to combine Tailwind CSS class names intelligently.\r\n * Handles merging and conflict resolution.\r\n * @param inputs Class values to combine.\r\n * @returns Merged class string.\r\n */ function cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\r\n * Formats a grade level string for display.\r\n * Handles formats like \"primary-5\", \"jss1\", \"SSS2\", \"jss-1-3\", etc.\r\n * @param gradeLevelInput Raw grade level string (e.g., \"primary-5\", \"jss1\")\r\n * @returns Formatted string (e.g., \"Primary 5\", \"Junior Secondary School 1\", \"Junior Secondary School 1-3\") or the original input if no specific format matches.\r\n */ function formatGradeLevelForDisplay(gradeLevelInput) {\n    if (!gradeLevelInput) {\n        return 'N/A'; // Or return empty string ''\n    }\n    const input = gradeLevelInput.trim().toLowerCase();\n    // Handle \"primary-X\" format\n    if (input.startsWith('primary-')) {\n        const parts = input.split('-');\n        if (parts.length === 2 && !isNaN(parseInt(parts[1], 10))) {\n            return `Primary ${parts[1]}`;\n        }\n    }\n    // Handle specific range case \"jss-1-3\" first\n    if (input === 'jss-1-3') {\n        return 'Junior Secondary School 1-3';\n    }\n    // Handle \"jssX\" or \"jss-X\" format (single number)\n    if (input.startsWith('jss')) {\n        const numPart = input.replace('jss', '').replace('-', '').trim();\n        if (!isNaN(parseInt(numPart, 10))) {\n            return `Junior Secondary School ${numPart}`;\n        }\n    }\n    // Handle specific range case \"sss-1-3\" first\n    if (input === 'sss-1-3') {\n        return 'Senior Secondary School 1-3';\n    }\n    // Handle \"sssX\" or \"sss-X\" format (single number)\n    if (input.startsWith('sss')) {\n        const numPart = input.replace('sss', '').replace('-', '').trim();\n        if (!isNaN(parseInt(numPart, 10))) {\n            return `Senior Secondary School ${numPart}`;\n        }\n    }\n    // Add more specific rules if needed (e.g., \"P5\" -> \"Primary 5\")\n    if (input === 'p5') {\n        return 'Primary 5';\n    }\n    // Add more rules like the one above for P1, P2, etc. if needed\n    // Fallback: Return the original input string if no rules matched\n    // Consider capitalizing if appropriate as a fallback\n    // return gradeLevelInput.charAt(0).toUpperCase() + gradeLevelInput.slice(1);\n    return gradeLevelInput;\n}\n/**\r\n * Normalizes various grade level input strings to a consistent internal format.\r\n * Example: \"Primary 5\" -> \"primary-5\", \"Junior Secondary School 1\" -> \"jss1\"\r\n * @param gradeLevelInput Raw or display grade level string.\r\n * @returns Normalized string (lowercase, specific format) or original lowercase if no rule matches.\r\n */ function normalizeGradeLevel(gradeLevelInput) {\n    if (!gradeLevelInput) return '';\n    const input = gradeLevelInput.trim().toLowerCase();\n    // Normalization rules\n    if (input.startsWith('primary')) {\n        const numPart = input.replace('primary', '').trim();\n        if (!isNaN(parseInt(numPart, 10))) {\n            return `primary-${numPart}`; // \"primary 5\" -> \"primary-5\"\n        }\n    }\n    if (input.startsWith('junior secondary school')) {\n        // Handle range first\n        if (input === 'junior secondary school 1-3') return 'jss-1-3';\n        const numPart = input.replace('junior secondary school', '').trim();\n        if (!isNaN(parseInt(numPart, 10))) {\n            return `jss${numPart}`; // \"Junior Secondary School 1\" -> \"jss1\"\n        }\n    }\n    if (input.startsWith('senior secondary school')) {\n        // Handle range first\n        if (input === 'senior secondary school 1-3') return 'sss-1-3';\n        const numPart = input.replace('senior secondary school', '').trim();\n        if (!isNaN(parseInt(numPart, 10))) {\n            return `sss${numPart}`; // \"Senior Secondary School 2\" -> \"sss2\"\n        }\n    }\n    // Add more normalization rules as needed (e.g., p5 -> primary-5)\n    if (input === 'p5') return 'primary-5';\n    // Add rules for p1, p2, etc.\n    // Return input directly if it already matches a normalized format or no rule applies\n    return input;\n}\n// lib/api-utils.ts\n/**\r\n * Helper function to fetch data with built-in error handling\r\n */ async function fetchWithErrorHandling(url, options) {\n    try {\n        console.log(`Fetching: ${url}`);\n        const response = await fetch(url, options);\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error(`API Error (${response.status}):`, errorText);\n            throw new Error(`API request failed with status ${response.status}: ${response.statusText}`);\n        }\n        const data = await response.json();\n        console.log('API Response:', data);\n        return data;\n    } catch (error) {\n        console.error('Fetch error:', error);\n        throw error;\n    }\n}\n/**\r\n * Helper to get parent ID from localStorage\r\n */ function getParentId() {\n    return localStorage.getItem('parent_id') || localStorage.getItem('current_session');\n}\n/**\r\n * Helper to switch to a child's account (for parent viewing)\r\n */ function switchToChildAccount(childId, parentId) {\n    // Store the child's session ID in localStorage\n    localStorage.setItem('current_session', childId);\n    // Set parent ID for returning back\n    localStorage.setItem('parent_id', parentId);\n    // Set flag to indicate parent is viewing child's dashboard\n    localStorage.setItem('viewing_as_child', 'true');\n    // Store a message to display on the child's dashboard\n    localStorage.setItem('parentEnrollmentMessage', \"You're viewing your child's dashboard. All actions will be performed on their behalf.\");\n}\n/**\r\n * Helper to switch back to parent account\r\n */ function returnToParentAccount() {\n    const parentId = localStorage.getItem('parent_id');\n    if (parentId) {\n        // Clear the viewing_as_child flag\n        localStorage.removeItem('viewing_as_child');\n        // Set the parent ID as the current session\n        localStorage.setItem('current_session', parentId);\n        // Clear the parent message\n        localStorage.removeItem('parentEnrollmentMessage');\n    }\n}\n/**\r\n * Extracts context parameters from a request, including URL parameters and headers.\r\n * Used to properly pass required data to backend APIs.\r\n */ function extractContextFromRequest(request) {\n    const url = new URL(request.url);\n    return {\n        studentId: request.headers.get('X-Student-ID') || url.searchParams.get('studentId'),\n        country: request.headers.get('X-Country') || url.searchParams.get('country'),\n        curriculum: request.headers.get('X-Curriculum') || url.searchParams.get('curriculum'),\n        grade: request.headers.get('X-Grade') || url.searchParams.get('grade'),\n        level: request.headers.get('X-Level') || url.searchParams.get('level'),\n        subject: request.headers.get('X-Subject') || url.searchParams.get('subject')\n    };\n}\n/**\r\n * Maps frontend field names to backend field names for API requests.\r\n * Helps maintain consistent API communication when field naming differs.\r\n */ function mapFrontendToBackendFields(data) {\n    const fieldMapping = {\n        'lesson_ref': 'lessonRef',\n        'sessionId': 'session_id'\n    };\n    const result = {\n        ...data\n    };\n    // Apply field mappings\n    Object.entries(data).forEach(([key, value])=>{\n        if (fieldMapping[key] && !result[fieldMapping[key]]) {\n            result[fieldMapping[key]] = value;\n        }\n    });\n    // Ensure critical fields are present\n    if (data.lesson_ref && !result.lessonRef) {\n        result.lessonRef = data.lesson_ref;\n    }\n    return result;\n}\n/**\r\n * Prepares a standardized request body for backend APIs, ensuring all required parameters are included.\r\n */ function prepareBackendRequest(request, body) {\n    // Extract context parameters\n    const context = extractContextFromRequest(request);\n    // Map frontend fields to backend fields\n    const mappedData = mapFrontendToBackendFields(body);\n    // Combine data with context parameters\n    return {\n        ...mappedData,\n        lessonRef: mappedData.lessonRef || body.lesson_ref,\n        student_id: context.studentId || mappedData.studentId || body.studentId,\n        country: context.country || mappedData.country || body.country,\n        curriculum: context.curriculum || mappedData.curriculum || body.curriculum,\n        grade: context.grade || mappedData.grade || body.grade,\n        level: context.level || mappedData.level || body.level,\n        subject: context.subject || mappedData.subject || body.subject\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("http2");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@opentelemetry","vendor-chunks/@babel","vendor-chunks/@firebase","vendor-chunks/protobufjs","vendor-chunks/framer-motion","vendor-chunks/long","vendor-chunks/@react-aria","vendor-chunks/@protobufjs","vendor-chunks/lodash.camelcase","vendor-chunks/tslib","vendor-chunks/@grpc","vendor-chunks/idb","vendor-chunks/motion-utils","vendor-chunks/next-themes","vendor-chunks/@nextui-org","vendor-chunks/@swc","vendor-chunks/firebase","vendor-chunks/tailwind-merge","vendor-chunks/class-variance-authority","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fstart-lesson%2Fpage&page=%2Fstart-lesson%2Fpage&appPaths=%2Fstart-lesson%2Fpage&pagePath=private-next-app-dir%2Fstart-lesson%2Fpage.tsx&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();