[2025-06-17 14:25:43,720] INFO - __main__ - main.py:562 - Logging configuration complete with immediate console output
[2025-06-17 14:25:43,724] INFO - __main__ - main.py:638 - INIT_INFO: Flask app instance created and CORS configured.
[2025-06-17 14:25:43,725] INFO - __main__ - main.py:817 - INIT_INFO: Flask app.secret_key SET from FLASK_SECRET_KEY environment variable.
[2025-06-17 14:25:43,733] INFO - __main__ - main.py:848 - Phase transition fixes imported successfully
[2025-06-17 14:25:43,737] WARNING - __main__ - main.py:3083 - Could not import from 'utils'. Using placeholder functions.
[2025-06-17 14:25:43,743] INFO - __main__ - main.py:3529 - FLASK: Using unified Firebase initialization approach...
[2025-06-17 14:25:43,745] INFO - unified_firebase_init - unified_firebase_init.py:90 - Attempting Firebase initialization with: firebase-adminsdk-service-key.json
[2025-06-17 14:25:43,799] INFO - unified_firebase_init - unified_firebase_init.py:95 - ✅ Firebase initialized successfully with: firebase-adminsdk-service-key.json
[2025-06-17 14:25:43,799] INFO - unified_firebase_init - unified_firebase_init.py:121 - Testing Firestore connectivity with lightweight operation...
[2025-06-17 14:25:44,638] DEBUG - google.auth.transport.requests - requests.py:185 - Making request: POST https://oauth2.googleapis.com/token
[2025-06-17 14:25:44,641] DEBUG - urllib3.connectionpool - connectionpool.py:1022 - Starting new HTTPS connection (1): oauth2.googleapis.com:443
[2025-06-17 14:25:45,825] DEBUG - urllib3.connectionpool - connectionpool.py:475 - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 200 None
[2025-06-17 14:25:46,204] INFO - unified_firebase_init - unified_firebase_init.py:165 - ✅ Firestore connected successfully - connectivity test passed
[2025-06-17 14:25:46,205] INFO - __main__ - main.py:3537 - FLASK: Unified Firebase initialization successful - Firestore client ready
[2025-06-17 14:25:46,206] INFO - __main__ - main.py:3627 - Gemini API will be initialized on first use (lazy loading).
[2025-06-17 14:25:46,277] INFO - __main__ - main.py:13673 - Starting Lesson Manager Service...
[2025-06-17 14:25:46,278] INFO - __main__ - main.py:13679 - Flask server starting on host 0.0.0.0, port 5000
[2025-06-17 14:25:46,280] INFO - __main__ - main.py:13680 - Debug mode: ON
[2025-06-17 14:25:46,392] INFO - werkzeug - _internal.py:97 - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
[2025-06-17 14:25:46,393] INFO - werkzeug - _internal.py:97 - [33mPress CTRL+C to quit[0m
[2025-06-17 14:26:26,321] INFO - __main__ - main.py:4831 - REQUEST: POST http://localhost:5000/lesson-content -> endpoint: lesson_content_and_quiz
[2025-06-17 14:26:26,322] INFO - __main__ - main.py:4874 - Incoming request: {"request_id": "7c610ceb-48ce-46d0-9654-ed427b468cbf", "timestamp": "2025-06-17T13:26:26.322267+00:00", "method": "POST", "path": "/lesson-content", "ip": "127.0.0.1", "user_agent": "axios/1.9.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-AI-001", "subject": "Artificial Intelligence", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "Primary 5", "level": "P5", "current_phase": "diagnostic_start_probe"}}
[2025-06-17 14:26:26,323] INFO - __main__ - main.py:10185 - [RAW HEADERS /lesson-content] Received Headers: {'Accept': 'application/json, text/plain, */*', 'Content-Type': 'application/json', 'X-Student-Id': 'andrea_ugono_33305', 'Authorization': 'Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6ImE0YTEwZGVjZTk4MzY2ZDZmNjNlMTY3Mjg2YWU5YjYxMWQyYmFhMjciLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.L9K0a5SAik26Jil8noRp-oCaFeGgL0b2D6b2MdXWINrnnv2TGxWG7nE17qS4e7selFJTMB02DQQATIXQtFHtCna51T51Z_ZVXdujvjuehGq-tYaEmv-VcfSzXw8QlmfzBPQ1NivU8pzNAcCLFZVmPRwZOcjGdXVP8dgYvIoSWBjapNkEJ9Pz2YS1aY5ckzuC1Y34PEfX0RYjhTbdbBz9yvPPZVkCGkceqmYoGrXg-tkOdwB2mSj9ga8EXNZTmVqucWcRVdJOqr4QqRLHtRfi_uwaApQTNqD6pMHuhn08qLR61qlArKYZjUkvIv-HqZXZrBZkRpryduLE24ucsZnzjQ', 'User-Agent': 'axios/1.9.0', 'Content-Length': '225', 'Accept-Encoding': 'gzip, compress, deflate, br', 'Host': 'localhost:5000', 'Connection': 'keep-alive'}
[2025-06-17 14:26:26,324] INFO - temporary_auth_bypass - temporary_auth_bypass.py:29 - [7c610ceb-48ce-46d0-9654-ed427b468cbf] Using temporary auth bypass with mock token
[2025-06-17 14:26:26,324] WARNING - __main__ - main.py:10192 - 🔥 LESSON-CONTENT ENDPOINT CALLED!
[2025-06-17 14:26:26,325] INFO - __main__ - main.py:10199 - [7c610ceb-48ce-46d0-9654-ed427b468cbf] lesson_content_and_quiz invoked by /lesson-content
[2025-06-17 14:26:26,325] INFO - __main__ - main.py:10215 - Lesson content request by authenticated user: andrea_ugono_33305 (Andrea Ugono)
[2025-06-17 14:26:26,325] INFO - __main__ - main.py:10219 - !!! [lesson_content_and_quiz] [7c610ceb-48ce-46d0-9654-ed427b468cbf] RAW BODY AT START (bytes length: 225): b'{"student_id":"andrea_ugono_33305","lesson_ref":"P5-AI-001","subject":"Artificial Intelligence","country":"Nigeria","curriculum":"National Curriculum","grade":"Primary 5","level":"P5","current_phase":"diagnostic_start_probe"}'
[2025-06-17 14:26:26,326] INFO - __main__ - main.py:1122 - [7c610ceb-48ce-46d0-9654-ed427b468cbf] fetch_lesson_data: Fetching lesson with parameters:
[2025-06-17 14:26:26,326] INFO - __main__ - main.py:1123 -   • Country: Nigeria
[2025-06-17 14:26:26,327] INFO - __main__ - main.py:1124 -   • Curriculum: National Curriculum
[2025-06-17 14:26:26,327] INFO - __main__ - main.py:1125 -   • Grade: Primary 5
[2025-06-17 14:26:26,328] INFO - __main__ - main.py:1126 -   • Level: P5
[2025-06-17 14:26:26,329] INFO - __main__ - main.py:1127 -   • Subject: Artificial Intelligence
[2025-06-17 14:26:26,330] INFO - __main__ - main.py:1128 -   • Lesson ID: P5-AI-001
[2025-06-17 14:26:26,331] INFO - __main__ - main.py:1147 - [7c610ceb-48ce-46d0-9654-ed427b468cbf] Attempting primary path: countries/Nigeria/curriculums/National Curriculum/grades/Primary 5/levels/P5/subjects/Artificial Intelligence/lessonRef/P5-AI-001
[2025-06-17 14:26:26,678] INFO - __main__ - main.py:1211 - [7c610ceb-48ce-46d0-9654-ed427b468cbf] Successfully retrieved document with keys: ['additionalNotes', 'content', 'subject', 'existingAssessments', 'id', 'instructionalSteps', 'lessonTimeLength', 'learningObjectives', 'metadata', 'digitalMaterials', 'quizzes', 'gradeLevel', 'lessonRef', 'lessonTitle', 'topic', 'country', 'extensionActivities', 'theme', 'adaptiveStrategies', 'conclusion', 'curriculumType', 'quizzesAndAssessments', 'introduction']
[2025-06-17 14:26:26,679] INFO - __main__ - main.py:1362 - [7c610ceb-48ce-46d0-9654-ed427b468cbf] Extracted 10 key concepts: ['Explore', 'role', 'saving', 'energy', 'Understand']...
[2025-06-17 14:26:26,680] INFO - __main__ - main.py:1442 - [7c610ceb-48ce-46d0-9654-ed427b468cbf] Universal content extraction: 1005 characters from 4 steps
[2025-06-17 14:26:26,681] INFO - __main__ - main.py:1479 - [7c610ceb-48ce-46d0-9654-ed427b468cbf] Universal conversion: 4 steps → 4 sections
[2025-06-17 14:26:26,682] INFO - __main__ - main.py:1297 - [7c610ceb-48ce-46d0-9654-ed427b468cbf] Field mapping completed:
[2025-06-17 14:26:26,682] INFO - __main__ - main.py:1298 -   - Subject: Artificial Intelligence
[2025-06-17 14:26:26,683] INFO - __main__ - main.py:1299 -   - Topic: How AI helps the planet
[2025-06-17 14:26:26,683] INFO - __main__ - main.py:1300 -   - Grade: Primary 5
[2025-06-17 14:26:26,684] INFO - __main__ - main.py:1301 -   - Key Concepts: 10 extracted
[2025-06-17 14:26:26,684] INFO - __main__ - main.py:1302 -   - Instructional Steps: 4
[2025-06-17 14:26:26,685] INFO - __main__ - main.py:1500 - [7c610ceb-48ce-46d0-9654-ed427b468cbf] ✅ Universal content structure recognized: instructionalSteps (4 steps)
[2025-06-17 14:26:26,689] INFO - __main__ - main.py:1515 - [7c610ceb-48ce-46d0-9654-ed427b468cbf] ✅ All required fields present after universal mapping
[2025-06-17 14:26:26,689] INFO - __main__ - main.py:1220 - [7c610ceb-48ce-46d0-9654-ed427b468cbf] Successfully mapped lesson fields for AI inference
[2025-06-17 14:26:26,689] DEBUG - __main__ - main.py:602 - Cached result for fetch_lesson_data
[2025-06-17 14:26:26,689] INFO - __main__ - main.py:10312 - [7c610ceb-48ce-46d0-9654-ed427b468cbf] ✅ USING existing 2 learning objectives
[2025-06-17 14:26:26,690] INFO - __main__ - main.py:10313 - [7c610ceb-48ce-46d0-9654-ed427b468cbf] 🎯 EXISTING OBJECTIVES: ['Explore AI’s role in saving energy.', 'Understand AI’s impact on wildlife conservation.']
[2025-06-17 14:26:27,017] INFO - __main__ - main.py:10319 - [7c610ceb-48ce-46d0-9654-ed427b468cbf] 💾 SAVED learning objectives to Firestore
[2025-06-17 14:26:27,020] INFO - __main__ - main.py:8735 - [7c610ceb-48ce-46d0-9654-ed427b468cbf] Attempting to initialize lesson session for student_id: andrea_ugono_33305
[2025-06-17 14:26:27,021] DEBUG - __main__ - main.py:8736 - [7c610ceb-48ce-46d0-9654-ed427b468cbf] lesson_data keys for init: ['lessonRef', 'lessonTitle', 'topic', 'subject', 'grade', 'key_concepts', 'learningObjectives', 'createdAt', 'updatedAt', 'instructionalSteps', 'content', 'sections', 'lessonTimeLength', 'introduction', 'conclusion', 'quizzes', 'adaptiveStrategies', 'blooms_level', 'difficulty', 'taxonomy_alignment', '_original_keys', '_mapping_timestamp', 'additionalNotes', 'existingAssessments', 'id', 'metadata', 'digitalMaterials', 'gradeLevel', 'country', 'extensionActivities', 'theme', 'curriculumType', 'quizzesAndAssessments', 'curriculum', 'level']
[2025-06-17 14:26:27,024] INFO - __main__ - main.py:8775 - [7c610ceb-48ce-46d0-9654-ed427b468cbf] Using student name: 'Andrea Ugono' for session session_b71023f5-62ee-482e-be3a-783fab2fb64c
[2025-06-17 14:26:27,025] INFO - __main__ - main.py:1537 - [7c610ceb-48ce-46d0-9654-ed427b468cbf] Constructing initial lesson state data for Firestore session: session_b71023f5-62ee-482e-be3a-783fab2fb64c
[2025-06-17 14:26:27,027] INFO - __main__ - main.py:1587 - [7c610ceb-48ce-46d0-9654-ed427b468cbf] ✅ Constructed initial state dictionary for session session_b71023f5-62ee-482e-be3a-783fab2fb64c. Phase: diagnostic_start_probe
[2025-06-17 14:26:27,028] INFO - __main__ - main.py:1588 - [7c610ceb-48ce-46d0-9654-ed427b468cbf] ✅ VALIDATION: current_phase = diagnostic_start_probe, current_lesson_phase = diagnostic_start_probe
[2025-06-17 14:26:27,029] INFO - __main__ - main.py:8828 - [7c610ceb-48ce-46d0-9654-ed427b468cbf] Preparing to create 'lesson_sessions' doc 'session_b71023f5-62ee-482e-be3a-783fab2fb64c'.
[2025-06-17 14:26:27,030] WARNING - __main__ - main.py:963 - JSON serialization failed, using string representation: Object of type Sentinel is not JSON serializable
[2025-06-17 14:26:27,031] DEBUG - __main__ - main.py:8829 - [7c610ceb-48ce-46d0-9654-ed427b468cbf] 'lesson_sessions' data (excluding snapshot): {'session_id': 'session_b71023f5-62ee-482e-be3a-783fab2fb64c', 'student_id': 'andrea_ugono_33305', 'lessonRef': 'P5-AI-001', 'country': 'Nigeria', 'curriculum': 'National Curriculum', 'grade': 'Primary 5', 'level': 'P5', 'subject': 'Artificial Intelligence', 'status': 'active', 'created_at': Sentinel: Value used to set a document field to the server timestamp., 'last_interaction': Sentinel: Value used to set a document field to the server timestamp., 'progress': 0, 'completion_status': 'in_progress', 'last_modified': Sentinel: Value used to set a document field to the server timestamp., 'student_name_at_creation': 'Andrea Ugono', 'interactions': [], 'ai_interactions': [], 'current_segment_index': 0, 'completed_segments': [], 'has_notes': False, 'lessonTitle': 'AI: Saving Our Planet!', 'topic': 'How AI helps the planet', 'learningObjectives': ['Explore AI’s role in saving energy.', 'Understand AI’s impact on wildlife conservation.'], 'key_concepts': ['Explore', 'role', 'saving', 'energy', 'Understand', 'impact', 'wildlife', 'conservation', 'What', 'Energy Saving'], 'metadata': {'blooms_level': ['Understanding', 'Applying', 'Analyzing'], 'context': 'Introduction', 'apiConnections': ["Gemini: Dynamically determines Bloom's Taxonomy level based on lesson content"], 'skills': ['Sustainability', 'Critical thinking'], 'difficulty': 'medium', 'taxonomy_alignment': "Aligned to Bloom's Taxonomy by promoting understanding of AI concepts, application of AI to real-world scenarios, and analysis of ethical considerations."}}
[2025-06-17 14:26:27,033] INFO - __main__ - main.py:8831 - [7c610ceb-48ce-46d0-9654-ed427b468cbf] Preparing to create 'lesson_states' doc 'session_b71023f5-62ee-482e-be3a-783fab2fb64c'.
[2025-06-17 14:26:27,035] WARNING - __main__ - main.py:963 - JSON serialization failed, using string representation: Object of type Sentinel is not JSON serializable
[2025-06-17 14:26:27,036] DEBUG - __main__ - main.py:8832 - [7c610ceb-48ce-46d0-9654-ed427b468cbf] 'lesson_states' data: {'session_id': 'session_b71023f5-62ee-482e-be3a-783fab2fb64c', 'student_id': 'andrea_ugono_33305', 'student_name': 'Andrea Ugono', 'current_lesson_phase': 'diagnostic_start_probe', 'current_phase': 'diagnostic_start_probe', 'current_probing_level_number': 5, 'current_question_index': 0, 'student_answers_for_probing_level': {}, 'levels_probed_and_failed': [], 'assigned_level_for_teaching': None, 'diagnostic_completed_this_session': False, 'lesson_context_snapshot': {'lessonRef': 'P5-AI-001', 'subject': 'Artificial Intelligence', 'grade': 'Primary 5', 'topic': 'How AI helps the planet', 'module_id': None, 'module_name': None}, 'blooms_levels_str_for_ai': 'Remember, Understand, Apply, Analyze, Evaluate, Create', 'key_concepts_str_for_ai': 'Explore, role, saving, energy, Understand, impact, wildlife, conservation, What, Energy Saving', 'created_at': Sentinel: Value used to set a document field to the server timestamp., 'last_modified': Sentinel: Value used to set a document field to the server timestamp.}
[2025-06-17 14:26:28,531] INFO - __main__ - main.py:8842 - [7c610ceb-48ce-46d0-9654-ed427b468cbf] Successfully created Firestore docs for session 'session_b71023f5-62ee-482e-be3a-783fab2fb64c', student 'andrea_ugono_33305'
[2025-06-17 14:26:28,539] INFO - werkzeug - _internal.py:97 - 127.0.0.1 - - [17/Jun/2025 14:26:28] "POST /lesson-content HTTP/1.1" 200 -
[2025-06-17 14:26:29,767] INFO - __main__ - main.py:4831 - REQUEST: POST http://localhost:5000/api/enhance-content -> endpoint: enhance_content_api
[2025-06-17 14:26:29,768] INFO - __main__ - main.py:4874 - Incoming request: {"request_id": "0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24", "timestamp": "2025-06-17T13:26:29.768344+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.9.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-AI-001", "content_to_enhance": "Start diagnostic assessment", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "Primary 5", "level": "P5", "subject": "Artificial Intelligence", "session_id": "session_b71023f5-62ee-482e-be3a-783fab2fb64c", "chat_history": []}}
[2025-06-17 14:26:29,770] INFO - temporary_auth_bypass - temporary_auth_bypass.py:29 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] Using temporary auth bypass with mock token
[2025-06-17 14:26:29,775] DEBUG - asyncio - proactor_events.py:631 - Using proactor: IocpProactor
[2025-06-17 14:26:29,778] WARNING - __main__ - main.py:5067 - 🔥 ENHANCE-CONTENT ENDPOINT CALLED!
[2025-06-17 14:26:29,779] INFO - __main__ - main.py:5178 - [enhance_content_api] Received request with decoded token.
[2025-06-17 14:26:29,779] INFO - __main__ - main.py:5180 - [enhance_content_api][0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] Processing enhance content request
[2025-06-17 14:26:29,780] INFO - __main__ - main.py:5226 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24][enhance_content_api] Parsed JSON payload (first 250 bytes): {'student_id': 'andrea_ugono_33305', 'lesson_ref': 'P5-AI-001', 'content_to_enhance': 'Start diagnostic assessment', 'country': 'Nigeria', 'curriculum': 'National Curriculum', 'grade': 'Primary 5', 'level': 'P5', 'subject': 'Artificial Intelligence',
[2025-06-17 14:26:30,145] INFO - __main__ - main.py:4424 - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
[2025-06-17 14:26:30,146] DEBUG - __main__ - main.py:596 - Cache hit for fetch_lesson_data
[2025-06-17 14:26:30,146] INFO - __main__ - main.py:5272 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] ✅ All required fields present after lesson content parsing and mapping
[2025-06-17 14:26:30,147] INFO - __main__ - main.py:5311 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] Attempting to infer module. GS Subject Slug: 'artificial_intelligence'.
[2025-06-17 14:26:30,148] INFO - __main__ - main.py:2273 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] AI Inference: Inferring module for subject 'artificial_intelligence', lesson 'AI: Saving Our Planet!'.
[2025-06-17 14:26:30,150] INFO - __main__ - main.py:3618 - Gemini API configured successfully with gemini-1.5-flash and safety filters disabled.
[2025-06-17 14:26:30,520] INFO - __main__ - main.py:2332 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] AI Inference: Loaded metadata for module 'ai_awareness_and_foundations' ('AI Awareness & Foundations')
[2025-06-17 14:26:30,520] INFO - __main__ - main.py:2332 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] AI Inference: Loaded metadata for module 'ai_tools_and_applications' ('AI Tools & Applications')
[2025-06-17 14:26:30,521] INFO - __main__ - main.py:2332 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] AI Inference: Loaded metadata for module 'computational_thinking_and_coding' ('Computational Thinking & Coding')
[2025-06-17 14:26:30,522] INFO - __main__ - main.py:2332 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] AI Inference: Loaded metadata for module 'data_literacy_and_machine_learning' ('Data Literacy & Machine Learning')
[2025-06-17 14:26:30,523] INFO - __main__ - main.py:2332 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] AI Inference: Loaded metadata for module 'ethics_and_societal_impact' ('Ethics & Societal Impact')
[2025-06-17 14:26:30,524] INFO - __main__ - main.py:2401 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] AI Inference: Starting module inference for subject 'artificial_intelligence' with 5 module options
[2025-06-17 14:26:30,524] DEBUG - __main__ - main.py:2415 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] AI Inference: Sample modules available (first 3):
- ai_awareness_and_foundations: AI Awareness & Foundations
- ai_tools_and_applications: AI Tools & Applications
- computational_thinking_and_coding: Computational Thinking & Coding
[2025-06-17 14:26:30,525] DEBUG - __main__ - main.py:2418 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] AI Inference Prompt (first 500 chars): 
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: AI: Saving Our Planet!. Topic: How AI helps the planet. Learning Objectives: Explore AI’s role in saving energy.; Understand AI’s impact on wildlife conservation.. Key Concepts: Explore; role; saving; energy; Understand; impact; wildlife; conservation; What; Energy Saving. Introducti...
[2025-06-17 14:26:30,526] DEBUG - __main__ - main.py:2419 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] AI Inference Lesson Summary (first 300 chars): Lesson Title: AI: Saving Our Planet!. Topic: How AI helps the planet. Learning Objectives: Explore AI’s role in saving energy.; Understand AI’s impact on wildlife conservation.. Key Concepts: Explore; role; saving; energy; Understand; impact; wildlife; conservation; What; Energy Saving. Introduction...
[2025-06-17 14:26:30,527] DEBUG - __main__ - main.py:2420 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] AI Inference Module Options (first 200 chars): 1. Slug: "ai_awareness_and_foundations", Name: "AI Awareness & Foundations", Description: "Conceptual journey from recognising smart helpers to understanding core AI paradigms (symbolic, statistical, ...
[2025-06-17 14:26:30,527] INFO - __main__ - main.py:2424 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] AI Inference: Calling Gemini API for module inference...
[2025-06-17 14:26:32,228] DEBUG - __main__ - main.py:2196 - extract_gemini_text: Successfully extracted 25 characters
[2025-06-17 14:26:32,229] INFO - __main__ - main.py:2434 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] AI Inference: Gemini API call completed in 1.70s. Raw response: 'ai_tools_and_applications'
[2025-06-17 14:26:32,230] DEBUG - __main__ - main.py:2456 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] AI Inference: Cleaned slug: 'ai_tools_and_applications'
[2025-06-17 14:26:32,230] INFO - __main__ - main.py:2461 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] AI Inference: Successfully matched module by slug. Chosen: 'ai_tools_and_applications' (AI Tools & Applications)
[2025-06-17 14:26:32,231] INFO - __main__ - main.py:5344 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] Successfully inferred module ID via AI: ai_tools_and_applications
[2025-06-17 14:26:32,233] INFO - __main__ - main.py:2510 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] CACHE MISS or fetch: Getting GS levels for subject 'artificial_intelligence', module 'ai_tools_and_applications'.
[2025-06-17 14:26:32,614] INFO - __main__ - main.py:2533 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] Fetched metadata for module: 'AI Tools & Applications'
[2025-06-17 14:26:33,066] INFO - __main__ - main.py:2565 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] Successfully fetched 10 levels for module 'ai_tools_and_applications'.
[2025-06-17 14:26:33,066] INFO - __main__ - main.py:5370 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] Effective module name for prompt context: 'AI Tools & Applications' (Module ID: ai_tools_and_applications)
[2025-06-17 14:26:33,410] INFO - __main__ - main.py:1995 - No prior student performance document found for Topic: artificial_intelligence_Primary 5_artificial_intelligence_ai_tools_and_applications
[2025-06-17 14:26:33,768] WARNING - __main__ - main.py:5391 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🔍 SESSION STATE DEBUG:
[2025-06-17 14:26:33,768] WARNING - __main__ - main.py:5392 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🔍   - Session exists: True
[2025-06-17 14:26:33,769] WARNING - __main__ - main.py:5393 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🔍   - Current phase: diagnostic_start_probe
[2025-06-17 14:26:33,771] WARNING - __main__ - main.py:5394 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🔍   - State data keys: ['created_at', 'session_id', 'key_concepts_str_for_ai', 'blooms_levels_str_for_ai', 'current_phase', 'diagnostic_completed_this_session', 'student_name', 'lesson_context_snapshot', 'current_probing_level_number', 'levels_probed_and_failed', 'assigned_level_for_teaching', 'current_lesson_phase', 'current_question_index', 'student_id', 'last_modified', 'student_answers_for_probing_level']
[2025-06-17 14:26:33,773] INFO - __main__ - main.py:5458 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] �🔍 FIRST ENCOUNTER LOGIC:
[2025-06-17 14:26:33,773] INFO - __main__ - main.py:5459 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24]   assigned_level_for_teaching (session): None
[2025-06-17 14:26:33,774] INFO - __main__ - main.py:5460 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24]   latest_assessed_level (profile): None
[2025-06-17 14:26:33,774] INFO - __main__ - main.py:5461 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24]   teaching_level_for_returning_student: None
[2025-06-17 14:26:33,774] INFO - __main__ - main.py:5462 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24]   has_completed_diagnostic_before: False
[2025-06-17 14:26:33,775] INFO - __main__ - main.py:5463 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24]   is_first_encounter_for_module: True
[2025-06-17 14:26:33,776] WARNING - __main__ - main.py:5468 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
[2025-06-17 14:26:33,776] INFO - __main__ - main.py:5474 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🔍 PHASE INVESTIGATION:
[2025-06-17 14:26:33,776] INFO - __main__ - main.py:5475 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24]   Retrieved from Firestore: 'diagnostic_start_probe'
[2025-06-17 14:26:33,777] INFO - __main__ - main.py:5476 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'diagnostic_start_probe'
[2025-06-17 14:26:33,777] INFO - __main__ - main.py:5477 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24]   Is first encounter: True
[2025-06-17 14:26:33,777] INFO - __main__ - main.py:5478 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24]   Diagnostic completed: False
[2025-06-17 14:26:33,778] INFO - __main__ - main.py:5484 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] ✅ Using stored phase from Firestore: 'diagnostic_start_probe'
[2025-06-17 14:26:33,778] INFO - __main__ - main.py:5498 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
[2025-06-17 14:26:33,778] INFO - __main__ - main.py:5500 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] Final phase for AI logic: diagnostic_start_probe
[2025-06-17 14:26:33,778] INFO - __main__ - main.py:5520 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] NEW SESSION: Forcing question_index to 0 (was: 0)
[2025-06-17 14:26:33,779] INFO - __main__ - main.py:3692 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] Diagnostic context validation passed
[2025-06-17 14:26:33,779] INFO - __main__ - main.py:3719 - DETERMINE_PHASE: New session or first interaction. Starting with diagnostic_start_probe.
[2025-06-17 14:26:33,779] WARNING - __main__ - main.py:5586 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'diagnostic_start_probe' for first encounter
[2025-06-17 14:26:33,780] INFO - __main__ - main.py:3802 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] Enhanced diagnostic context with 35 fields
[2025-06-17 14:26:33,780] INFO - __main__ - main.py:5608 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] Robust diagnostic context prepared successfully. Phase: diagnostic_start_probe
[2025-06-17 14:26:33,780] DEBUG - __main__ - main.py:5609 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'diagnostic_context', 'performance_tracking', 'level_specific_guidance', 'current_phase_for_ai']
[2025-06-17 14:26:33,780] WARNING - __main__ - main.py:5617 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🤖 AI PROMPT GENERATION:
[2025-06-17 14:26:33,781] WARNING - __main__ - main.py:5618 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🤖   - Current phase: diagnostic_start_probe
[2025-06-17 14:26:33,781] WARNING - __main__ - main.py:5619 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🤖   - Student query: Start diagnostic assessment...
[2025-06-17 14:26:33,781] WARNING - __main__ - main.py:5620 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🤖   - Context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'diagnostic_context', 'performance_tracking', 'level_specific_guidance', 'current_phase_for_ai']
[2025-06-17 14:26:33,783] INFO - __main__ - main.py:6493 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] enhance_lesson_content invoked. Query: 'Start diagnostic assessment...'
[2025-06-17 14:26:33,783] INFO - __main__ - main.py:6536 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] CONTEXT DEBUG: lesson_phase from context = 'diagnostic_start_probe', processed = 'diagnostic_start_probe'
[2025-06-17 14:26:33,783] INFO - __main__ - main.py:6547 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24][enhance_lesson_content] Received from context - phase: 'diagnostic_start_probe', module_id: 'ai_tools_and_applications', gs_subject_slug: 'artificial_intelligence'
[2025-06-17 14:26:33,784] INFO - __main__ - main.py:6581 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] DIAGNOSTIC ANSWER: Storing sequential answer 1
[2025-06-17 14:26:33,784] INFO - __main__ - main.py:6586 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] DIAGNOSTIC ANSWER STORED: q1 = 'Start diagnostic assessment...'
[2025-06-17 14:26:33,784] INFO - __main__ - main.py:6587 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] Total diagnostic answers now: 1/5
[2025-06-17 14:26:33,784] INFO - __main__ - main.py:6628 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 📝 DIAGNOSTIC PROGRESSION: Continue with 1/5 answers, phase: diagnostic_start_probe
[2025-06-17 14:26:33,785] INFO - __main__ - main.py:6766 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24][enhance_lesson_content] Proceeding to main AI prompt generation. Phase: diagnostic_start_probe
[2025-06-17 14:26:33,785] INFO - __main__ - main.py:6773 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24][DIAGNOSTIC_FLOW] Phase: diagnostic_start_probe
[2025-06-17 14:26:33,785] INFO - __main__ - main.py:6774 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24][DIAGNOSTIC_FLOW] Questions asked: 1/5
[2025-06-17 14:26:33,786] INFO - __main__ - main.py:6775 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24][DIAGNOSTIC_FLOW] Question index: 0
[2025-06-17 14:26:33,786] INFO - __main__ - main.py:6776 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24][DIAGNOSTIC_FLOW] Student answers count: 1
[2025-06-17 14:26:33,786] INFO - __main__ - main.py:6781 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24][DIAGNOSTIC_FLOW] Answer q1: Start diagnostic assessment...
[2025-06-17 14:26:33,787] INFO - __main__ - main.py:6805 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24][DIAGNOSTIC_FLOW] SIMPLIFIED LOGIC: Phase=diagnostic_start_probe, Answers=1/5
[2025-06-17 14:26:33,787] INFO - __main__ - main.py:6808 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24][DIAGNOSTIC_FLOW] SIMPLIFIED: Trusting AI to handle diagnostic completion naturally
[2025-06-17 14:26:33,788] INFO - __main__ - main.py:6839 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] INTERACTION COUNT: Updated to 1 and saved to context
[2025-06-17 14:26:33,788] INFO - __main__ - main.py:6853 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] DIAGNOSTIC PROGRESSION: Calculating next phase for diagnostic_start_probe
[2025-06-17 14:26:33,788] INFO - __main__ - main.py:6861 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] DIAGNOSTIC PROGRESSION: Running diagnostic logic for phase 'diagnostic_start_probe'
[2025-06-17 14:26:33,789] INFO - __main__ - main.py:6870 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] DIAGNOSTIC PROGRESSION: First/system interaction - staying in diagnostic_start_probe for introduction
[2025-06-17 14:26:33,789] INFO - __main__ - main.py:7120 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🎯 ENHANCED PHASE CALCULATION: diagnostic_start_probe → diagnostic_start_probe (interaction 1)
[2025-06-17 14:26:33,789] WARNING - __main__ - main.py:7123 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🔍 DIAGNOSTIC FLOW START:
[2025-06-17 14:26:33,790] WARNING - __main__ - main.py:7124 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🔍   - Input phase from context: 'diagnostic_start_probe'
[2025-06-17 14:26:33,790] WARNING - __main__ - main.py:7125 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🔍   - User query: 'Start diagnostic assessment'
[2025-06-17 14:26:33,791] WARNING - __main__ - main.py:7126 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🔍   - Is actual student response: True
[2025-06-17 14:26:33,793] WARNING - __main__ - main.py:7127 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🔍   - Current probing level: 5
[2025-06-17 14:26:33,794] WARNING - __main__ - main.py:7128 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🔍   - Current question index: 0
[2025-06-17 14:26:33,795] WARNING - __main__ - main.py:7129 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🔍   - Student answers count: 1
[2025-06-17 14:26:33,795] INFO - __main__ - main.py:7132 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🔧 NATURAL PROGRESSION DEBUG:
[2025-06-17 14:26:33,796] INFO - __main__ - main.py:7133 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🔧   - lesson_phase_from_context: 'diagnostic_start_probe'
[2025-06-17 14:26:33,796] INFO - __main__ - main.py:7134 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🔧   - python_calculated_new_phase_for_block: 'diagnostic_start_probe'
[2025-06-17 14:26:33,796] INFO - __main__ - main.py:7135 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🔧   - user_query: 'Start diagnostic assessment'
[2025-06-17 14:26:33,797] INFO - __main__ - main.py:7136 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🔧   - trusting_ai_state_updates: True
[2025-06-17 14:26:33,797] WARNING - __main__ - main.py:7139 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🎯 PHASE CALCULATION RESULT:
[2025-06-17 14:26:33,797] WARNING - __main__ - main.py:7140 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🎯   - Current phase: 'diagnostic_start_probe'
[2025-06-17 14:26:33,798] WARNING - __main__ - main.py:7141 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🎯   - Calculated new phase: 'diagnostic_start_probe'
[2025-06-17 14:26:33,798] WARNING - __main__ - main.py:7142 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🎯   - Phase changed: False
[2025-06-17 14:26:33,798] WARNING - __main__ - main.py:7145 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🎯 DIAGNOSTIC SPECIFIC:
[2025-06-17 14:26:33,799] WARNING - __main__ - main.py:7146 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🎯   - In diagnostic phase: True
[2025-06-17 14:26:33,799] WARNING - __main__ - main.py:7147 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🎯   - Student answers stored: {'q1': 'Start diagnostic assessment'}
[2025-06-17 14:26:33,799] WARNING - __main__ - main.py:7148 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🎯   - Expected progression: Should move to next question/phase
[2025-06-17 14:26:33,800] INFO - __main__ - main.py:1829 - ENHANCED lesson content extraction: subject='Artificial Intelligence', topic='How AI helps the planet', key_concepts='What, Energy, Saving, Wildlife, Conservation', examples=0
[2025-06-17 14:26:33,800] INFO - __main__ - main.py:7209 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24][enhance_lesson_content] Python-calculated TARGET values for AI's output state block: new_phase='diagnostic_start_probe', q_index='0', total_q_asked='1'
[2025-06-17 14:26:33,801] INFO - __main__ - main.py:7215 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24][diagnostic_logic] SIMPLIFIED: Trusting AI to handle diagnostic progression naturally
[2025-06-17 14:26:33,801] INFO - __main__ - main.py:7320 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] NATURAL COMPLETION: Trusting AI to handle diagnostic completion naturally
[2025-06-17 14:26:33,801] INFO - __main__ - main.py:7331 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] SIMPLIFIED: Trusting AI to determine appropriate phase transitions naturally
[2025-06-17 14:26:33,802] INFO - __main__ - main.py:7337 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] ✅ Template placeholder successfully substituted
[2025-06-17 14:26:33,802] INFO - __main__ - main.py:7341 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] ✅ Template contains calculated phase: diagnostic_start_probe
[2025-06-17 14:26:33,803] INFO - __main__ - main.py:7385 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] Prompt truncated from 12055 to 10637 chars for performance
[2025-06-17 14:26:33,804] INFO - __main__ - main.py:7388 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] CONTENT QUALITY DEBUG:
[2025-06-17 14:26:33,804] INFO - __main__ - main.py:7389 -   - Final prompt length: 10637 characters
[2025-06-17 14:26:33,805] INFO - __main__ - main.py:7390 -   - Subject: Artificial Intelligence
[2025-06-17 14:26:33,805] INFO - __main__ - main.py:7391 -   - Topic: How AI helps the planet
[2025-06-17 14:26:33,805] INFO - __main__ - main.py:7392 -   - Key concepts: What, Energy, Saving, Wildlife, Conservation
[2025-06-17 14:26:33,806] INFO - __main__ - main.py:7393 -   - Grade: Primary 5
[2025-06-17 14:26:33,807] INFO - __main__ - main.py:7394 -   - Phase: diagnostic_start_probe
[2025-06-17 14:26:33,808] INFO - __main__ - main.py:7395 -   - Student: Andrea
[2025-06-17 14:26:33,809] DEBUG - __main__ - main.py:7396 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] Prompt preview: 
MANDATORY: End with: // AI_STATE_UPDATE_BLOCK_START {"new_phase": "diagnostic_start_probe", "interaction_count": 1} // AI_STATE_UPDATE_BLOCK_END

CRITICAL PHASE PROGRESSION: 
• The system has calculated the next phase as: diagnostic_start_probe
• You MUST use this exact phase name in your state upd...
[2025-06-17 14:26:33,810] INFO - __main__ - main.py:7424 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] Gemini API call attempt 1/3
[2025-06-17 14:26:35,171] INFO - __main__ - main.py:7442 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] ✅ Gemini API call succeeded on attempt 1
[2025-06-17 14:26:35,172] INFO - __main__ - main.py:7461 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] Gemini API call completed in 1.36s
[2025-06-17 14:26:35,173] DEBUG - __main__ - main.py:2196 - extract_gemini_text: Successfully extracted 500 characters
[2025-06-17 14:26:35,174] INFO - __main__ - main.py:7684 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24][enhance_lesson_content] AI response: Hi Andrea! I'm your AI tutor, and I'm excited to help you learn about how Artificial Intelligence helps our planet.  We'll start with a quick diagnostic assessment to see what you already know. This h...
[2025-06-17 14:26:35,175] INFO - __main__ - main.py:7712 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🔍 STATE DEBUG: Looking for state update block in AI response
[2025-06-17 14:26:35,176] INFO - __main__ - main.py:7713 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🔍 STATE DEBUG: AI response length: 500 chars
[2025-06-17 14:26:35,178] INFO - __main__ - main.py:7725 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🔍 STATE DEBUG: Found state block: {"new_phase": "diagnostic_start_probe", "interaction_count": 1}
[2025-06-17 14:26:35,179] INFO - __main__ - main.py:7727 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] ✅ Found AI state update: {'new_phase': 'diagnostic_start_probe', 'interaction_count': 1}
[2025-06-17 14:26:35,180] INFO - __main__ - main.py:7791 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] SIMPLIFIED: Trusting AI state updates without aggressive diagnostic forcing
[2025-06-17 14:26:35,181] INFO - __main__ - main.py:7797 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] SIMPLIFIED: Using AI response without hardcoded fallbacks
[2025-06-17 14:26:35,181] INFO - __main__ - main.py:7817 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] Auto-corrected: added missing current_probing_level_number
[2025-06-17 14:26:35,182] INFO - __main__ - main.py:7821 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] Auto-corrected: added missing current_question_index
[2025-06-17 14:26:35,182] INFO - __main__ - main.py:7847 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] ✅ AI state validation completed: {'new_phase': 'diagnostic_start_probe', 'interaction_count': 1, 'current_probing_level_number': 5, 'current_question_index': 0}
[2025-06-17 14:26:35,183] INFO - __main__ - main.py:7859 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] ✅ AI PROVIDED VALID STATE: Using AI's state update proposal
[2025-06-17 14:26:35,184] INFO - __main__ - main.py:7902 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] Placeholder apply_phase_transition_fixes detected or no fix made. Prioritizing AI's originally proposed state.
[2025-06-17 14:26:35,184] INFO - __main__ - main.py:7911 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] State updates after apply_fixes and potential AI prioritization: {'new_phase': 'diagnostic_start_probe', 'interaction_count': 1, 'current_probing_level_number': 5, 'current_question_index': 0}
[2025-06-17 14:26:35,185] INFO - __main__ - main.py:7969 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 📝 DIAGNOSTIC PROGRESSION: Continue diagnostic - eval_phase=False, answers=1/5
[2025-06-17 14:26:35,186] INFO - __main__ - main.py:7972 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 📝 DIAGNOSTIC PROGRESSION: Need 4 more answers
[2025-06-17 14:26:35,187] INFO - __main__ - main.py:7974 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 📝 DIAGNOSTIC PROGRESSION: Not in eval_q5_decide_level phase
[2025-06-17 14:26:35,188] INFO - __main__ - main.py:7978 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] NATURAL COMPLETION: Trusting AI to handle diagnostic completion naturally
[2025-06-17 14:26:35,189] INFO - __main__ - main.py:8006 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] DIAGNOSTIC PROGRESSION: AI stuck in same phase - applying calculated fallback: diagnostic_start_probe → diagnostic_start_probe
[2025-06-17 14:26:35,189] INFO - __main__ - main.py:8055 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] DIAGNOSTIC FALLBACK: Applying calculated progression only when AI fails to provide updates: diagnostic_start_probe → diagnostic_start_probe
[2025-06-17 14:26:35,190] WARNING - __main__ - main.py:8070 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🔍 DIAGNOSTIC FALLBACK STATE UPDATE:
[2025-06-17 14:26:35,190] WARNING - __main__ - main.py:8071 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🔍   - Phase: diagnostic_start_probe → diagnostic_start_probe
[2025-06-17 14:26:35,191] WARNING - __main__ - main.py:8072 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🔍   - Question Index: 0
[2025-06-17 14:26:35,191] WARNING - __main__ - main.py:8073 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🔍   - Probing Level: 5
[2025-06-17 14:26:35,191] INFO - __main__ - main.py:8077 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] DIAGNOSTIC FALLBACK: System provided fallback state updates when AI failed to respond
[2025-06-17 14:26:35,192] INFO - __main__ - main.py:8078 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] DIAGNOSTIC ENFORCEMENT: Original AI phase: diagnostic_start_probe, Enforced phase: diagnostic_start_probe
[2025-06-17 14:26:35,193] INFO - __main__ - main.py:8098 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] [OK] State update block guaranteed by system enforcement
[2025-06-17 14:26:35,195] DEBUG - __main__ - main.py:8164 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] Phase unchanged: diagnostic_start_probe
[2025-06-17 14:26:35,196] INFO - __main__ - main.py:8180 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 📋 FINAL STATE UPDATE: {'new_phase': 'diagnostic_start_probe', 'current_probing_level_number': 5, 'current_question_index': 0, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': 'Start diagnostic assessment'}, 'levels_probed_and_failed': []}
[2025-06-17 14:26:35,197] INFO - __main__ - main.py:8216 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] ✅ Clean response prepared for user, state updates handled internally
[2025-06-17 14:26:35,198] INFO - __main__ - main.py:8232 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] ✅ AI response already includes personalization
[2025-06-17 14:26:35,199] INFO - __main__ - main.py:8238 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] [OK] State updates processed: {'new_phase': 'diagnostic_start_probe', 'current_probing_level_number': 5, 'current_question_index': 0, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': 'Start diagnostic assessment'}, 'levels_probed_and_failed': []}
[2025-06-17 14:26:35,199] INFO - __main__ - main.py:8241 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] SIMPLIFIED: Diagnostic enforcement disabled to prevent backward transitions
[2025-06-17 14:26:35,200] WARNING - __main__ - main.py:8262 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🔄 STATE UPDATE PROCESSING:
[2025-06-17 14:26:35,200] WARNING - __main__ - main.py:8263 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🔄   - AI provided state update: True
[2025-06-17 14:26:35,200] WARNING - __main__ - main.py:8265 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🔄   - AI proposed phase: 'diagnostic_start_probe'
[2025-06-17 14:26:35,201] WARNING - __main__ - main.py:8266 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🔄   - Current lesson phase: 'diagnostic_start_probe'
[2025-06-17 14:26:35,201] WARNING - __main__ - main.py:8267 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🔄   - Python calculated phase: 'diagnostic_start_probe'
[2025-06-17 14:26:35,202] INFO - __main__ - main.py:8275 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] ⚡ PERFORMANCE METRICS:
[2025-06-17 14:26:35,202] INFO - __main__ - main.py:8276 -   - Total execution time: 1.419s
[2025-06-17 14:26:35,203] WARNING - __main__ - main.py:5643 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🤖 AI RESPONSE RECEIVED:
[2025-06-17 14:26:35,204] WARNING - __main__ - main.py:5644 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🤖   - Content length: 374 chars
[2025-06-17 14:26:35,205] WARNING - __main__ - main.py:5645 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🤖   - State updates: {'new_phase': 'diagnostic_start_probe', 'current_probing_level_number': 5, 'current_question_index': 0, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': 'Start diagnostic assessment'}, 'levels_probed_and_failed': []}
[2025-06-17 14:26:35,205] WARNING - __main__ - main.py:5646 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🤖   - Raw state block: None...
[2025-06-17 14:26:35,207] INFO - __main__ - main.py:5662 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
[2025-06-17 14:26:35,208] INFO - __main__ - main.py:5663 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] CURRENT PHASE DETERMINATION: AI=diagnostic_start_probe, Session=diagnostic_start_probe, Final=diagnostic_start_probe
[2025-06-17 14:26:35,551] INFO - __main__ - main.py:5772 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] AI processing completed in 1.77s
[2025-06-17 14:26:35,552] WARNING - __main__ - main.py:5783 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🔍 STATE UPDATE VALIDATION: current_phase='diagnostic_start_probe', new_phase='diagnostic_start_probe'
[2025-06-17 14:26:35,554] INFO - __main__ - main.py:3897 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] AI state update validation passed: diagnostic_start_probe → diagnostic_start_probe
[2025-06-17 14:26:35,555] WARNING - __main__ - main.py:5792 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] ✅ STATE UPDATE VALIDATION PASSED
[2025-06-17 14:26:35,555] WARNING - __main__ - main.py:5799 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] � PHASE MAINTAINED: diagnostic_start_probe
[2025-06-17 14:26:35,556] WARNING - __main__ - main.py:5806 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🔍 COMPLETE PHASE FLOW DEBUG:
[2025-06-17 14:26:35,557] WARNING - __main__ - main.py:5807 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🔍   1. Input phase: 'diagnostic_start_probe'
[2025-06-17 14:26:35,558] WARNING - __main__ - main.py:5808 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🔍   2. Python calculated next phase: 'NOT_FOUND'
[2025-06-17 14:26:35,559] WARNING - __main__ - main.py:5809 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🔍   3. AI state updates: {'new_phase': 'diagnostic_start_probe', 'current_probing_level_number': 5, 'current_question_index': 0, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': 'Start diagnostic assessment'}, 'levels_probed_and_failed': []}
[2025-06-17 14:26:35,560] WARNING - __main__ - main.py:5810 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🔍   4. Final phase to save: 'diagnostic_start_probe'
[2025-06-17 14:26:35,560] WARNING - __main__ - main.py:5813 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 💾 FINAL STATE APPLICATION:
[2025-06-17 14:26:35,561] WARNING - __main__ - main.py:5814 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 💾   - Current phase input: 'diagnostic_start_probe'
[2025-06-17 14:26:35,561] WARNING - __main__ - main.py:5815 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 💾   - State updates from AI: {'new_phase': 'diagnostic_start_probe', 'current_probing_level_number': 5, 'current_question_index': 0, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': 'Start diagnostic assessment'}, 'levels_probed_and_failed': []}
[2025-06-17 14:26:35,561] WARNING - __main__ - main.py:5816 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 💾   - Final phase to save: 'diagnostic_start_probe'
[2025-06-17 14:26:35,562] WARNING - __main__ - main.py:5817 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 💾   - Phase change: False
[2025-06-17 14:26:35,562] INFO - __main__ - main.py:3929 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] DIAGNOSTIC_FLOW_METRICS:
[2025-06-17 14:26:35,562] INFO - __main__ - main.py:3930 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24]   Phase transition: diagnostic_start_probe -> diagnostic_start_probe
[2025-06-17 14:26:35,563] INFO - __main__ - main.py:3931 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24]   Current level: 5
[2025-06-17 14:26:35,563] INFO - __main__ - main.py:3932 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24]   Question index: 0
[2025-06-17 14:26:35,563] INFO - __main__ - main.py:3933 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24]   First encounter: True
[2025-06-17 14:26:35,564] INFO - __main__ - main.py:3938 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24]   Answers collected: 1
[2025-06-17 14:26:35,564] INFO - __main__ - main.py:3939 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24]   Levels failed: 0
[2025-06-17 14:26:35,564] INFO - __main__ - main.py:3897 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] AI state update validation passed: diagnostic_start_probe → diagnostic_start_probe
[2025-06-17 14:26:35,565] INFO - __main__ - main.py:3943 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24]   State update valid: True
[2025-06-17 14:26:35,565] INFO - __main__ - main.py:3950 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24]   Diagnostic complete: False
[2025-06-17 14:26:35,565] WARNING - __main__ - main.py:5829 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 0, current_q_index_for_prompt = NOT_FOUND
[2025-06-17 14:26:36,029] WARNING - __main__ - main.py:5856 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] ✅ SESSION STATE SAVED TO FIRESTORE:
[2025-06-17 14:26:36,029] WARNING - __main__ - main.py:5857 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] ✅   - Phase: diagnostic_start_probe
[2025-06-17 14:26:36,030] WARNING - __main__ - main.py:5858 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] ✅   - Probing Level: 5
[2025-06-17 14:26:36,030] WARNING - __main__ - main.py:5859 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] ✅   - Question Index: 0
[2025-06-17 14:26:36,031] WARNING - __main__ - main.py:5860 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] ✅   - Diagnostic Complete: False
[2025-06-17 14:26:37,159] INFO - __main__ - main.py:5919 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] ✅ Updated existing session document: session_b71023f5-62ee-482e-be3a-783fab2fb64c
[2025-06-17 14:26:37,160] WARNING - __main__ - main.py:5920 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] ✅ SESSION UPDATE COMPLETE:
[2025-06-17 14:26:37,160] WARNING - __main__ - main.py:5921 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] ✅   - Session ID: session_b71023f5-62ee-482e-be3a-783fab2fb64c
[2025-06-17 14:26:37,161] WARNING - __main__ - main.py:5922 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] ✅   - Phase transition: diagnostic_start_probe → diagnostic_start_probe
[2025-06-17 14:26:37,162] WARNING - __main__ - main.py:5923 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] ✅   - Interaction logged successfully
[2025-06-17 14:26:37,163] INFO - __main__ - main.py:9173 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
[2025-06-17 14:26:37,165] DEBUG - __main__ - main.py:2753 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] FINAL_ASSESSMENT_BLOCK not found in AI response.
[2025-06-17 14:26:37,165] DEBUG - __main__ - main.py:5969 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] No final assessment data found in AI response
[2025-06-17 14:26:37,167] INFO - __main__ - main.py:6034 - [0fa5be92-0d98-4f0d-b1ba-3e4de11d2e24] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
[2025-06-17 14:26:37,171] WARNING - __main__ - main.py:625 - High response time detected: 7.40s for enhance_content_api
[2025-06-17 14:26:37,172] INFO - werkzeug - _internal.py:97 - 127.0.0.1 - - [17/Jun/2025 14:26:37] "POST /api/enhance-content HTTP/1.1" 200 -
[2025-06-17 14:27:05,444] INFO - __main__ - main.py:4831 - REQUEST: POST http://localhost:5000/api/enhance-content -> endpoint: enhance_content_api
[2025-06-17 14:27:05,445] INFO - __main__ - main.py:4874 - Incoming request: {"request_id": "90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d", "timestamp": "2025-06-17T13:27:05.444896+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.9.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-AI-001", "content_to_enhance": "Yes I'm ready", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "Primary 5", "level": "P5", "subject": "Artificial Intelligence", "session_id": "session_b71023f5-62ee-482e-be3a-783fab2fb64c", "chat_history": [{"role": "assistant", "content": "Hi Andrea! I'm your AI tutor, and I'm excited to help you learn about how Artificial Intelligence helps our planet. We'll start with a quick diagnostic assessment to see what you already know. This helps me tailor the lessons perfectly for you. Are you ready to begin with some questions about what AI is, how it saves energy, protects wildlife, and helps with conservation?", "timestamp": "2025-06-17T13:26:37.197Z"}]}}
[2025-06-17 14:27:05,446] INFO - temporary_auth_bypass - temporary_auth_bypass.py:29 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] Using temporary auth bypass with mock token
[2025-06-17 14:27:05,447] DEBUG - asyncio - proactor_events.py:631 - Using proactor: IocpProactor
[2025-06-17 14:27:05,455] WARNING - __main__ - main.py:5067 - 🔥 ENHANCE-CONTENT ENDPOINT CALLED!
[2025-06-17 14:27:05,460] INFO - __main__ - main.py:5178 - [enhance_content_api] Received request with decoded token.
[2025-06-17 14:27:05,461] INFO - __main__ - main.py:5180 - [enhance_content_api][90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] Processing enhance content request
[2025-06-17 14:27:05,462] INFO - __main__ - main.py:5226 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d][enhance_content_api] Parsed JSON payload (first 250 bytes): {'student_id': 'andrea_ugono_33305', 'lesson_ref': 'P5-AI-001', 'content_to_enhance': "Yes I'm ready", 'country': 'Nigeria', 'curriculum': 'National Curriculum', 'grade': 'Primary 5', 'level': 'P5', 'subject': 'Artificial Intelligence', 'session_id':
[2025-06-17 14:27:05,776] INFO - __main__ - main.py:4424 - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
[2025-06-17 14:27:05,777] DEBUG - __main__ - main.py:596 - Cache hit for fetch_lesson_data
[2025-06-17 14:27:05,777] INFO - __main__ - main.py:5272 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] ✅ All required fields present after lesson content parsing and mapping
[2025-06-17 14:27:05,778] INFO - __main__ - main.py:5311 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] Attempting to infer module. GS Subject Slug: 'artificial_intelligence'.
[2025-06-17 14:27:05,779] INFO - __main__ - main.py:2273 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] AI Inference: Inferring module for subject 'artificial_intelligence', lesson 'AI: Saving Our Planet!'.
[2025-06-17 14:27:06,094] INFO - __main__ - main.py:2332 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] AI Inference: Loaded metadata for module 'ai_awareness_and_foundations' ('AI Awareness & Foundations')
[2025-06-17 14:27:06,095] INFO - __main__ - main.py:2332 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] AI Inference: Loaded metadata for module 'ai_tools_and_applications' ('AI Tools & Applications')
[2025-06-17 14:27:06,096] INFO - __main__ - main.py:2332 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] AI Inference: Loaded metadata for module 'computational_thinking_and_coding' ('Computational Thinking & Coding')
[2025-06-17 14:27:06,096] INFO - __main__ - main.py:2332 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] AI Inference: Loaded metadata for module 'data_literacy_and_machine_learning' ('Data Literacy & Machine Learning')
[2025-06-17 14:27:06,097] INFO - __main__ - main.py:2332 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] AI Inference: Loaded metadata for module 'ethics_and_societal_impact' ('Ethics & Societal Impact')
[2025-06-17 14:27:06,098] INFO - __main__ - main.py:2401 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] AI Inference: Starting module inference for subject 'artificial_intelligence' with 5 module options
[2025-06-17 14:27:06,099] DEBUG - __main__ - main.py:2415 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] AI Inference: Sample modules available (first 3):
- ai_awareness_and_foundations: AI Awareness & Foundations
- ai_tools_and_applications: AI Tools & Applications
- computational_thinking_and_coding: Computational Thinking & Coding
[2025-06-17 14:27:06,100] DEBUG - __main__ - main.py:2418 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] AI Inference Prompt (first 500 chars): 
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: AI: Saving Our Planet!. Topic: How AI helps the planet. Learning Objectives: Explore AI’s role in saving energy.; Understand AI’s impact on wildlife conservation.. Key Concepts: Explore; role; saving; energy; Understand; impact; wildlife; conservation; What; Energy Saving. Introducti...
[2025-06-17 14:27:06,101] DEBUG - __main__ - main.py:2419 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] AI Inference Lesson Summary (first 300 chars): Lesson Title: AI: Saving Our Planet!. Topic: How AI helps the planet. Learning Objectives: Explore AI’s role in saving energy.; Understand AI’s impact on wildlife conservation.. Key Concepts: Explore; role; saving; energy; Understand; impact; wildlife; conservation; What; Energy Saving. Introduction...
[2025-06-17 14:27:06,102] DEBUG - __main__ - main.py:2420 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] AI Inference Module Options (first 200 chars): 1. Slug: "ai_awareness_and_foundations", Name: "AI Awareness & Foundations", Description: "Conceptual journey from recognising smart helpers to understanding core AI paradigms (symbolic, statistical, ...
[2025-06-17 14:27:06,104] INFO - __main__ - main.py:2424 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] AI Inference: Calling Gemini API for module inference...
[2025-06-17 14:27:06,753] DEBUG - __main__ - main.py:2196 - extract_gemini_text: Successfully extracted 25 characters
[2025-06-17 14:27:06,755] INFO - __main__ - main.py:2434 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] AI Inference: Gemini API call completed in 0.65s. Raw response: 'ai_tools_and_applications'
[2025-06-17 14:27:06,755] DEBUG - __main__ - main.py:2456 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] AI Inference: Cleaned slug: 'ai_tools_and_applications'
[2025-06-17 14:27:06,756] INFO - __main__ - main.py:2461 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] AI Inference: Successfully matched module by slug. Chosen: 'ai_tools_and_applications' (AI Tools & Applications)
[2025-06-17 14:27:06,757] INFO - __main__ - main.py:5344 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] Successfully inferred module ID via AI: ai_tools_and_applications
[2025-06-17 14:27:06,758] INFO - __main__ - main.py:5370 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] Effective module name for prompt context: 'AI Tools & Applications' (Module ID: ai_tools_and_applications)
[2025-06-17 14:27:07,080] INFO - __main__ - main.py:1995 - No prior student performance document found for Topic: artificial_intelligence_Primary 5_artificial_intelligence_ai_tools_and_applications
[2025-06-17 14:27:07,409] WARNING - __main__ - main.py:5391 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🔍 SESSION STATE DEBUG:
[2025-06-17 14:27:07,410] WARNING - __main__ - main.py:5392 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🔍   - Session exists: True
[2025-06-17 14:27:07,410] WARNING - __main__ - main.py:5393 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🔍   - Current phase: diagnostic_start_probe
[2025-06-17 14:27:07,411] WARNING - __main__ - main.py:5394 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🔍   - State data keys: ['created_at', 'session_id', 'key_concepts_str_for_ai', 'blooms_levels_str_for_ai', 'last_diagnostic_question_text_asked', 'last_updated', 'student_name', 'diagnostic_completed_this_session', 'current_probing_level_number', 'lesson_context_snapshot', 'assigned_level_for_teaching', 'levels_probed_and_failed', 'current_phase', 'is_first_encounter_for_module', 'current_lesson_phase', 'last_update_request_id', 'current_session_working_level', 'current_question_index', 'student_id', 'last_modified', 'latest_assessed_level_for_module', 'student_answers_for_probing_level']
[2025-06-17 14:27:07,413] INFO - __main__ - main.py:5458 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] �🔍 FIRST ENCOUNTER LOGIC:
[2025-06-17 14:27:07,413] INFO - __main__ - main.py:5459 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d]   assigned_level_for_teaching (session): None
[2025-06-17 14:27:07,414] INFO - __main__ - main.py:5460 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d]   latest_assessed_level (profile): None
[2025-06-17 14:27:07,415] INFO - __main__ - main.py:5461 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d]   teaching_level_for_returning_student: None
[2025-06-17 14:27:07,416] INFO - __main__ - main.py:5462 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d]   has_completed_diagnostic_before: False
[2025-06-17 14:27:07,416] INFO - __main__ - main.py:5463 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d]   is_first_encounter_for_module: True
[2025-06-17 14:27:07,417] WARNING - __main__ - main.py:5468 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
[2025-06-17 14:27:07,417] INFO - __main__ - main.py:5474 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🔍 PHASE INVESTIGATION:
[2025-06-17 14:27:07,418] INFO - __main__ - main.py:5475 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d]   Retrieved from Firestore: 'diagnostic_start_probe'
[2025-06-17 14:27:07,418] INFO - __main__ - main.py:5476 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'diagnostic_start_probe'
[2025-06-17 14:27:07,418] INFO - __main__ - main.py:5477 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d]   Is first encounter: True
[2025-06-17 14:27:07,420] INFO - __main__ - main.py:5478 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d]   Diagnostic completed: False
[2025-06-17 14:27:07,420] INFO - __main__ - main.py:5484 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] ✅ Using stored phase from Firestore: 'diagnostic_start_probe'
[2025-06-17 14:27:07,421] INFO - __main__ - main.py:5498 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
[2025-06-17 14:27:07,421] INFO - __main__ - main.py:5500 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] Final phase for AI logic: diagnostic_start_probe
[2025-06-17 14:27:07,422] INFO - __main__ - main.py:5520 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] NEW SESSION: Forcing question_index to 0 (was: 0)
[2025-06-17 14:27:07,423] INFO - __main__ - main.py:3692 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] Diagnostic context validation passed
[2025-06-17 14:27:07,424] INFO - __main__ - main.py:3725 - DETERMINE_PHASE: Resuming in-progress diagnostic at phase: 'diagnostic_start_probe'
[2025-06-17 14:27:07,424] WARNING - __main__ - main.py:5586 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'diagnostic_start_probe' for first encounter
[2025-06-17 14:27:07,424] INFO - __main__ - main.py:3802 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] Enhanced diagnostic context with 35 fields
[2025-06-17 14:27:07,425] INFO - __main__ - main.py:5608 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] Robust diagnostic context prepared successfully. Phase: diagnostic_start_probe
[2025-06-17 14:27:07,425] DEBUG - __main__ - main.py:5609 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'diagnostic_context', 'performance_tracking', 'level_specific_guidance', 'current_phase_for_ai']
[2025-06-17 14:27:07,425] WARNING - __main__ - main.py:5617 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🤖 AI PROMPT GENERATION:
[2025-06-17 14:27:07,425] WARNING - __main__ - main.py:5618 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🤖   - Current phase: diagnostic_start_probe
[2025-06-17 14:27:07,426] WARNING - __main__ - main.py:5619 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🤖   - Student query: Yes I'm ready...
[2025-06-17 14:27:07,426] WARNING - __main__ - main.py:5620 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🤖   - Context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'diagnostic_context', 'performance_tracking', 'level_specific_guidance', 'current_phase_for_ai']
[2025-06-17 14:27:07,427] INFO - __main__ - main.py:6493 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] enhance_lesson_content invoked. Query: 'Yes I'm ready...'
[2025-06-17 14:27:07,427] INFO - __main__ - main.py:6536 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] CONTEXT DEBUG: lesson_phase from context = 'diagnostic_start_probe', processed = 'diagnostic_start_probe'
[2025-06-17 14:27:07,427] INFO - __main__ - main.py:6547 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d][enhance_lesson_content] Received from context - phase: 'diagnostic_start_probe', module_id: 'ai_tools_and_applications', gs_subject_slug: 'artificial_intelligence'
[2025-06-17 14:27:07,427] INFO - __main__ - main.py:6581 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] DIAGNOSTIC ANSWER: Storing sequential answer 2
[2025-06-17 14:27:07,428] INFO - __main__ - main.py:6586 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] DIAGNOSTIC ANSWER STORED: q2 = 'Yes I'm ready...'
[2025-06-17 14:27:07,428] INFO - __main__ - main.py:6587 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] Total diagnostic answers now: 2/5
[2025-06-17 14:27:07,428] INFO - __main__ - main.py:6628 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 📝 DIAGNOSTIC PROGRESSION: Continue with 2/5 answers, phase: diagnostic_start_probe
[2025-06-17 14:27:07,429] INFO - __main__ - main.py:6766 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d][enhance_lesson_content] Proceeding to main AI prompt generation. Phase: diagnostic_start_probe
[2025-06-17 14:27:07,429] INFO - __main__ - main.py:6773 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d][DIAGNOSTIC_FLOW] Phase: diagnostic_start_probe
[2025-06-17 14:27:07,429] INFO - __main__ - main.py:6774 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d][DIAGNOSTIC_FLOW] Questions asked: 2/5
[2025-06-17 14:27:07,429] INFO - __main__ - main.py:6775 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d][DIAGNOSTIC_FLOW] Question index: 0
[2025-06-17 14:27:07,430] INFO - __main__ - main.py:6776 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d][DIAGNOSTIC_FLOW] Student answers count: 2
[2025-06-17 14:27:07,430] INFO - __main__ - main.py:6781 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d][DIAGNOSTIC_FLOW] Answer q1: Start diagnostic assessment...
[2025-06-17 14:27:07,430] INFO - __main__ - main.py:6781 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d][DIAGNOSTIC_FLOW] Answer q2: Yes I'm ready...
[2025-06-17 14:27:07,430] INFO - __main__ - main.py:6805 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d][DIAGNOSTIC_FLOW] SIMPLIFIED LOGIC: Phase=diagnostic_start_probe, Answers=2/5
[2025-06-17 14:27:07,431] INFO - __main__ - main.py:6808 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d][DIAGNOSTIC_FLOW] SIMPLIFIED: Trusting AI to handle diagnostic completion naturally
[2025-06-17 14:27:07,431] INFO - __main__ - main.py:6839 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] INTERACTION COUNT: Updated to 1 and saved to context
[2025-06-17 14:27:07,431] INFO - __main__ - main.py:6853 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] DIAGNOSTIC PROGRESSION: Calculating next phase for diagnostic_start_probe
[2025-06-17 14:27:07,432] INFO - __main__ - main.py:6861 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] DIAGNOSTIC PROGRESSION: Running diagnostic logic for phase 'diagnostic_start_probe'
[2025-06-17 14:27:07,432] INFO - __main__ - main.py:6881 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] DIAGNOSTIC PROGRESSION: Student responded 'yes i'm ready' - AI should transition to diagnostic_probing_L5_ask_q1
[2025-06-17 14:27:07,432] INFO - __main__ - main.py:6887 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] NATURAL PROGRESSION: Trusting AI to handle phase transition with state update block
[2025-06-17 14:27:07,432] INFO - __main__ - main.py:7120 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🎯 ENHANCED PHASE CALCULATION: diagnostic_start_probe → diagnostic_probing_L5_ask_q1 (interaction 1)
[2025-06-17 14:27:07,433] WARNING - __main__ - main.py:7123 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🔍 DIAGNOSTIC FLOW START:
[2025-06-17 14:27:07,433] WARNING - __main__ - main.py:7124 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🔍   - Input phase from context: 'diagnostic_start_probe'
[2025-06-17 14:27:07,433] WARNING - __main__ - main.py:7125 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🔍   - User query: 'Yes I'm ready'
[2025-06-17 14:27:07,433] WARNING - __main__ - main.py:7126 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🔍   - Is actual student response: True
[2025-06-17 14:27:07,434] WARNING - __main__ - main.py:7127 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🔍   - Current probing level: 5
[2025-06-17 14:27:07,434] WARNING - __main__ - main.py:7128 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🔍   - Current question index: 0
[2025-06-17 14:27:07,434] WARNING - __main__ - main.py:7129 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🔍   - Student answers count: 2
[2025-06-17 14:27:07,434] INFO - __main__ - main.py:7132 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🔧 NATURAL PROGRESSION DEBUG:
[2025-06-17 14:27:07,435] INFO - __main__ - main.py:7133 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🔧   - lesson_phase_from_context: 'diagnostic_start_probe'
[2025-06-17 14:27:07,435] INFO - __main__ - main.py:7134 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🔧   - python_calculated_new_phase_for_block: 'diagnostic_probing_L5_ask_q1'
[2025-06-17 14:27:07,437] INFO - __main__ - main.py:7135 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🔧   - user_query: 'Yes I'm ready'
[2025-06-17 14:27:07,438] INFO - __main__ - main.py:7136 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🔧   - trusting_ai_state_updates: True
[2025-06-17 14:27:07,438] WARNING - __main__ - main.py:7139 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🎯 PHASE CALCULATION RESULT:
[2025-06-17 14:27:07,438] WARNING - __main__ - main.py:7140 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🎯   - Current phase: 'diagnostic_start_probe'
[2025-06-17 14:27:07,439] WARNING - __main__ - main.py:7141 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🎯   - Calculated new phase: 'diagnostic_probing_L5_ask_q1'
[2025-06-17 14:27:07,440] WARNING - __main__ - main.py:7142 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🎯   - Phase changed: True
[2025-06-17 14:27:07,440] WARNING - __main__ - main.py:7145 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🎯 DIAGNOSTIC SPECIFIC:
[2025-06-17 14:27:07,440] WARNING - __main__ - main.py:7146 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🎯   - In diagnostic phase: True
[2025-06-17 14:27:07,440] WARNING - __main__ - main.py:7147 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🎯   - Student answers stored: {'q1': 'Start diagnostic assessment', 'q2': "Yes I'm ready"}
[2025-06-17 14:27:07,441] WARNING - __main__ - main.py:7148 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🎯   - Expected progression: Should move to next question/phase
[2025-06-17 14:27:07,441] INFO - __main__ - main.py:1829 - ENHANCED lesson content extraction: subject='Artificial Intelligence', topic='How AI helps the planet', key_concepts='What, Energy, Saving, Wildlife, Conservation', examples=0
[2025-06-17 14:27:07,441] INFO - __main__ - main.py:7209 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d][enhance_lesson_content] Python-calculated TARGET values for AI's output state block: new_phase='diagnostic_probing_L5_ask_q1', q_index='0', total_q_asked='1'
[2025-06-17 14:27:07,442] INFO - __main__ - main.py:7215 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d][diagnostic_logic] SIMPLIFIED: Trusting AI to handle diagnostic progression naturally
[2025-06-17 14:27:07,442] WARNING - __main__ - main.py:7240 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🚀 DIAGNOSTIC PHASE OVERRIDE: AI will receive 'diagnostic_probing_L5_ask_q1' instead of 'diagnostic_start_probe'
[2025-06-17 14:27:07,442] WARNING - __main__ - main.py:7241 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🚀 REASON: Student responded to diagnostic_start_probe, need to progress to Q1
[2025-06-17 14:27:07,442] INFO - __main__ - main.py:7320 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] NATURAL COMPLETION: Trusting AI to handle diagnostic completion naturally
[2025-06-17 14:27:07,443] INFO - __main__ - main.py:7331 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] SIMPLIFIED: Trusting AI to determine appropriate phase transitions naturally
[2025-06-17 14:27:07,443] INFO - __main__ - main.py:7337 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] ✅ Template placeholder successfully substituted
[2025-06-17 14:27:07,443] INFO - __main__ - main.py:7341 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] ✅ Template contains calculated phase: diagnostic_probing_L5_ask_q1
[2025-06-17 14:27:07,444] INFO - __main__ - main.py:7385 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] Prompt truncated from 12456 to 10667 chars for performance
[2025-06-17 14:27:07,444] INFO - __main__ - main.py:7388 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] CONTENT QUALITY DEBUG:
[2025-06-17 14:27:07,444] INFO - __main__ - main.py:7389 -   - Final prompt length: 10667 characters
[2025-06-17 14:27:07,445] INFO - __main__ - main.py:7390 -   - Subject: Artificial Intelligence
[2025-06-17 14:27:07,445] INFO - __main__ - main.py:7391 -   - Topic: How AI helps the planet
[2025-06-17 14:27:07,445] INFO - __main__ - main.py:7392 -   - Key concepts: What, Energy, Saving, Wildlife, Conservation
[2025-06-17 14:27:07,446] INFO - __main__ - main.py:7393 -   - Grade: Primary 5
[2025-06-17 14:27:07,446] INFO - __main__ - main.py:7394 -   - Phase: diagnostic_start_probe
[2025-06-17 14:27:07,446] INFO - __main__ - main.py:7395 -   - Student: Andrea
[2025-06-17 14:27:07,446] DEBUG - __main__ - main.py:7396 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] Prompt preview: 
MANDATORY: End with: // AI_STATE_UPDATE_BLOCK_START {"new_phase": "diagnostic_probing_L5_ask_q1", "interaction_count": 1} // AI_STATE_UPDATE_BLOCK_END

CRITICAL PHASE PROGRESSION: 
• The system has calculated the next phase as: diagnostic_probing_L5_ask_q1
• You MUST use this exact phase name in yo...
[2025-06-17 14:27:07,447] INFO - __main__ - main.py:7424 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] Gemini API call attempt 1/3
[2025-06-17 14:27:08,649] INFO - __main__ - main.py:7442 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] ✅ Gemini API call succeeded on attempt 1
[2025-06-17 14:27:08,650] INFO - __main__ - main.py:7461 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] Gemini API call completed in 1.20s
[2025-06-17 14:27:08,650] DEBUG - __main__ - main.py:2196 - extract_gemini_text: Successfully extracted 358 characters
[2025-06-17 14:27:08,651] INFO - __main__ - main.py:7684 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d][enhance_lesson_content] AI response: Great! Let's start with our first question about how AI helps the planet:  Imagine a large national park in Nigeria filled with endangered animals. How could AI be used to help protect these animals a...
[2025-06-17 14:27:08,651] INFO - __main__ - main.py:7712 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🔍 STATE DEBUG: Looking for state update block in AI response
[2025-06-17 14:27:08,653] INFO - __main__ - main.py:7713 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🔍 STATE DEBUG: AI response length: 358 chars
[2025-06-17 14:27:08,654] INFO - __main__ - main.py:7725 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🔍 STATE DEBUG: Found state block: {"new_phase": "diagnostic_probing_L5_ask_q1", "interaction_count": 1}
[2025-06-17 14:27:08,655] INFO - __main__ - main.py:7727 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] ✅ Found AI state update: {'new_phase': 'diagnostic_probing_L5_ask_q1', 'interaction_count': 1}
[2025-06-17 14:27:08,656] INFO - __main__ - main.py:7791 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] SIMPLIFIED: Trusting AI state updates without aggressive diagnostic forcing
[2025-06-17 14:27:08,657] INFO - __main__ - main.py:7797 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] SIMPLIFIED: Using AI response without hardcoded fallbacks
[2025-06-17 14:27:08,658] INFO - __main__ - main.py:7817 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] Auto-corrected: added missing current_probing_level_number
[2025-06-17 14:27:08,659] INFO - __main__ - main.py:7821 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] Auto-corrected: added missing current_question_index
[2025-06-17 14:27:08,659] INFO - __main__ - main.py:7847 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] ✅ AI state validation completed: {'new_phase': 'diagnostic_probing_L5_ask_q1', 'interaction_count': 1, 'current_probing_level_number': 5, 'current_question_index': 0}
[2025-06-17 14:27:08,660] INFO - __main__ - main.py:7859 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] ✅ AI PROVIDED VALID STATE: Using AI's state update proposal
[2025-06-17 14:27:08,660] INFO - __main__ - main.py:7902 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] Placeholder apply_phase_transition_fixes detected or no fix made. Prioritizing AI's originally proposed state.
[2025-06-17 14:27:08,660] INFO - __main__ - main.py:7911 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] State updates after apply_fixes and potential AI prioritization: {'new_phase': 'diagnostic_probing_L5_ask_q1', 'interaction_count': 1, 'current_probing_level_number': 5, 'current_question_index': 0}
[2025-06-17 14:27:08,661] INFO - __main__ - main.py:7969 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 📝 DIAGNOSTIC PROGRESSION: Continue diagnostic - eval_phase=False, answers=2/5
[2025-06-17 14:27:08,661] INFO - __main__ - main.py:7972 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 📝 DIAGNOSTIC PROGRESSION: Need 3 more answers
[2025-06-17 14:27:08,661] INFO - __main__ - main.py:7974 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 📝 DIAGNOSTIC PROGRESSION: Not in eval_q5_decide_level phase
[2025-06-17 14:27:08,662] INFO - __main__ - main.py:7978 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] NATURAL COMPLETION: Trusting AI to handle diagnostic completion naturally
[2025-06-17 14:27:08,662] INFO - __main__ - main.py:8010 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] DIAGNOSTIC PROGRESSION: AI provided phase transition: diagnostic_start_probe → diagnostic_probing_L5_ask_q1 - trusting AI
[2025-06-17 14:27:08,662] INFO - __main__ - main.py:8055 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] DIAGNOSTIC FALLBACK: Applying calculated progression only when AI fails to provide updates: diagnostic_start_probe → diagnostic_probing_L5_ask_q1
[2025-06-17 14:27:08,663] WARNING - __main__ - main.py:8070 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🔍 DIAGNOSTIC FALLBACK STATE UPDATE:
[2025-06-17 14:27:08,663] WARNING - __main__ - main.py:8071 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🔍   - Phase: diagnostic_start_probe → diagnostic_probing_L5_ask_q1
[2025-06-17 14:27:08,664] WARNING - __main__ - main.py:8072 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🔍   - Question Index: 0
[2025-06-17 14:27:08,664] WARNING - __main__ - main.py:8073 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🔍   - Probing Level: 5
[2025-06-17 14:27:08,664] INFO - __main__ - main.py:8077 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] DIAGNOSTIC FALLBACK: System provided fallback state updates when AI failed to respond
[2025-06-17 14:27:08,665] INFO - __main__ - main.py:8078 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] DIAGNOSTIC ENFORCEMENT: Original AI phase: diagnostic_probing_L5_ask_q1, Enforced phase: diagnostic_probing_L5_ask_q1
[2025-06-17 14:27:08,665] INFO - __main__ - main.py:8098 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] [OK] State update block guaranteed by system enforcement
[2025-06-17 14:27:08,666] INFO - __main__ - main.py:8104 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] Phase transition: diagnostic_start_probe -> diagnostic_probing_L5_ask_q1
[2025-06-17 14:27:08,666] INFO - __main__ - main.py:3972 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] VALIDATING DIAGNOSTIC PHASE SEQUENCE: diagnostic_start_probe → diagnostic_probing_L5_ask_q1
[2025-06-17 14:27:08,667] INFO - __main__ - main.py:4074 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] ✅ VALID DIAGNOSTIC TRANSITION: diagnostic_start_probe → diagnostic_probing_L5_ask_q1
[2025-06-17 14:27:08,667] INFO - __main__ - main.py:8180 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 📋 FINAL STATE UPDATE: {'new_phase': 'diagnostic_probing_L5_ask_q1', 'current_probing_level_number': 5, 'current_question_index': 0, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': 'Start diagnostic assessment', 'q2': "Yes I'm ready"}, 'levels_probed_and_failed': []}
[2025-06-17 14:27:08,667] INFO - __main__ - main.py:8216 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] ✅ Clean response prepared for user, state updates handled internally
[2025-06-17 14:27:08,668] INFO - __main__ - main.py:8227 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🔧 FORCED personalization injection
[2025-06-17 14:27:08,669] INFO - __main__ - main.py:8238 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] [OK] State updates processed: {'new_phase': 'diagnostic_probing_L5_ask_q1', 'current_probing_level_number': 5, 'current_question_index': 0, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': 'Start diagnostic assessment', 'q2': "Yes I'm ready"}, 'levels_probed_and_failed': []}
[2025-06-17 14:27:08,669] INFO - __main__ - main.py:8241 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] SIMPLIFIED: Diagnostic enforcement disabled to prevent backward transitions
[2025-06-17 14:27:08,670] WARNING - __main__ - main.py:8262 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🔄 STATE UPDATE PROCESSING:
[2025-06-17 14:27:08,671] WARNING - __main__ - main.py:8263 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🔄   - AI provided state update: True
[2025-06-17 14:27:08,671] WARNING - __main__ - main.py:8265 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🔄   - AI proposed phase: 'diagnostic_probing_L5_ask_q1'
[2025-06-17 14:27:08,672] WARNING - __main__ - main.py:8266 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🔄   - Current lesson phase: 'diagnostic_start_probe'
[2025-06-17 14:27:08,673] WARNING - __main__ - main.py:8267 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🔄   - Python calculated phase: 'diagnostic_probing_L5_ask_q1'
[2025-06-17 14:27:08,673] INFO - __main__ - main.py:8275 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] ⚡ PERFORMANCE METRICS:
[2025-06-17 14:27:08,674] INFO - __main__ - main.py:8276 -   - Total execution time: 1.247s
[2025-06-17 14:27:08,675] WARNING - __main__ - main.py:5643 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🤖 AI RESPONSE RECEIVED:
[2025-06-17 14:27:08,675] WARNING - __main__ - main.py:5644 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🤖   - Content length: 237 chars
[2025-06-17 14:27:08,675] WARNING - __main__ - main.py:5645 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🤖   - State updates: {'new_phase': 'diagnostic_probing_L5_ask_q1', 'current_probing_level_number': 5, 'current_question_index': 0, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': 'Start diagnostic assessment', 'q2': "Yes I'm ready"}, 'levels_probed_and_failed': []}
[2025-06-17 14:27:08,676] WARNING - __main__ - main.py:5646 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🤖   - Raw state block: None...
[2025-06-17 14:27:08,676] INFO - __main__ - main.py:5662 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
[2025-06-17 14:27:08,677] INFO - __main__ - main.py:5663 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] CURRENT PHASE DETERMINATION: AI=diagnostic_probing_L5_ask_q1, Session=diagnostic_start_probe, Final=diagnostic_probing_L5_ask_q1
[2025-06-17 14:27:09,273] INFO - __main__ - main.py:5772 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] AI processing completed in 1.85s
[2025-06-17 14:27:09,274] WARNING - __main__ - main.py:5783 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🔍 STATE UPDATE VALIDATION: current_phase='diagnostic_start_probe', new_phase='diagnostic_probing_L5_ask_q1'
[2025-06-17 14:27:09,274] INFO - __main__ - main.py:3897 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] AI state update validation passed: diagnostic_start_probe → diagnostic_probing_L5_ask_q1
[2025-06-17 14:27:09,275] WARNING - __main__ - main.py:5792 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] ✅ STATE UPDATE VALIDATION PASSED
[2025-06-17 14:27:09,275] WARNING - __main__ - main.py:5797 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🔄 PHASE TRANSITION: diagnostic_start_probe → diagnostic_probing_L5_ask_q1
[2025-06-17 14:27:09,276] WARNING - __main__ - main.py:5806 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🔍 COMPLETE PHASE FLOW DEBUG:
[2025-06-17 14:27:09,276] WARNING - __main__ - main.py:5807 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🔍   1. Input phase: 'diagnostic_start_probe'
[2025-06-17 14:27:09,277] WARNING - __main__ - main.py:5808 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🔍   2. Python calculated next phase: 'NOT_FOUND'
[2025-06-17 14:27:09,278] WARNING - __main__ - main.py:5809 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🔍   3. AI state updates: {'new_phase': 'diagnostic_probing_L5_ask_q1', 'current_probing_level_number': 5, 'current_question_index': 0, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': 'Start diagnostic assessment', 'q2': "Yes I'm ready"}, 'levels_probed_and_failed': []}
[2025-06-17 14:27:09,279] WARNING - __main__ - main.py:5810 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🔍   4. Final phase to save: 'diagnostic_probing_L5_ask_q1'
[2025-06-17 14:27:09,280] WARNING - __main__ - main.py:5813 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 💾 FINAL STATE APPLICATION:
[2025-06-17 14:27:09,280] WARNING - __main__ - main.py:5814 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 💾   - Current phase input: 'diagnostic_start_probe'
[2025-06-17 14:27:09,281] WARNING - __main__ - main.py:5815 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 💾   - State updates from AI: {'new_phase': 'diagnostic_probing_L5_ask_q1', 'current_probing_level_number': 5, 'current_question_index': 0, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': 'Start diagnostic assessment', 'q2': "Yes I'm ready"}, 'levels_probed_and_failed': []}
[2025-06-17 14:27:09,281] WARNING - __main__ - main.py:5816 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 💾   - Final phase to save: 'diagnostic_probing_L5_ask_q1'
[2025-06-17 14:27:09,282] WARNING - __main__ - main.py:5817 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 💾   - Phase change: True
[2025-06-17 14:27:09,282] INFO - __main__ - main.py:3929 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] DIAGNOSTIC_FLOW_METRICS:
[2025-06-17 14:27:09,283] INFO - __main__ - main.py:3930 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d]   Phase transition: diagnostic_start_probe -> diagnostic_probing_L5_ask_q1
[2025-06-17 14:27:09,283] INFO - __main__ - main.py:3931 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d]   Current level: 5
[2025-06-17 14:27:09,283] INFO - __main__ - main.py:3932 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d]   Question index: 0
[2025-06-17 14:27:09,284] INFO - __main__ - main.py:3933 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d]   First encounter: True
[2025-06-17 14:27:09,284] INFO - __main__ - main.py:3938 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d]   Answers collected: 2
[2025-06-17 14:27:09,284] INFO - __main__ - main.py:3939 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d]   Levels failed: 0
[2025-06-17 14:27:09,285] INFO - __main__ - main.py:3897 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] AI state update validation passed: diagnostic_start_probe → diagnostic_probing_L5_ask_q1
[2025-06-17 14:27:09,286] INFO - __main__ - main.py:3943 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d]   State update valid: True
[2025-06-17 14:27:09,289] INFO - __main__ - main.py:3950 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d]   Diagnostic complete: False
[2025-06-17 14:27:09,290] WARNING - __main__ - main.py:5829 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 0, current_q_index_for_prompt = NOT_FOUND
[2025-06-17 14:27:09,637] WARNING - __main__ - main.py:5856 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] ✅ SESSION STATE SAVED TO FIRESTORE:
[2025-06-17 14:27:09,637] WARNING - __main__ - main.py:5857 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] ✅   - Phase: diagnostic_probing_L5_ask_q1
[2025-06-17 14:27:09,638] WARNING - __main__ - main.py:5858 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] ✅   - Probing Level: 5
[2025-06-17 14:27:09,639] WARNING - __main__ - main.py:5859 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] ✅   - Question Index: 0
[2025-06-17 14:27:09,639] WARNING - __main__ - main.py:5860 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] ✅   - Diagnostic Complete: False
[2025-06-17 14:27:10,368] INFO - __main__ - main.py:5919 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] ✅ Updated existing session document: session_b71023f5-62ee-482e-be3a-783fab2fb64c
[2025-06-17 14:27:10,369] WARNING - __main__ - main.py:5920 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] ✅ SESSION UPDATE COMPLETE:
[2025-06-17 14:27:10,370] WARNING - __main__ - main.py:5921 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] ✅   - Session ID: session_b71023f5-62ee-482e-be3a-783fab2fb64c
[2025-06-17 14:27:10,371] WARNING - __main__ - main.py:5922 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] ✅   - Phase transition: diagnostic_start_probe → diagnostic_probing_L5_ask_q1
[2025-06-17 14:27:10,371] WARNING - __main__ - main.py:5923 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] ✅   - Interaction logged successfully
[2025-06-17 14:27:10,372] INFO - __main__ - main.py:9173 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
[2025-06-17 14:27:10,373] DEBUG - __main__ - main.py:2753 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] FINAL_ASSESSMENT_BLOCK not found in AI response.
[2025-06-17 14:27:10,373] DEBUG - __main__ - main.py:5969 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] No final assessment data found in AI response
[2025-06-17 14:27:10,374] INFO - __main__ - main.py:6034 - [90c6c67a-5a8a-4ae8-9872-80cfb89c7c9d] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
[2025-06-17 14:27:10,376] WARNING - __main__ - main.py:625 - High response time detected: 4.93s for enhance_content_api
[2025-06-17 14:27:10,377] INFO - werkzeug - _internal.py:97 - 127.0.0.1 - - [17/Jun/2025 14:27:10] "POST /api/enhance-content HTTP/1.1" 200 -
[2025-06-17 14:29:21,854] INFO - __main__ - main.py:4831 - REQUEST: POST http://localhost:5000/api/enhance-content -> endpoint: enhance_content_api
[2025-06-17 14:29:21,870] INFO - __main__ - main.py:4874 - Incoming request: {"request_id": "4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77", "timestamp": "2025-06-17T13:29:21.864012+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.9.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-AI-001", "content_to_enhance": "Hi! This sounds like a superhero job for computers!  Robot Eyes (AI Cameras): AI could use special cameras that are always watching the park. If a poacher (someone who tries to hurt animals illegally) comes into the park, the AI could instantly see them and tell the park rangers! It could also count animals, like elephants or lions, to see if their numbers are going up or down.   Listening Ears (AI Microphones): AI could listen for sounds like gunshots or chainsaws (cutting down trees). If it hears something bad, it can tell the rangers right away so they can go investigate.  Smart Trackers on Animals: They could put tiny trackers on some animals, and AI could watch where they go. If an animal goes somewhere dangerous, or if it stops moving for too long, the AI could alert the rangers that something might be wrong.  Watching the Forest: AI could look at pictures from satellites (like big cameras in space) to see if trees are being cut down where they shouldn't be, or if there's a bushfire starting. It helps keep the animals' homes safe!  So, AI is like having super smart, tireless helpers always watching and listening to protect our animals!", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "Primary 5", "level": "P5", "subject": "Artificial Intelligence", "session_id": "session_b71023f5-62ee-482e-be3a-783fab2fb64c", "chat_history": [{"role": "assistant", "content": "Hi Andrea! I'm your AI tutor, and I'm excited to help you learn about how Artificial Intelligence helps our planet. We'll start with a quick diagnostic assessment to see what you already know. This helps me tailor the lessons perfectly for you. Are you ready to begin with some questions about what AI is, how it saves energy, protects wildlife, and helps with conservation?", "timestamp": "2025-06-17T13:26:37.197Z"}, {"role": "user", "content": "Yes I'm ready", "timestamp": "2025-06-17T13:27:05.374Z"}, {"role": "assistant", "content": "Hi Andrea! Great! Let's start with our first question about how AI helps the planet: Imagine a large national park in Nigeria filled with endangered animals. How could AI be used to help protect these animals and conserve their habitats?", "timestamp": "2025-06-17T13:27:10.398Z"}]}}
[2025-06-17 14:29:21,873] INFO - temporary_auth_bypass - temporary_auth_bypass.py:29 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] Using temporary auth bypass with mock token
[2025-06-17 14:29:21,880] DEBUG - asyncio - proactor_events.py:631 - Using proactor: IocpProactor
[2025-06-17 14:29:21,895] WARNING - __main__ - main.py:5067 - 🔥 ENHANCE-CONTENT ENDPOINT CALLED!
[2025-06-17 14:29:21,899] INFO - __main__ - main.py:5178 - [enhance_content_api] Received request with decoded token.
[2025-06-17 14:29:21,899] INFO - __main__ - main.py:5180 - [enhance_content_api][4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] Processing enhance content request
[2025-06-17 14:29:21,900] INFO - __main__ - main.py:5226 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77][enhance_content_api] Parsed JSON payload (first 250 bytes): {'student_id': 'andrea_ugono_33305', 'lesson_ref': 'P5-AI-001', 'content_to_enhance': "Hi! This sounds like a superhero job for computers!  Robot Eyes (AI Cameras): AI could use special cameras that are always watching the park. If a poacher (someone
[2025-06-17 14:29:22,961] INFO - __main__ - main.py:4424 - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
[2025-06-17 14:29:22,967] DEBUG - __main__ - main.py:596 - Cache hit for fetch_lesson_data
[2025-06-17 14:29:22,968] INFO - __main__ - main.py:5272 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] ✅ All required fields present after lesson content parsing and mapping
[2025-06-17 14:29:22,971] INFO - __main__ - main.py:5311 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] Attempting to infer module. GS Subject Slug: 'artificial_intelligence'.
[2025-06-17 14:29:22,975] INFO - __main__ - main.py:2273 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] AI Inference: Inferring module for subject 'artificial_intelligence', lesson 'AI: Saving Our Planet!'.
[2025-06-17 14:29:23,427] INFO - __main__ - main.py:2332 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] AI Inference: Loaded metadata for module 'ai_awareness_and_foundations' ('AI Awareness & Foundations')
[2025-06-17 14:29:23,428] INFO - __main__ - main.py:2332 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] AI Inference: Loaded metadata for module 'ai_tools_and_applications' ('AI Tools & Applications')
[2025-06-17 14:29:23,428] INFO - __main__ - main.py:2332 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] AI Inference: Loaded metadata for module 'computational_thinking_and_coding' ('Computational Thinking & Coding')
[2025-06-17 14:29:23,428] INFO - __main__ - main.py:2332 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] AI Inference: Loaded metadata for module 'data_literacy_and_machine_learning' ('Data Literacy & Machine Learning')
[2025-06-17 14:29:23,429] INFO - __main__ - main.py:2332 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] AI Inference: Loaded metadata for module 'ethics_and_societal_impact' ('Ethics & Societal Impact')
[2025-06-17 14:29:23,431] INFO - __main__ - main.py:2401 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] AI Inference: Starting module inference for subject 'artificial_intelligence' with 5 module options
[2025-06-17 14:29:23,432] DEBUG - __main__ - main.py:2415 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] AI Inference: Sample modules available (first 3):
- ai_awareness_and_foundations: AI Awareness & Foundations
- ai_tools_and_applications: AI Tools & Applications
- computational_thinking_and_coding: Computational Thinking & Coding
[2025-06-17 14:29:23,432] DEBUG - __main__ - main.py:2418 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] AI Inference Prompt (first 500 chars): 
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: AI: Saving Our Planet!. Topic: How AI helps the planet. Learning Objectives: Explore AI’s role in saving energy.; Understand AI’s impact on wildlife conservation.. Key Concepts: Explore; role; saving; energy; Understand; impact; wildlife; conservation; What; Energy Saving. Introducti...
[2025-06-17 14:29:23,434] DEBUG - __main__ - main.py:2419 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] AI Inference Lesson Summary (first 300 chars): Lesson Title: AI: Saving Our Planet!. Topic: How AI helps the planet. Learning Objectives: Explore AI’s role in saving energy.; Understand AI’s impact on wildlife conservation.. Key Concepts: Explore; role; saving; energy; Understand; impact; wildlife; conservation; What; Energy Saving. Introduction...
[2025-06-17 14:29:23,435] DEBUG - __main__ - main.py:2420 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] AI Inference Module Options (first 200 chars): 1. Slug: "ai_awareness_and_foundations", Name: "AI Awareness & Foundations", Description: "Conceptual journey from recognising smart helpers to understanding core AI paradigms (symbolic, statistical, ...
[2025-06-17 14:29:23,435] INFO - __main__ - main.py:2424 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] AI Inference: Calling Gemini API for module inference...
[2025-06-17 14:29:24,094] DEBUG - __main__ - main.py:2196 - extract_gemini_text: Successfully extracted 25 characters
[2025-06-17 14:29:24,094] INFO - __main__ - main.py:2434 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] AI Inference: Gemini API call completed in 0.66s. Raw response: 'ai_tools_and_applications'
[2025-06-17 14:29:24,095] DEBUG - __main__ - main.py:2456 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] AI Inference: Cleaned slug: 'ai_tools_and_applications'
[2025-06-17 14:29:24,095] INFO - __main__ - main.py:2461 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] AI Inference: Successfully matched module by slug. Chosen: 'ai_tools_and_applications' (AI Tools & Applications)
[2025-06-17 14:29:24,095] INFO - __main__ - main.py:5344 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] Successfully inferred module ID via AI: ai_tools_and_applications
[2025-06-17 14:29:24,100] INFO - __main__ - main.py:5370 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] Effective module name for prompt context: 'AI Tools & Applications' (Module ID: ai_tools_and_applications)
[2025-06-17 14:29:24,401] INFO - __main__ - main.py:1995 - No prior student performance document found for Topic: artificial_intelligence_Primary 5_artificial_intelligence_ai_tools_and_applications
[2025-06-17 14:29:24,894] WARNING - __main__ - main.py:5391 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🔍 SESSION STATE DEBUG:
[2025-06-17 14:29:24,895] WARNING - __main__ - main.py:5392 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🔍   - Session exists: True
[2025-06-17 14:29:24,897] WARNING - __main__ - main.py:5393 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🔍   - Current phase: diagnostic_probing_L5_ask_q1
[2025-06-17 14:29:24,898] WARNING - __main__ - main.py:5394 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🔍   - State data keys: ['created_at', 'session_id', 'key_concepts_str_for_ai', 'blooms_levels_str_for_ai', 'last_diagnostic_question_text_asked', 'last_updated', 'student_name', 'diagnostic_completed_this_session', 'current_probing_level_number', 'lesson_context_snapshot', 'assigned_level_for_teaching', 'levels_probed_and_failed', 'is_first_encounter_for_module', 'current_phase', 'current_lesson_phase', 'last_update_request_id', 'current_session_working_level', 'current_question_index', 'student_id', 'last_modified', 'latest_assessed_level_for_module', 'student_answers_for_probing_level']
[2025-06-17 14:29:24,900] INFO - __main__ - main.py:5458 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] �🔍 FIRST ENCOUNTER LOGIC:
[2025-06-17 14:29:24,901] INFO - __main__ - main.py:5459 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77]   assigned_level_for_teaching (session): None
[2025-06-17 14:29:24,902] INFO - __main__ - main.py:5460 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77]   latest_assessed_level (profile): None
[2025-06-17 14:29:24,902] INFO - __main__ - main.py:5461 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77]   teaching_level_for_returning_student: None
[2025-06-17 14:29:24,903] INFO - __main__ - main.py:5462 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77]   has_completed_diagnostic_before: False
[2025-06-17 14:29:24,904] INFO - __main__ - main.py:5463 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77]   is_first_encounter_for_module: True
[2025-06-17 14:29:24,905] WARNING - __main__ - main.py:5468 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
[2025-06-17 14:29:24,906] INFO - __main__ - main.py:5474 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🔍 PHASE INVESTIGATION:
[2025-06-17 14:29:24,906] INFO - __main__ - main.py:5475 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77]   Retrieved from Firestore: 'diagnostic_probing_L5_ask_q1'
[2025-06-17 14:29:24,907] INFO - __main__ - main.py:5476 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'diagnostic_start_probe'
[2025-06-17 14:29:24,907] INFO - __main__ - main.py:5477 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77]   Is first encounter: True
[2025-06-17 14:29:24,907] INFO - __main__ - main.py:5478 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77]   Diagnostic completed: False
[2025-06-17 14:29:24,908] INFO - __main__ - main.py:5484 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] ✅ Using stored phase from Firestore: 'diagnostic_probing_L5_ask_q1'
[2025-06-17 14:29:24,908] INFO - __main__ - main.py:5498 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
[2025-06-17 14:29:24,909] INFO - __main__ - main.py:5500 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] Final phase for AI logic: diagnostic_probing_L5_ask_q1
[2025-06-17 14:29:24,909] INFO - __main__ - main.py:5520 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] NEW SESSION: Forcing question_index to 0 (was: 0)
[2025-06-17 14:29:24,910] INFO - __main__ - main.py:3692 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] Diagnostic context validation passed
[2025-06-17 14:29:24,911] INFO - __main__ - main.py:3725 - DETERMINE_PHASE: Resuming in-progress diagnostic at phase: 'diagnostic_probing_L5_ask_q1'
[2025-06-17 14:29:24,911] WARNING - __main__ - main.py:5586 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'diagnostic_probing_L5_ask_q1' for first encounter
[2025-06-17 14:29:24,917] INFO - __main__ - main.py:3802 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] Enhanced diagnostic context with 35 fields
[2025-06-17 14:29:24,917] INFO - __main__ - main.py:5608 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] Robust diagnostic context prepared successfully. Phase: diagnostic_probing_L5_ask_q1
[2025-06-17 14:29:24,917] DEBUG - __main__ - main.py:5609 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'diagnostic_context', 'performance_tracking', 'level_specific_guidance', 'current_phase_for_ai']
[2025-06-17 14:29:24,918] WARNING - __main__ - main.py:5617 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🤖 AI PROMPT GENERATION:
[2025-06-17 14:29:24,918] WARNING - __main__ - main.py:5618 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🤖   - Current phase: diagnostic_start_probe
[2025-06-17 14:29:24,918] WARNING - __main__ - main.py:5619 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🤖   - Student query: Hi! This sounds like a superhero job for computers!  Robot Eyes (AI Cameras): AI could use special c...
[2025-06-17 14:29:24,919] WARNING - __main__ - main.py:5620 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🤖   - Context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'diagnostic_context', 'performance_tracking', 'level_specific_guidance', 'current_phase_for_ai']
[2025-06-17 14:29:24,923] INFO - __main__ - main.py:6493 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] enhance_lesson_content invoked. Query: 'Hi! This sounds like a superhero job for computers...'
[2025-06-17 14:29:24,923] INFO - __main__ - main.py:6536 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] CONTEXT DEBUG: lesson_phase from context = 'diagnostic_probing_L5_ask_q1', processed = 'diagnostic_probing_L5_ask_q1'
[2025-06-17 14:29:24,924] INFO - __main__ - main.py:6547 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77][enhance_lesson_content] Received from context - phase: 'diagnostic_probing_L5_ask_q1', module_id: 'ai_tools_and_applications', gs_subject_slug: 'artificial_intelligence'
[2025-06-17 14:29:24,924] INFO - __main__ - main.py:6581 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] DIAGNOSTIC ANSWER: Storing sequential answer 3
[2025-06-17 14:29:24,924] INFO - __main__ - main.py:6586 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] DIAGNOSTIC ANSWER STORED: q3 = 'Hi! This sounds like a superhero job for computers...'
[2025-06-17 14:29:24,925] INFO - __main__ - main.py:6587 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] Total diagnostic answers now: 3/5
[2025-06-17 14:29:24,925] INFO - __main__ - main.py:6628 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 📝 DIAGNOSTIC PROGRESSION: Continue with 3/5 answers, phase: diagnostic_probing_L5_ask_q1
[2025-06-17 14:29:24,927] INFO - __main__ - main.py:6766 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77][enhance_lesson_content] Proceeding to main AI prompt generation. Phase: diagnostic_probing_L5_ask_q1
[2025-06-17 14:29:24,927] INFO - __main__ - main.py:6773 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77][DIAGNOSTIC_FLOW] Phase: diagnostic_probing_L5_ask_q1
[2025-06-17 14:29:24,927] INFO - __main__ - main.py:6774 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77][DIAGNOSTIC_FLOW] Questions asked: 3/5
[2025-06-17 14:29:24,928] INFO - __main__ - main.py:6775 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77][DIAGNOSTIC_FLOW] Question index: 0
[2025-06-17 14:29:24,928] INFO - __main__ - main.py:6776 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77][DIAGNOSTIC_FLOW] Student answers count: 3
[2025-06-17 14:29:24,928] INFO - __main__ - main.py:6781 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77][DIAGNOSTIC_FLOW] Answer q1: Start diagnostic assessment...
[2025-06-17 14:29:24,929] INFO - __main__ - main.py:6781 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77][DIAGNOSTIC_FLOW] Answer q2: Yes I'm ready...
[2025-06-17 14:29:24,929] INFO - __main__ - main.py:6781 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77][DIAGNOSTIC_FLOW] Answer q3: Hi! This sounds like a superhero job for computers...
[2025-06-17 14:29:24,929] INFO - __main__ - main.py:6805 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77][DIAGNOSTIC_FLOW] SIMPLIFIED LOGIC: Phase=diagnostic_probing_L5_ask_q1, Answers=3/5
[2025-06-17 14:29:24,929] INFO - __main__ - main.py:6808 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77][DIAGNOSTIC_FLOW] SIMPLIFIED: Trusting AI to handle diagnostic completion naturally
[2025-06-17 14:29:24,930] INFO - __main__ - main.py:6839 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] INTERACTION COUNT: Updated to 1 and saved to context
[2025-06-17 14:29:24,930] INFO - __main__ - main.py:6853 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] DIAGNOSTIC PROGRESSION: Calculating next phase for diagnostic_probing_L5_ask_q1
[2025-06-17 14:29:24,930] INFO - __main__ - main.py:6861 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] DIAGNOSTIC PROGRESSION: Running diagnostic logic for phase 'diagnostic_probing_L5_ask_q1'
[2025-06-17 14:29:24,930] INFO - __main__ - main.py:7120 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🎯 ENHANCED PHASE CALCULATION: diagnostic_probing_L5_ask_q1 → diagnostic_probing_L5_ask_q1 (interaction 1)
[2025-06-17 14:29:24,931] WARNING - __main__ - main.py:7123 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🔍 DIAGNOSTIC FLOW START:
[2025-06-17 14:29:24,932] WARNING - __main__ - main.py:7124 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🔍   - Input phase from context: 'diagnostic_probing_L5_ask_q1'
[2025-06-17 14:29:24,932] WARNING - __main__ - main.py:7125 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🔍   - User query: 'Hi! This sounds like a superhero job for computers!  Robot Eyes (AI Cameras): AI could use special cameras that are always watching the park. If a poacher (someone who tries to hurt animals illegally) comes into the park, the AI could instantly see them and tell the park rangers! It could also count animals, like elephants or lions, to see if their numbers are going up or down.   Listening Ears (AI Microphones): AI could listen for sounds like gunshots or chainsaws (cutting down trees). If it hears something bad, it can tell the rangers right away so they can go investigate.  Smart Trackers on Animals: They could put tiny trackers on some animals, and AI could watch where they go. If an animal goes somewhere dangerous, or if it stops moving for too long, the AI could alert the rangers that something might be wrong.  Watching the Forest: AI could look at pictures from satellites (like big cameras in space) to see if trees are being cut down where they shouldn't be, or if there's a bushfire starting. It helps keep the animals' homes safe!  So, AI is like having super smart, tireless helpers always watching and listening to protect our animals!'
[2025-06-17 14:29:24,933] WARNING - __main__ - main.py:7126 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🔍   - Is actual student response: True
[2025-06-17 14:29:24,933] WARNING - __main__ - main.py:7127 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🔍   - Current probing level: 5
[2025-06-17 14:29:24,933] WARNING - __main__ - main.py:7128 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🔍   - Current question index: 0
[2025-06-17 14:29:24,933] WARNING - __main__ - main.py:7129 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🔍   - Student answers count: 3
[2025-06-17 14:29:24,934] INFO - __main__ - main.py:7132 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🔧 NATURAL PROGRESSION DEBUG:
[2025-06-17 14:29:24,934] INFO - __main__ - main.py:7133 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🔧   - lesson_phase_from_context: 'diagnostic_probing_L5_ask_q1'
[2025-06-17 14:29:24,934] INFO - __main__ - main.py:7134 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🔧   - python_calculated_new_phase_for_block: 'diagnostic_probing_L5_ask_q1'
[2025-06-17 14:29:24,935] INFO - __main__ - main.py:7135 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🔧   - user_query: 'Hi! This sounds like a superhero job for computers!  Robot Eyes (AI Cameras): AI could use special cameras that are always watching the park. If a poacher (someone who tries to hurt animals illegally) comes into the park, the AI could instantly see them and tell the park rangers! It could also count animals, like elephants or lions, to see if their numbers are going up or down.   Listening Ears (AI Microphones): AI could listen for sounds like gunshots or chainsaws (cutting down trees). If it hears something bad, it can tell the rangers right away so they can go investigate.  Smart Trackers on Animals: They could put tiny trackers on some animals, and AI could watch where they go. If an animal goes somewhere dangerous, or if it stops moving for too long, the AI could alert the rangers that something might be wrong.  Watching the Forest: AI could look at pictures from satellites (like big cameras in space) to see if trees are being cut down where they shouldn't be, or if there's a bushfire starting. It helps keep the animals' homes safe!  So, AI is like having super smart, tireless helpers always watching and listening to protect our animals!'
[2025-06-17 14:29:24,935] INFO - __main__ - main.py:7136 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🔧   - trusting_ai_state_updates: True
[2025-06-17 14:29:24,936] WARNING - __main__ - main.py:7139 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🎯 PHASE CALCULATION RESULT:
[2025-06-17 14:29:24,936] WARNING - __main__ - main.py:7140 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🎯   - Current phase: 'diagnostic_probing_L5_ask_q1'
[2025-06-17 14:29:24,937] WARNING - __main__ - main.py:7141 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🎯   - Calculated new phase: 'diagnostic_probing_L5_ask_q1'
[2025-06-17 14:29:24,937] WARNING - __main__ - main.py:7142 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🎯   - Phase changed: False
[2025-06-17 14:29:24,938] WARNING - __main__ - main.py:7145 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🎯 DIAGNOSTIC SPECIFIC:
[2025-06-17 14:29:24,938] WARNING - __main__ - main.py:7146 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🎯   - In diagnostic phase: True
[2025-06-17 14:29:24,940] WARNING - __main__ - main.py:7147 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🎯   - Student answers stored: {'q1': 'Start diagnostic assessment', 'q2': "Yes I'm ready", 'q3': "Hi! This sounds like a superhero job for computers!  Robot Eyes (AI Cameras): AI could use special cameras that are always watching the park. If a poacher (someone who tries to hurt animals illegally) comes into the park, the AI could instantly see them and tell the park rangers! It could also count animals, like elephants or lions, to see if their numbers are going up or down.   Listening Ears (AI Microphones): AI could listen for sounds like gunshots or chainsaws (cutting down trees). If it hears something bad, it can tell the rangers right away so they can go investigate.  Smart Trackers on Animals: They could put tiny trackers on some animals, and AI could watch where they go. If an animal goes somewhere dangerous, or if it stops moving for too long, the AI could alert the rangers that something might be wrong.  Watching the Forest: AI could look at pictures from satellites (like big cameras in space) to see if trees are being cut down where they shouldn't be, or if there's a bushfire starting. It helps keep the animals' homes safe!  So, AI is like having super smart, tireless helpers always watching and listening to protect our animals!"}
[2025-06-17 14:29:24,940] WARNING - __main__ - main.py:7148 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🎯   - Expected progression: Should move to next question/phase
[2025-06-17 14:29:24,942] INFO - __main__ - main.py:1829 - ENHANCED lesson content extraction: subject='Artificial Intelligence', topic='How AI helps the planet', key_concepts='What, Energy, Saving, Wildlife, Conservation', examples=0
[2025-06-17 14:29:24,942] INFO - __main__ - main.py:7209 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77][enhance_lesson_content] Python-calculated TARGET values for AI's output state block: new_phase='diagnostic_probing_L5_ask_q1', q_index='0', total_q_asked='2'
[2025-06-17 14:29:24,943] INFO - __main__ - main.py:7215 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77][diagnostic_logic] SIMPLIFIED: Trusting AI to handle diagnostic progression naturally
[2025-06-17 14:29:24,944] INFO - __main__ - main.py:7320 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] NATURAL COMPLETION: Trusting AI to handle diagnostic completion naturally
[2025-06-17 14:29:24,947] INFO - __main__ - main.py:7331 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] SIMPLIFIED: Trusting AI to determine appropriate phase transitions naturally
[2025-06-17 14:29:24,947] INFO - __main__ - main.py:7337 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] ✅ Template placeholder successfully substituted
[2025-06-17 14:29:24,947] INFO - __main__ - main.py:7341 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] ✅ Template contains calculated phase: diagnostic_probing_L5_ask_q1
[2025-06-17 14:29:24,949] INFO - __main__ - main.py:7385 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] Prompt truncated from 13871 to 10667 chars for performance
[2025-06-17 14:29:24,950] INFO - __main__ - main.py:7388 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] CONTENT QUALITY DEBUG:
[2025-06-17 14:29:24,950] INFO - __main__ - main.py:7389 -   - Final prompt length: 10667 characters
[2025-06-17 14:29:24,950] INFO - __main__ - main.py:7390 -   - Subject: Artificial Intelligence
[2025-06-17 14:29:24,951] INFO - __main__ - main.py:7391 -   - Topic: How AI helps the planet
[2025-06-17 14:29:24,951] INFO - __main__ - main.py:7392 -   - Key concepts: What, Energy, Saving, Wildlife, Conservation
[2025-06-17 14:29:24,952] INFO - __main__ - main.py:7393 -   - Grade: Primary 5
[2025-06-17 14:29:24,952] INFO - __main__ - main.py:7394 -   - Phase: diagnostic_probing_L5_ask_q1
[2025-06-17 14:29:24,953] INFO - __main__ - main.py:7395 -   - Student: Andrea
[2025-06-17 14:29:24,954] DEBUG - __main__ - main.py:7396 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] Prompt preview: 
MANDATORY: End with: // AI_STATE_UPDATE_BLOCK_START {"new_phase": "diagnostic_probing_L5_ask_q1", "interaction_count": 1} // AI_STATE_UPDATE_BLOCK_END

CRITICAL PHASE PROGRESSION: 
• The system has calculated the next phase as: diagnostic_probing_L5_ask_q1
• You MUST use this exact phase name in yo...
[2025-06-17 14:29:24,955] INFO - __main__ - main.py:7424 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] Gemini API call attempt 1/3
[2025-06-17 14:29:26,166] INFO - __main__ - main.py:7442 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] ✅ Gemini API call succeeded on attempt 1
[2025-06-17 14:29:26,167] INFO - __main__ - main.py:7461 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] Gemini API call completed in 1.21s
[2025-06-17 14:29:26,167] DEBUG - __main__ - main.py:2196 - extract_gemini_text: Successfully extracted 353 characters
[2025-06-17 14:29:26,168] INFO - __main__ - main.py:7684 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77][enhance_lesson_content] AI response: That's a fantastic idea, Andrea!  AI cameras could definitely help protect wildlife.  Now let's explore how AI saves energy. Can you think of an example of how AI might help reduce energy consumption ...
[2025-06-17 14:29:26,169] INFO - __main__ - main.py:7712 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🔍 STATE DEBUG: Looking for state update block in AI response
[2025-06-17 14:29:26,170] INFO - __main__ - main.py:7713 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🔍 STATE DEBUG: AI response length: 353 chars
[2025-06-17 14:29:26,173] INFO - __main__ - main.py:7725 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🔍 STATE DEBUG: Found state block: {"new_phase": "diagnostic_probing_L5_ask_q1", "interaction_count": 1}
[2025-06-17 14:29:26,174] INFO - __main__ - main.py:7727 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] ✅ Found AI state update: {'new_phase': 'diagnostic_probing_L5_ask_q1', 'interaction_count': 1}
[2025-06-17 14:29:26,175] WARNING - __main__ - main.py:7786 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🔧 FORCING DIAGNOSTIC PROGRESSION: diagnostic_probing_L5_ask_q1 → diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:29:26,176] INFO - __main__ - main.py:7791 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] SIMPLIFIED: Trusting AI state updates without aggressive diagnostic forcing
[2025-06-17 14:29:26,177] INFO - __main__ - main.py:7797 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] SIMPLIFIED: Using AI response without hardcoded fallbacks
[2025-06-17 14:29:26,177] INFO - __main__ - main.py:7817 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] Auto-corrected: added missing current_probing_level_number
[2025-06-17 14:29:26,177] INFO - __main__ - main.py:7847 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] ✅ AI state validation completed: {'new_phase': 'diagnostic_probing_L5_eval_q1_ask_q2', 'interaction_count': 1, 'current_question_index': 1, 'current_probing_level_number': 5}
[2025-06-17 14:29:26,178] INFO - __main__ - main.py:7859 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] ✅ AI PROVIDED VALID STATE: Using AI's state update proposal
[2025-06-17 14:29:26,178] INFO - __main__ - main.py:7902 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] Placeholder apply_phase_transition_fixes detected or no fix made. Prioritizing AI's originally proposed state.
[2025-06-17 14:29:26,178] INFO - __main__ - main.py:7911 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] State updates after apply_fixes and potential AI prioritization: {'new_phase': 'diagnostic_probing_L5_eval_q1_ask_q2', 'interaction_count': 1, 'current_question_index': 1, 'current_probing_level_number': 5}
[2025-06-17 14:29:26,179] INFO - __main__ - main.py:7969 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 📝 DIAGNOSTIC PROGRESSION: Continue diagnostic - eval_phase=False, answers=3/5
[2025-06-17 14:29:26,179] INFO - __main__ - main.py:7972 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 📝 DIAGNOSTIC PROGRESSION: Need 2 more answers
[2025-06-17 14:29:26,179] INFO - __main__ - main.py:7974 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 📝 DIAGNOSTIC PROGRESSION: Not in eval_q5_decide_level phase
[2025-06-17 14:29:26,180] INFO - __main__ - main.py:7978 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] NATURAL COMPLETION: Trusting AI to handle diagnostic completion naturally
[2025-06-17 14:29:26,180] INFO - __main__ - main.py:8010 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] DIAGNOSTIC PROGRESSION: AI provided phase transition: diagnostic_probing_L5_ask_q1 → diagnostic_probing_L5_eval_q1_ask_q2 - trusting AI
[2025-06-17 14:29:26,181] INFO - __main__ - main.py:8098 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] [OK] State update block guaranteed by system enforcement
[2025-06-17 14:29:26,181] INFO - __main__ - main.py:8104 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] Phase transition: diagnostic_probing_L5_ask_q1 -> diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:29:26,182] INFO - __main__ - main.py:3972 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] VALIDATING DIAGNOSTIC PHASE SEQUENCE: diagnostic_probing_L5_ask_q1 → diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:29:26,182] INFO - __main__ - main.py:4074 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] ✅ VALID DIAGNOSTIC TRANSITION: diagnostic_probing_L5_ask_q1 → diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:29:26,183] INFO - __main__ - main.py:8180 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 📋 FINAL STATE UPDATE: {'new_phase': 'diagnostic_probing_L5_eval_q1_ask_q2', 'interaction_count': 1, 'current_question_index': 1, 'current_probing_level_number': 5}
[2025-06-17 14:29:26,185] INFO - __main__ - main.py:8216 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] ✅ Clean response prepared for user, state updates handled internally
[2025-06-17 14:29:26,185] INFO - __main__ - main.py:8232 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] ✅ AI response already includes personalization
[2025-06-17 14:29:26,185] INFO - __main__ - main.py:8238 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] [OK] State updates processed: {'new_phase': 'diagnostic_probing_L5_eval_q1_ask_q2', 'interaction_count': 1, 'current_question_index': 1, 'current_probing_level_number': 5}
[2025-06-17 14:29:26,186] INFO - __main__ - main.py:8241 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] SIMPLIFIED: Diagnostic enforcement disabled to prevent backward transitions
[2025-06-17 14:29:26,186] WARNING - __main__ - main.py:8262 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🔄 STATE UPDATE PROCESSING:
[2025-06-17 14:29:26,187] WARNING - __main__ - main.py:8263 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🔄   - AI provided state update: True
[2025-06-17 14:29:26,188] WARNING - __main__ - main.py:8265 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🔄   - AI proposed phase: 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 14:29:26,189] WARNING - __main__ - main.py:8266 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🔄   - Current lesson phase: 'diagnostic_probing_L5_ask_q1'
[2025-06-17 14:29:26,189] WARNING - __main__ - main.py:8267 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🔄   - Python calculated phase: 'diagnostic_probing_L5_ask_q1'
[2025-06-17 14:29:26,190] INFO - __main__ - main.py:8275 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] ⚡ PERFORMANCE METRICS:
[2025-06-17 14:29:26,191] INFO - __main__ - main.py:8276 -   - Total execution time: 1.268s
[2025-06-17 14:29:26,193] WARNING - __main__ - main.py:5643 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🤖 AI RESPONSE RECEIVED:
[2025-06-17 14:29:26,194] WARNING - __main__ - main.py:5644 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🤖   - Content length: 220 chars
[2025-06-17 14:29:26,194] WARNING - __main__ - main.py:5645 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🤖   - State updates: {'new_phase': 'diagnostic_probing_L5_eval_q1_ask_q2', 'interaction_count': 1, 'current_question_index': 1, 'current_probing_level_number': 5}
[2025-06-17 14:29:26,195] WARNING - __main__ - main.py:5646 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🤖   - Raw state block: None...
[2025-06-17 14:29:26,195] INFO - __main__ - main.py:5662 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
[2025-06-17 14:29:26,196] INFO - __main__ - main.py:5663 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] CURRENT PHASE DETERMINATION: AI=diagnostic_probing_L5_eval_q1_ask_q2, Session=diagnostic_probing_L5_ask_q1, Final=diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:29:26,477] INFO - __main__ - main.py:5772 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] AI processing completed in 1.56s
[2025-06-17 14:29:26,477] WARNING - __main__ - main.py:5783 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🔍 STATE UPDATE VALIDATION: current_phase='diagnostic_probing_L5_ask_q1', new_phase='diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 14:29:26,478] INFO - __main__ - main.py:3897 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] AI state update validation passed: diagnostic_probing_L5_ask_q1 → diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:29:26,479] WARNING - __main__ - main.py:5792 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] ✅ STATE UPDATE VALIDATION PASSED
[2025-06-17 14:29:26,479] WARNING - __main__ - main.py:5797 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🔄 PHASE TRANSITION: diagnostic_probing_L5_ask_q1 → diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:29:26,481] WARNING - __main__ - main.py:5806 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🔍 COMPLETE PHASE FLOW DEBUG:
[2025-06-17 14:29:26,481] WARNING - __main__ - main.py:5807 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🔍   1. Input phase: 'diagnostic_probing_L5_ask_q1'
[2025-06-17 14:29:26,482] WARNING - __main__ - main.py:5808 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🔍   2. Python calculated next phase: 'NOT_FOUND'
[2025-06-17 14:29:26,482] WARNING - __main__ - main.py:5809 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🔍   3. AI state updates: {'new_phase': 'diagnostic_probing_L5_eval_q1_ask_q2', 'interaction_count': 1, 'current_question_index': 1, 'current_probing_level_number': 5}
[2025-06-17 14:29:26,483] WARNING - __main__ - main.py:5810 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🔍   4. Final phase to save: 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 14:29:26,483] WARNING - __main__ - main.py:5813 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 💾 FINAL STATE APPLICATION:
[2025-06-17 14:29:26,484] WARNING - __main__ - main.py:5814 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 💾   - Current phase input: 'diagnostic_probing_L5_ask_q1'
[2025-06-17 14:29:26,485] WARNING - __main__ - main.py:5815 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 💾   - State updates from AI: {'new_phase': 'diagnostic_probing_L5_eval_q1_ask_q2', 'interaction_count': 1, 'current_question_index': 1, 'current_probing_level_number': 5}
[2025-06-17 14:29:26,485] WARNING - __main__ - main.py:5816 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 💾   - Final phase to save: 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 14:29:26,485] WARNING - __main__ - main.py:5817 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 💾   - Phase change: True
[2025-06-17 14:29:26,486] INFO - __main__ - main.py:3929 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] DIAGNOSTIC_FLOW_METRICS:
[2025-06-17 14:29:26,486] INFO - __main__ - main.py:3930 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77]   Phase transition: diagnostic_probing_L5_ask_q1 -> diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:29:26,486] INFO - __main__ - main.py:3931 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77]   Current level: 5
[2025-06-17 14:29:26,487] INFO - __main__ - main.py:3932 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77]   Question index: 0
[2025-06-17 14:29:26,487] INFO - __main__ - main.py:3933 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77]   First encounter: True
[2025-06-17 14:29:26,487] INFO - __main__ - main.py:3938 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77]   Answers collected: 3
[2025-06-17 14:29:26,488] INFO - __main__ - main.py:3939 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77]   Levels failed: 0
[2025-06-17 14:29:26,488] INFO - __main__ - main.py:3897 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] AI state update validation passed: diagnostic_probing_L5_ask_q1 → diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:29:26,488] INFO - __main__ - main.py:3943 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77]   State update valid: True
[2025-06-17 14:29:26,489] INFO - __main__ - main.py:3950 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77]   Diagnostic complete: False
[2025-06-17 14:29:26,489] WARNING - __main__ - main.py:5829 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 1, current_q_index_for_prompt = NOT_FOUND
[2025-06-17 14:29:27,004] WARNING - __main__ - main.py:5856 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] ✅ SESSION STATE SAVED TO FIRESTORE:
[2025-06-17 14:29:27,005] WARNING - __main__ - main.py:5857 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] ✅   - Phase: diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:29:27,005] WARNING - __main__ - main.py:5858 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] ✅   - Probing Level: 5
[2025-06-17 14:29:27,005] WARNING - __main__ - main.py:5859 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] ✅   - Question Index: 1
[2025-06-17 14:29:27,006] WARNING - __main__ - main.py:5860 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] ✅   - Diagnostic Complete: False
[2025-06-17 14:29:27,796] INFO - __main__ - main.py:5919 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] ✅ Updated existing session document: session_b71023f5-62ee-482e-be3a-783fab2fb64c
[2025-06-17 14:29:27,797] WARNING - __main__ - main.py:5920 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] ✅ SESSION UPDATE COMPLETE:
[2025-06-17 14:29:27,797] WARNING - __main__ - main.py:5921 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] ✅   - Session ID: session_b71023f5-62ee-482e-be3a-783fab2fb64c
[2025-06-17 14:29:27,798] WARNING - __main__ - main.py:5922 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] ✅   - Phase transition: diagnostic_probing_L5_ask_q1 → diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:29:27,798] WARNING - __main__ - main.py:5923 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] ✅   - Interaction logged successfully
[2025-06-17 14:29:27,798] INFO - __main__ - main.py:9173 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
[2025-06-17 14:29:27,799] DEBUG - __main__ - main.py:2753 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] FINAL_ASSESSMENT_BLOCK not found in AI response.
[2025-06-17 14:29:27,800] DEBUG - __main__ - main.py:5969 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] No final assessment data found in AI response
[2025-06-17 14:29:27,801] INFO - __main__ - main.py:6034 - [4bb4ef4f-fc69-4efc-8de3-17f9a58c2d77] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
[2025-06-17 14:29:27,813] WARNING - __main__ - main.py:625 - High response time detected: 5.94s for enhance_content_api
[2025-06-17 14:29:27,825] INFO - werkzeug - _internal.py:97 - 127.0.0.1 - - [17/Jun/2025 14:29:27] "POST /api/enhance-content HTTP/1.1" 200 -
[2025-06-17 14:30:40,828] INFO - __main__ - main.py:4831 - REQUEST: POST http://localhost:5000/api/enhance-content -> endpoint: enhance_content_api
[2025-06-17 14:30:40,830] INFO - __main__ - main.py:4874 - Incoming request: {"request_id": "f39b5939-ce54-467e-ba4c-332a63a09fff", "timestamp": "2025-06-17T13:30:40.828900+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.9.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-AI-001", "content_to_enhance": "That's a great question, especially here in Lagos where we use so much electricity!  Imagine all the streetlights in Lagos. Instead of them being on super bright all night long, even when no one is around, AI could make them smarter!  Smart Streetlights!  AI could be connected to the streetlights and use special sensors.  If there are no cars or people on a street late at night, the AI could tell the lights to dim down or even turn off a few, and then turn them bright again when a car comes. The AI could also learn when the sun rises and sets exactly each day and only turn the lights on when it's truly dark, saving energy compared to a fixed timer. This would save a lot of electricity because the lights are only super bright when they are really needed. It's like the AI is a smart energy manager for the whole city!", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "Primary 5", "level": "P5", "subject": "Artificial Intelligence", "session_id": "session_b71023f5-62ee-482e-be3a-783fab2fb64c", "chat_history": [{"role": "assistant", "content": "Hi Andrea! I'm your AI tutor, and I'm excited to help you learn about how Artificial Intelligence helps our planet. We'll start with a quick diagnostic assessment to see what you already know. This helps me tailor the lessons perfectly for you. Are you ready to begin with some questions about what AI is, how it saves energy, protects wildlife, and helps with conservation?", "timestamp": "2025-06-17T13:26:37.197Z"}, {"role": "user", "content": "Yes I'm ready", "timestamp": "2025-06-17T13:27:05.374Z"}, {"role": "assistant", "content": "Hi Andrea! Great! Let's start with our first question about how AI helps the planet: Imagine a large national park in Nigeria filled with endangered animals. How could AI be used to help protect these animals and conserve their habitats?", "timestamp": "2025-06-17T13:27:10.398Z"}, {"role": "user", "content": "Hi! This sounds like a superhero job for computers!  Robot Eyes (AI Cameras): AI could use special cameras that are always watching the park. If a poacher (someone who tries to hurt animals illegally) comes into the park, the AI could instantly see them and tell the park rangers! It could also count animals, like elephants or lions, to see if their numbers are going up or down.   Listening Ears (AI Microphones): AI could listen for sounds like gunshots or chainsaws (cutting down trees). If it hears something bad, it can tell the rangers right away so they can go investigate.  Smart Trackers on Animals: They could put tiny trackers on some animals, and AI could watch where they go. If an animal goes somewhere dangerous, or if it stops moving for too long, the AI could alert the rangers that something might be wrong.  Watching the Forest: AI could look at pictures from satellites (like big cameras in space) to see if trees are being cut down where they shouldn't be, or if there's a bushfire starting. It helps keep the animals' homes safe!  So, AI is like having super smart, tireless helpers always watching and listening to protect our animals!", "timestamp": "2025-06-17T13:29:21.480Z"}, {"role": "assistant", "content": "That's a fantastic idea, Andrea! AI cameras could definitely help protect wildlife. Now let's explore how AI saves energy. Can you think of an example of how AI might help reduce energy consumption in a city, like Lagos?", "timestamp": "2025-06-17T13:29:27.852Z"}]}}
[2025-06-17 14:30:40,833] INFO - temporary_auth_bypass - temporary_auth_bypass.py:29 - [f39b5939-ce54-467e-ba4c-332a63a09fff] Using temporary auth bypass with mock token
[2025-06-17 14:30:40,834] DEBUG - asyncio - proactor_events.py:631 - Using proactor: IocpProactor
[2025-06-17 14:30:40,839] WARNING - __main__ - main.py:5067 - 🔥 ENHANCE-CONTENT ENDPOINT CALLED!
[2025-06-17 14:30:40,843] INFO - __main__ - main.py:5178 - [enhance_content_api] Received request with decoded token.
[2025-06-17 14:30:40,844] INFO - __main__ - main.py:5180 - [enhance_content_api][f39b5939-ce54-467e-ba4c-332a63a09fff] Processing enhance content request
[2025-06-17 14:30:40,845] INFO - __main__ - main.py:5226 - [f39b5939-ce54-467e-ba4c-332a63a09fff][enhance_content_api] Parsed JSON payload (first 250 bytes): {'student_id': 'andrea_ugono_33305', 'lesson_ref': 'P5-AI-001', 'content_to_enhance': "That's a great question, especially here in Lagos where we use so much electricity!  Imagine all the streetlights in Lagos. Instead of them being on super bright a
[2025-06-17 14:30:42,425] INFO - __main__ - main.py:4424 - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
[2025-06-17 14:30:42,426] DEBUG - __main__ - main.py:596 - Cache hit for fetch_lesson_data
[2025-06-17 14:30:42,426] INFO - __main__ - main.py:5272 - [f39b5939-ce54-467e-ba4c-332a63a09fff] ✅ All required fields present after lesson content parsing and mapping
[2025-06-17 14:30:42,427] INFO - __main__ - main.py:5311 - [f39b5939-ce54-467e-ba4c-332a63a09fff] Attempting to infer module. GS Subject Slug: 'artificial_intelligence'.
[2025-06-17 14:30:42,428] INFO - __main__ - main.py:2273 - [f39b5939-ce54-467e-ba4c-332a63a09fff] AI Inference: Inferring module for subject 'artificial_intelligence', lesson 'AI: Saving Our Planet!'.
[2025-06-17 14:30:42,716] INFO - __main__ - main.py:2332 - [f39b5939-ce54-467e-ba4c-332a63a09fff] AI Inference: Loaded metadata for module 'ai_awareness_and_foundations' ('AI Awareness & Foundations')
[2025-06-17 14:30:42,717] INFO - __main__ - main.py:2332 - [f39b5939-ce54-467e-ba4c-332a63a09fff] AI Inference: Loaded metadata for module 'ai_tools_and_applications' ('AI Tools & Applications')
[2025-06-17 14:30:42,718] INFO - __main__ - main.py:2332 - [f39b5939-ce54-467e-ba4c-332a63a09fff] AI Inference: Loaded metadata for module 'computational_thinking_and_coding' ('Computational Thinking & Coding')
[2025-06-17 14:30:42,719] INFO - __main__ - main.py:2332 - [f39b5939-ce54-467e-ba4c-332a63a09fff] AI Inference: Loaded metadata for module 'data_literacy_and_machine_learning' ('Data Literacy & Machine Learning')
[2025-06-17 14:30:42,720] INFO - __main__ - main.py:2332 - [f39b5939-ce54-467e-ba4c-332a63a09fff] AI Inference: Loaded metadata for module 'ethics_and_societal_impact' ('Ethics & Societal Impact')
[2025-06-17 14:30:42,722] INFO - __main__ - main.py:2401 - [f39b5939-ce54-467e-ba4c-332a63a09fff] AI Inference: Starting module inference for subject 'artificial_intelligence' with 5 module options
[2025-06-17 14:30:42,725] DEBUG - __main__ - main.py:2415 - [f39b5939-ce54-467e-ba4c-332a63a09fff] AI Inference: Sample modules available (first 3):
- ai_awareness_and_foundations: AI Awareness & Foundations
- ai_tools_and_applications: AI Tools & Applications
- computational_thinking_and_coding: Computational Thinking & Coding
[2025-06-17 14:30:42,726] DEBUG - __main__ - main.py:2418 - [f39b5939-ce54-467e-ba4c-332a63a09fff] AI Inference Prompt (first 500 chars): 
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: AI: Saving Our Planet!. Topic: How AI helps the planet. Learning Objectives: Explore AI’s role in saving energy.; Understand AI’s impact on wildlife conservation.. Key Concepts: Explore; role; saving; energy; Understand; impact; wildlife; conservation; What; Energy Saving. Introducti...
[2025-06-17 14:30:42,728] DEBUG - __main__ - main.py:2419 - [f39b5939-ce54-467e-ba4c-332a63a09fff] AI Inference Lesson Summary (first 300 chars): Lesson Title: AI: Saving Our Planet!. Topic: How AI helps the planet. Learning Objectives: Explore AI’s role in saving energy.; Understand AI’s impact on wildlife conservation.. Key Concepts: Explore; role; saving; energy; Understand; impact; wildlife; conservation; What; Energy Saving. Introduction...
[2025-06-17 14:30:42,728] DEBUG - __main__ - main.py:2420 - [f39b5939-ce54-467e-ba4c-332a63a09fff] AI Inference Module Options (first 200 chars): 1. Slug: "ai_awareness_and_foundations", Name: "AI Awareness & Foundations", Description: "Conceptual journey from recognising smart helpers to understanding core AI paradigms (symbolic, statistical, ...
[2025-06-17 14:30:42,729] INFO - __main__ - main.py:2424 - [f39b5939-ce54-467e-ba4c-332a63a09fff] AI Inference: Calling Gemini API for module inference...
[2025-06-17 14:30:43,324] DEBUG - __main__ - main.py:2196 - extract_gemini_text: Successfully extracted 25 characters
[2025-06-17 14:30:43,325] INFO - __main__ - main.py:2434 - [f39b5939-ce54-467e-ba4c-332a63a09fff] AI Inference: Gemini API call completed in 0.59s. Raw response: 'ai_tools_and_applications'
[2025-06-17 14:30:43,325] DEBUG - __main__ - main.py:2456 - [f39b5939-ce54-467e-ba4c-332a63a09fff] AI Inference: Cleaned slug: 'ai_tools_and_applications'
[2025-06-17 14:30:43,326] INFO - __main__ - main.py:2461 - [f39b5939-ce54-467e-ba4c-332a63a09fff] AI Inference: Successfully matched module by slug. Chosen: 'ai_tools_and_applications' (AI Tools & Applications)
[2025-06-17 14:30:43,327] INFO - __main__ - main.py:5344 - [f39b5939-ce54-467e-ba4c-332a63a09fff] Successfully inferred module ID via AI: ai_tools_and_applications
[2025-06-17 14:30:43,328] INFO - __main__ - main.py:5370 - [f39b5939-ce54-467e-ba4c-332a63a09fff] Effective module name for prompt context: 'AI Tools & Applications' (Module ID: ai_tools_and_applications)
[2025-06-17 14:30:43,608] INFO - __main__ - main.py:1995 - No prior student performance document found for Topic: artificial_intelligence_Primary 5_artificial_intelligence_ai_tools_and_applications
[2025-06-17 14:30:44,094] WARNING - __main__ - main.py:5391 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🔍 SESSION STATE DEBUG:
[2025-06-17 14:30:44,095] WARNING - __main__ - main.py:5392 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🔍   - Session exists: True
[2025-06-17 14:30:44,098] WARNING - __main__ - main.py:5393 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🔍   - Current phase: diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:30:44,100] WARNING - __main__ - main.py:5394 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🔍   - State data keys: ['created_at', 'session_id', 'key_concepts_str_for_ai', 'blooms_levels_str_for_ai', 'last_diagnostic_question_text_asked', 'last_updated', 'current_probing_level_number', 'diagnostic_completed_this_session', 'assigned_level_for_teaching', 'lesson_context_snapshot', 'student_name', 'levels_probed_and_failed', 'is_first_encounter_for_module', 'current_phase', 'current_lesson_phase', 'last_update_request_id', 'current_session_working_level', 'current_question_index', 'student_id', 'last_modified', 'latest_assessed_level_for_module', 'student_answers_for_probing_level']
[2025-06-17 14:30:44,103] INFO - __main__ - main.py:5458 - [f39b5939-ce54-467e-ba4c-332a63a09fff] �🔍 FIRST ENCOUNTER LOGIC:
[2025-06-17 14:30:44,104] INFO - __main__ - main.py:5459 - [f39b5939-ce54-467e-ba4c-332a63a09fff]   assigned_level_for_teaching (session): None
[2025-06-17 14:30:44,105] INFO - __main__ - main.py:5460 - [f39b5939-ce54-467e-ba4c-332a63a09fff]   latest_assessed_level (profile): None
[2025-06-17 14:30:44,107] INFO - __main__ - main.py:5461 - [f39b5939-ce54-467e-ba4c-332a63a09fff]   teaching_level_for_returning_student: None
[2025-06-17 14:30:44,108] INFO - __main__ - main.py:5462 - [f39b5939-ce54-467e-ba4c-332a63a09fff]   has_completed_diagnostic_before: False
[2025-06-17 14:30:44,109] INFO - __main__ - main.py:5463 - [f39b5939-ce54-467e-ba4c-332a63a09fff]   is_first_encounter_for_module: True
[2025-06-17 14:30:44,109] WARNING - __main__ - main.py:5468 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
[2025-06-17 14:30:44,110] INFO - __main__ - main.py:5474 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🔍 PHASE INVESTIGATION:
[2025-06-17 14:30:44,111] INFO - __main__ - main.py:5475 - [f39b5939-ce54-467e-ba4c-332a63a09fff]   Retrieved from Firestore: 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 14:30:44,112] INFO - __main__ - main.py:5476 - [f39b5939-ce54-467e-ba4c-332a63a09fff]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'diagnostic_start_probe'
[2025-06-17 14:30:44,112] INFO - __main__ - main.py:5477 - [f39b5939-ce54-467e-ba4c-332a63a09fff]   Is first encounter: True
[2025-06-17 14:30:44,113] INFO - __main__ - main.py:5478 - [f39b5939-ce54-467e-ba4c-332a63a09fff]   Diagnostic completed: False
[2025-06-17 14:30:44,114] INFO - __main__ - main.py:5484 - [f39b5939-ce54-467e-ba4c-332a63a09fff] ✅ Using stored phase from Firestore: 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 14:30:44,114] INFO - __main__ - main.py:5498 - [f39b5939-ce54-467e-ba4c-332a63a09fff] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
[2025-06-17 14:30:44,115] INFO - __main__ - main.py:5500 - [f39b5939-ce54-467e-ba4c-332a63a09fff] Final phase for AI logic: diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:30:44,116] INFO - __main__ - main.py:5514 - [f39b5939-ce54-467e-ba4c-332a63a09fff] NEW SESSION: Using phase-based question index 0 from phase diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:30:44,116] INFO - __main__ - main.py:3692 - [f39b5939-ce54-467e-ba4c-332a63a09fff] Diagnostic context validation passed
[2025-06-17 14:30:44,117] INFO - __main__ - main.py:3725 - DETERMINE_PHASE: Resuming in-progress diagnostic at phase: 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 14:30:44,117] WARNING - __main__ - main.py:5586 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'diagnostic_probing_L5_eval_q1_ask_q2' for first encounter
[2025-06-17 14:30:44,118] INFO - __main__ - main.py:3802 - [f39b5939-ce54-467e-ba4c-332a63a09fff] Enhanced diagnostic context with 35 fields
[2025-06-17 14:30:44,118] INFO - __main__ - main.py:5608 - [f39b5939-ce54-467e-ba4c-332a63a09fff] Robust diagnostic context prepared successfully. Phase: diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:30:44,119] DEBUG - __main__ - main.py:5609 - [f39b5939-ce54-467e-ba4c-332a63a09fff] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'diagnostic_context', 'performance_tracking', 'level_specific_guidance', 'current_phase_for_ai']
[2025-06-17 14:30:44,119] WARNING - __main__ - main.py:5617 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🤖 AI PROMPT GENERATION:
[2025-06-17 14:30:44,120] WARNING - __main__ - main.py:5618 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🤖   - Current phase: diagnostic_start_probe
[2025-06-17 14:30:44,121] WARNING - __main__ - main.py:5619 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🤖   - Student query: That's a great question, especially here in Lagos where we use so much electricity!  Imagine all the...
[2025-06-17 14:30:44,123] WARNING - __main__ - main.py:5620 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🤖   - Context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'diagnostic_context', 'performance_tracking', 'level_specific_guidance', 'current_phase_for_ai']
[2025-06-17 14:30:44,125] INFO - __main__ - main.py:6493 - [f39b5939-ce54-467e-ba4c-332a63a09fff] enhance_lesson_content invoked. Query: 'That's a great question, especially here in Lagos ...'
[2025-06-17 14:30:44,125] INFO - __main__ - main.py:6536 - [f39b5939-ce54-467e-ba4c-332a63a09fff] CONTEXT DEBUG: lesson_phase from context = 'diagnostic_probing_L5_eval_q1_ask_q2', processed = 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 14:30:44,125] INFO - __main__ - main.py:6547 - [f39b5939-ce54-467e-ba4c-332a63a09fff][enhance_lesson_content] Received from context - phase: 'diagnostic_probing_L5_eval_q1_ask_q2', module_id: 'ai_tools_and_applications', gs_subject_slug: 'artificial_intelligence'
[2025-06-17 14:30:44,126] INFO - __main__ - main.py:6577 - [f39b5939-ce54-467e-ba4c-332a63a09fff] DIAGNOSTIC ANSWER: Storing Q1 answer from phase diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:30:44,126] INFO - __main__ - main.py:6586 - [f39b5939-ce54-467e-ba4c-332a63a09fff] DIAGNOSTIC ANSWER STORED: q1 = 'That's a great question, especially here in Lagos ...'
[2025-06-17 14:30:44,127] INFO - __main__ - main.py:6587 - [f39b5939-ce54-467e-ba4c-332a63a09fff] Total diagnostic answers now: 2/5
[2025-06-17 14:30:44,127] INFO - __main__ - main.py:6628 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 📝 DIAGNOSTIC PROGRESSION: Continue with 2/5 answers, phase: diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:30:44,128] INFO - __main__ - main.py:6766 - [f39b5939-ce54-467e-ba4c-332a63a09fff][enhance_lesson_content] Proceeding to main AI prompt generation. Phase: diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:30:44,128] INFO - __main__ - main.py:6773 - [f39b5939-ce54-467e-ba4c-332a63a09fff][DIAGNOSTIC_FLOW] Phase: diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:30:44,128] INFO - __main__ - main.py:6774 - [f39b5939-ce54-467e-ba4c-332a63a09fff][DIAGNOSTIC_FLOW] Questions asked: 2/5
[2025-06-17 14:30:44,129] INFO - __main__ - main.py:6775 - [f39b5939-ce54-467e-ba4c-332a63a09fff][DIAGNOSTIC_FLOW] Question index: 0
[2025-06-17 14:30:44,129] INFO - __main__ - main.py:6776 - [f39b5939-ce54-467e-ba4c-332a63a09fff][DIAGNOSTIC_FLOW] Student answers count: 2
[2025-06-17 14:30:44,130] INFO - __main__ - main.py:6781 - [f39b5939-ce54-467e-ba4c-332a63a09fff][DIAGNOSTIC_FLOW] Answer q1: That's a great question, especially here in Lagos ...
[2025-06-17 14:30:44,130] INFO - __main__ - main.py:6781 - [f39b5939-ce54-467e-ba4c-332a63a09fff][DIAGNOSTIC_FLOW] Answer q2: Yes I'm ready...
[2025-06-17 14:30:44,130] INFO - __main__ - main.py:6805 - [f39b5939-ce54-467e-ba4c-332a63a09fff][DIAGNOSTIC_FLOW] SIMPLIFIED LOGIC: Phase=diagnostic_probing_L5_eval_q1_ask_q2, Answers=2/5
[2025-06-17 14:30:44,131] INFO - __main__ - main.py:6808 - [f39b5939-ce54-467e-ba4c-332a63a09fff][DIAGNOSTIC_FLOW] SIMPLIFIED: Trusting AI to handle diagnostic completion naturally
[2025-06-17 14:30:44,131] INFO - __main__ - main.py:6839 - [f39b5939-ce54-467e-ba4c-332a63a09fff] INTERACTION COUNT: Updated to 1 and saved to context
[2025-06-17 14:30:44,131] INFO - __main__ - main.py:6853 - [f39b5939-ce54-467e-ba4c-332a63a09fff] DIAGNOSTIC PROGRESSION: Calculating next phase for diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:30:44,132] INFO - __main__ - main.py:6861 - [f39b5939-ce54-467e-ba4c-332a63a09fff] DIAGNOSTIC PROGRESSION: Running diagnostic logic for phase 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 14:30:44,132] INFO - __main__ - main.py:7120 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🎯 ENHANCED PHASE CALCULATION: diagnostic_probing_L5_eval_q1_ask_q2 → diagnostic_probing_L5_eval_q1_ask_q2 (interaction 1)
[2025-06-17 14:30:44,132] WARNING - __main__ - main.py:7123 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🔍 DIAGNOSTIC FLOW START:
[2025-06-17 14:30:44,133] WARNING - __main__ - main.py:7124 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🔍   - Input phase from context: 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 14:30:44,133] WARNING - __main__ - main.py:7125 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🔍   - User query: 'That's a great question, especially here in Lagos where we use so much electricity!  Imagine all the streetlights in Lagos. Instead of them being on super bright all night long, even when no one is around, AI could make them smarter!  Smart Streetlights!  AI could be connected to the streetlights and use special sensors.  If there are no cars or people on a street late at night, the AI could tell the lights to dim down or even turn off a few, and then turn them bright again when a car comes. The AI could also learn when the sun rises and sets exactly each day and only turn the lights on when it's truly dark, saving energy compared to a fixed timer. This would save a lot of electricity because the lights are only super bright when they are really needed. It's like the AI is a smart energy manager for the whole city!'
[2025-06-17 14:30:44,134] WARNING - __main__ - main.py:7126 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🔍   - Is actual student response: True
[2025-06-17 14:30:44,134] WARNING - __main__ - main.py:7127 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🔍   - Current probing level: 5
[2025-06-17 14:30:44,134] WARNING - __main__ - main.py:7128 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🔍   - Current question index: 0
[2025-06-17 14:30:44,135] WARNING - __main__ - main.py:7129 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🔍   - Student answers count: 2
[2025-06-17 14:30:44,135] INFO - __main__ - main.py:7132 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🔧 NATURAL PROGRESSION DEBUG:
[2025-06-17 14:30:44,136] INFO - __main__ - main.py:7133 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🔧   - lesson_phase_from_context: 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 14:30:44,136] INFO - __main__ - main.py:7134 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🔧   - python_calculated_new_phase_for_block: 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 14:30:44,136] INFO - __main__ - main.py:7135 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🔧   - user_query: 'That's a great question, especially here in Lagos where we use so much electricity!  Imagine all the streetlights in Lagos. Instead of them being on super bright all night long, even when no one is around, AI could make them smarter!  Smart Streetlights!  AI could be connected to the streetlights and use special sensors.  If there are no cars or people on a street late at night, the AI could tell the lights to dim down or even turn off a few, and then turn them bright again when a car comes. The AI could also learn when the sun rises and sets exactly each day and only turn the lights on when it's truly dark, saving energy compared to a fixed timer. This would save a lot of electricity because the lights are only super bright when they are really needed. It's like the AI is a smart energy manager for the whole city!'
[2025-06-17 14:30:44,137] INFO - __main__ - main.py:7136 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🔧   - trusting_ai_state_updates: True
[2025-06-17 14:30:44,138] WARNING - __main__ - main.py:7139 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🎯 PHASE CALCULATION RESULT:
[2025-06-17 14:30:44,139] WARNING - __main__ - main.py:7140 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🎯   - Current phase: 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 14:30:44,140] WARNING - __main__ - main.py:7141 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🎯   - Calculated new phase: 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 14:30:44,141] WARNING - __main__ - main.py:7142 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🎯   - Phase changed: False
[2025-06-17 14:30:44,141] WARNING - __main__ - main.py:7145 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🎯 DIAGNOSTIC SPECIFIC:
[2025-06-17 14:30:44,142] WARNING - __main__ - main.py:7146 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🎯   - In diagnostic phase: True
[2025-06-17 14:30:44,142] WARNING - __main__ - main.py:7147 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🎯   - Student answers stored: {'q1': "That's a great question, especially here in Lagos where we use so much electricity!  Imagine all the streetlights in Lagos. Instead of them being on super bright all night long, even when no one is around, AI could make them smarter!  Smart Streetlights!  AI could be connected to the streetlights and use special sensors.  If there are no cars or people on a street late at night, the AI could tell the lights to dim down or even turn off a few, and then turn them bright again when a car comes. The AI could also learn when the sun rises and sets exactly each day and only turn the lights on when it's truly dark, saving energy compared to a fixed timer. This would save a lot of electricity because the lights are only super bright when they are really needed. It's like the AI is a smart energy manager for the whole city!", 'q2': "Yes I'm ready"}
[2025-06-17 14:30:44,142] WARNING - __main__ - main.py:7148 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🎯   - Expected progression: Should move to next question/phase
[2025-06-17 14:30:44,143] INFO - __main__ - main.py:1829 - ENHANCED lesson content extraction: subject='Artificial Intelligence', topic='How AI helps the planet', key_concepts='What, Energy, Saving, Wildlife, Conservation', examples=0
[2025-06-17 14:30:44,143] INFO - __main__ - main.py:7209 - [f39b5939-ce54-467e-ba4c-332a63a09fff][enhance_lesson_content] Python-calculated TARGET values for AI's output state block: new_phase='diagnostic_probing_L5_eval_q1_ask_q2', q_index='1', total_q_asked='2'
[2025-06-17 14:30:44,144] INFO - __main__ - main.py:7215 - [f39b5939-ce54-467e-ba4c-332a63a09fff][diagnostic_logic] SIMPLIFIED: Trusting AI to handle diagnostic progression naturally
[2025-06-17 14:30:44,144] INFO - __main__ - main.py:7320 - [f39b5939-ce54-467e-ba4c-332a63a09fff] NATURAL COMPLETION: Trusting AI to handle diagnostic completion naturally
[2025-06-17 14:30:44,144] INFO - __main__ - main.py:7331 - [f39b5939-ce54-467e-ba4c-332a63a09fff] SIMPLIFIED: Trusting AI to determine appropriate phase transitions naturally
[2025-06-17 14:30:44,145] INFO - __main__ - main.py:7337 - [f39b5939-ce54-467e-ba4c-332a63a09fff] ✅ Template placeholder successfully substituted
[2025-06-17 14:30:44,145] INFO - __main__ - main.py:7341 - [f39b5939-ce54-467e-ba4c-332a63a09fff] ✅ Template contains calculated phase: diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:30:44,146] INFO - __main__ - main.py:7385 - [f39b5939-ce54-467e-ba4c-332a63a09fff] Prompt truncated from 14976 to 10707 chars for performance
[2025-06-17 14:30:44,146] INFO - __main__ - main.py:7388 - [f39b5939-ce54-467e-ba4c-332a63a09fff] CONTENT QUALITY DEBUG:
[2025-06-17 14:30:44,146] INFO - __main__ - main.py:7389 -   - Final prompt length: 10707 characters
[2025-06-17 14:30:44,147] INFO - __main__ - main.py:7390 -   - Subject: Artificial Intelligence
[2025-06-17 14:30:44,147] INFO - __main__ - main.py:7391 -   - Topic: How AI helps the planet
[2025-06-17 14:30:44,147] INFO - __main__ - main.py:7392 -   - Key concepts: What, Energy, Saving, Wildlife, Conservation
[2025-06-17 14:30:44,148] INFO - __main__ - main.py:7393 -   - Grade: Primary 5
[2025-06-17 14:30:44,148] INFO - __main__ - main.py:7394 -   - Phase: diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:30:44,148] INFO - __main__ - main.py:7395 -   - Student: Andrea
[2025-06-17 14:30:44,148] DEBUG - __main__ - main.py:7396 - [f39b5939-ce54-467e-ba4c-332a63a09fff] Prompt preview: 
MANDATORY: End with: // AI_STATE_UPDATE_BLOCK_START {"new_phase": "diagnostic_probing_L5_eval_q1_ask_q2", "interaction_count": 1} // AI_STATE_UPDATE_BLOCK_END

CRITICAL PHASE PROGRESSION: 
• The system has calculated the next phase as: diagnostic_probing_L5_eval_q1_ask_q2
• You MUST use this exact ...
[2025-06-17 14:30:44,149] INFO - __main__ - main.py:7424 - [f39b5939-ce54-467e-ba4c-332a63a09fff] Gemini API call attempt 1/3
[2025-06-17 14:30:45,678] INFO - __main__ - main.py:7442 - [f39b5939-ce54-467e-ba4c-332a63a09fff] ✅ Gemini API call succeeded on attempt 1
[2025-06-17 14:30:45,679] INFO - __main__ - main.py:7461 - [f39b5939-ce54-467e-ba4c-332a63a09fff] Gemini API call completed in 1.53s
[2025-06-17 14:30:45,681] DEBUG - __main__ - main.py:2196 - extract_gemini_text: Successfully extracted 572 characters
[2025-06-17 14:30:45,683] INFO - __main__ - main.py:7684 - [f39b5939-ce54-467e-ba4c-332a63a09fff][enhance_lesson_content] AI response: That's a great example, Andrea!  Now let's explore how AI can help save energy even further. Imagine a smart city using AI to control all those streetlights.  The AI could analyze traffic patterns and...
[2025-06-17 14:30:45,684] INFO - __main__ - main.py:7712 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🔍 STATE DEBUG: Looking for state update block in AI response
[2025-06-17 14:30:45,687] INFO - __main__ - main.py:7713 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🔍 STATE DEBUG: AI response length: 572 chars
[2025-06-17 14:30:45,690] INFO - __main__ - main.py:7725 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🔍 STATE DEBUG: Found state block: {"new_phase": "diagnostic_probing_L5_eval_q1_ask_q2", "interaction_count": 1}
[2025-06-17 14:30:45,691] INFO - __main__ - main.py:7727 - [f39b5939-ce54-467e-ba4c-332a63a09fff] ✅ Found AI state update: {'new_phase': 'diagnostic_probing_L5_eval_q1_ask_q2', 'interaction_count': 1}
[2025-06-17 14:30:45,693] INFO - __main__ - main.py:7791 - [f39b5939-ce54-467e-ba4c-332a63a09fff] SIMPLIFIED: Trusting AI state updates without aggressive diagnostic forcing
[2025-06-17 14:30:45,695] INFO - __main__ - main.py:7797 - [f39b5939-ce54-467e-ba4c-332a63a09fff] SIMPLIFIED: Using AI response without hardcoded fallbacks
[2025-06-17 14:30:45,696] INFO - __main__ - main.py:7817 - [f39b5939-ce54-467e-ba4c-332a63a09fff] Auto-corrected: added missing current_probing_level_number
[2025-06-17 14:30:45,697] INFO - __main__ - main.py:7821 - [f39b5939-ce54-467e-ba4c-332a63a09fff] Auto-corrected: added missing current_question_index
[2025-06-17 14:30:45,698] INFO - __main__ - main.py:7847 - [f39b5939-ce54-467e-ba4c-332a63a09fff] ✅ AI state validation completed: {'new_phase': 'diagnostic_probing_L5_eval_q1_ask_q2', 'interaction_count': 1, 'current_probing_level_number': 5, 'current_question_index': 1}
[2025-06-17 14:30:45,699] INFO - __main__ - main.py:7859 - [f39b5939-ce54-467e-ba4c-332a63a09fff] ✅ AI PROVIDED VALID STATE: Using AI's state update proposal
[2025-06-17 14:30:45,699] INFO - __main__ - main.py:7902 - [f39b5939-ce54-467e-ba4c-332a63a09fff] Placeholder apply_phase_transition_fixes detected or no fix made. Prioritizing AI's originally proposed state.
[2025-06-17 14:30:45,700] INFO - __main__ - main.py:7911 - [f39b5939-ce54-467e-ba4c-332a63a09fff] State updates after apply_fixes and potential AI prioritization: {'new_phase': 'diagnostic_probing_L5_eval_q1_ask_q2', 'interaction_count': 1, 'current_probing_level_number': 5, 'current_question_index': 1}
[2025-06-17 14:30:45,701] INFO - __main__ - main.py:7969 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 📝 DIAGNOSTIC PROGRESSION: Continue diagnostic - eval_phase=False, answers=2/5
[2025-06-17 14:30:45,702] INFO - __main__ - main.py:7972 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 📝 DIAGNOSTIC PROGRESSION: Need 3 more answers
[2025-06-17 14:30:45,702] INFO - __main__ - main.py:7974 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 📝 DIAGNOSTIC PROGRESSION: Not in eval_q5_decide_level phase
[2025-06-17 14:30:45,703] INFO - __main__ - main.py:7978 - [f39b5939-ce54-467e-ba4c-332a63a09fff] NATURAL COMPLETION: Trusting AI to handle diagnostic completion naturally
[2025-06-17 14:30:45,706] INFO - __main__ - main.py:8006 - [f39b5939-ce54-467e-ba4c-332a63a09fff] DIAGNOSTIC PROGRESSION: AI stuck in same phase - applying calculated fallback: diagnostic_probing_L5_eval_q1_ask_q2 → diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:30:45,707] INFO - __main__ - main.py:8055 - [f39b5939-ce54-467e-ba4c-332a63a09fff] DIAGNOSTIC FALLBACK: Applying calculated progression only when AI fails to provide updates: diagnostic_probing_L5_eval_q1_ask_q2 → diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:30:45,708] WARNING - __main__ - main.py:8070 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🔍 DIAGNOSTIC FALLBACK STATE UPDATE:
[2025-06-17 14:30:45,709] WARNING - __main__ - main.py:8071 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🔍   - Phase: diagnostic_probing_L5_eval_q1_ask_q2 → diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:30:45,710] WARNING - __main__ - main.py:8072 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🔍   - Question Index: 0
[2025-06-17 14:30:45,710] WARNING - __main__ - main.py:8073 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🔍   - Probing Level: 5
[2025-06-17 14:30:45,711] INFO - __main__ - main.py:8077 - [f39b5939-ce54-467e-ba4c-332a63a09fff] DIAGNOSTIC FALLBACK: System provided fallback state updates when AI failed to respond
[2025-06-17 14:30:45,711] INFO - __main__ - main.py:8078 - [f39b5939-ce54-467e-ba4c-332a63a09fff] DIAGNOSTIC ENFORCEMENT: Original AI phase: diagnostic_probing_L5_eval_q1_ask_q2, Enforced phase: diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:30:45,712] INFO - __main__ - main.py:8098 - [f39b5939-ce54-467e-ba4c-332a63a09fff] [OK] State update block guaranteed by system enforcement
[2025-06-17 14:30:45,712] DEBUG - __main__ - main.py:8164 - [f39b5939-ce54-467e-ba4c-332a63a09fff] Phase unchanged: diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:30:45,713] INFO - __main__ - main.py:8180 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 📋 FINAL STATE UPDATE: {'new_phase': 'diagnostic_probing_L5_eval_q1_ask_q2', 'current_probing_level_number': 5, 'current_question_index': 0, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': "That's a great question, especially here in Lagos where we use so much electricity!  Imagine all the streetlights in Lagos. Instead of them being on super bright all night long, even when no one is around, AI could make them smarter!  Smart Streetlights!  AI could be connected to the streetlights and use special sensors.  If there are no cars or people on a street late at night, the AI could tell the lights to dim down or even turn off a few, and then turn them bright again when a car comes. The AI could also learn when the sun rises and sets exactly each day and only turn the lights on when it's truly dark, saving energy compared to a fixed timer. This would save a lot of electricity because the lights are only super bright when they are really needed. It's like the AI is a smart energy manager for the whole city!", 'q2': "Yes I'm ready"}, 'levels_probed_and_failed': []}
[2025-06-17 14:30:45,714] INFO - __main__ - main.py:8216 - [f39b5939-ce54-467e-ba4c-332a63a09fff] ✅ Clean response prepared for user, state updates handled internally
[2025-06-17 14:30:45,715] INFO - __main__ - main.py:8232 - [f39b5939-ce54-467e-ba4c-332a63a09fff] ✅ AI response already includes personalization
[2025-06-17 14:30:45,715] INFO - __main__ - main.py:8238 - [f39b5939-ce54-467e-ba4c-332a63a09fff] [OK] State updates processed: {'new_phase': 'diagnostic_probing_L5_eval_q1_ask_q2', 'current_probing_level_number': 5, 'current_question_index': 0, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': "That's a great question, especially here in Lagos where we use so much electricity!  Imagine all the streetlights in Lagos. Instead of them being on super bright all night long, even when no one is around, AI could make them smarter!  Smart Streetlights!  AI could be connected to the streetlights and use special sensors.  If there are no cars or people on a street late at night, the AI could tell the lights to dim down or even turn off a few, and then turn them bright again when a car comes. The AI could also learn when the sun rises and sets exactly each day and only turn the lights on when it's truly dark, saving energy compared to a fixed timer. This would save a lot of electricity because the lights are only super bright when they are really needed. It's like the AI is a smart energy manager for the whole city!", 'q2': "Yes I'm ready"}, 'levels_probed_and_failed': []}
[2025-06-17 14:30:45,716] INFO - __main__ - main.py:8241 - [f39b5939-ce54-467e-ba4c-332a63a09fff] SIMPLIFIED: Diagnostic enforcement disabled to prevent backward transitions
[2025-06-17 14:30:45,716] WARNING - __main__ - main.py:8262 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🔄 STATE UPDATE PROCESSING:
[2025-06-17 14:30:45,717] WARNING - __main__ - main.py:8263 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🔄   - AI provided state update: True
[2025-06-17 14:30:45,717] WARNING - __main__ - main.py:8265 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🔄   - AI proposed phase: 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 14:30:45,718] WARNING - __main__ - main.py:8266 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🔄   - Current lesson phase: 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 14:30:45,718] WARNING - __main__ - main.py:8267 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🔄   - Python calculated phase: 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 14:30:45,719] INFO - __main__ - main.py:8275 - [f39b5939-ce54-467e-ba4c-332a63a09fff] ⚡ PERFORMANCE METRICS:
[2025-06-17 14:30:45,719] INFO - __main__ - main.py:8276 -   - Total execution time: 1.594s
[2025-06-17 14:30:45,720] WARNING - __main__ - main.py:5643 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🤖 AI RESPONSE RECEIVED:
[2025-06-17 14:30:45,722] WARNING - __main__ - main.py:5644 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🤖   - Content length: 429 chars
[2025-06-17 14:30:45,723] WARNING - __main__ - main.py:5645 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🤖   - State updates: {'new_phase': 'diagnostic_probing_L5_eval_q1_ask_q2', 'current_probing_level_number': 5, 'current_question_index': 0, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': "That's a great question, especially here in Lagos where we use so much electricity!  Imagine all the streetlights in Lagos. Instead of them being on super bright all night long, even when no one is around, AI could make them smarter!  Smart Streetlights!  AI could be connected to the streetlights and use special sensors.  If there are no cars or people on a street late at night, the AI could tell the lights to dim down or even turn off a few, and then turn them bright again when a car comes. The AI could also learn when the sun rises and sets exactly each day and only turn the lights on when it's truly dark, saving energy compared to a fixed timer. This would save a lot of electricity because the lights are only super bright when they are really needed. It's like the AI is a smart energy manager for the whole city!", 'q2': "Yes I'm ready"}, 'levels_probed_and_failed': []}
[2025-06-17 14:30:45,723] WARNING - __main__ - main.py:5646 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🤖   - Raw state block: None...
[2025-06-17 14:30:45,724] INFO - __main__ - main.py:5662 - [f39b5939-ce54-467e-ba4c-332a63a09fff] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
[2025-06-17 14:30:45,725] INFO - __main__ - main.py:5663 - [f39b5939-ce54-467e-ba4c-332a63a09fff] CURRENT PHASE DETERMINATION: AI=diagnostic_probing_L5_eval_q1_ask_q2, Session=diagnostic_probing_L5_eval_q1_ask_q2, Final=diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:30:46,006] INFO - __main__ - main.py:5772 - [f39b5939-ce54-467e-ba4c-332a63a09fff] AI processing completed in 1.89s
[2025-06-17 14:30:46,007] WARNING - __main__ - main.py:5783 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🔍 STATE UPDATE VALIDATION: current_phase='diagnostic_probing_L5_eval_q1_ask_q2', new_phase='diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 14:30:46,008] INFO - __main__ - main.py:3897 - [f39b5939-ce54-467e-ba4c-332a63a09fff] AI state update validation passed: diagnostic_probing_L5_eval_q1_ask_q2 → diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:30:46,010] WARNING - __main__ - main.py:5792 - [f39b5939-ce54-467e-ba4c-332a63a09fff] ✅ STATE UPDATE VALIDATION PASSED
[2025-06-17 14:30:46,012] WARNING - __main__ - main.py:5799 - [f39b5939-ce54-467e-ba4c-332a63a09fff] � PHASE MAINTAINED: diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:30:46,013] WARNING - __main__ - main.py:5806 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🔍 COMPLETE PHASE FLOW DEBUG:
[2025-06-17 14:30:46,015] WARNING - __main__ - main.py:5807 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🔍   1. Input phase: 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 14:30:46,016] WARNING - __main__ - main.py:5808 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🔍   2. Python calculated next phase: 'NOT_FOUND'
[2025-06-17 14:30:46,017] WARNING - __main__ - main.py:5809 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🔍   3. AI state updates: {'new_phase': 'diagnostic_probing_L5_eval_q1_ask_q2', 'current_probing_level_number': 5, 'current_question_index': 0, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': "That's a great question, especially here in Lagos where we use so much electricity!  Imagine all the streetlights in Lagos. Instead of them being on super bright all night long, even when no one is around, AI could make them smarter!  Smart Streetlights!  AI could be connected to the streetlights and use special sensors.  If there are no cars or people on a street late at night, the AI could tell the lights to dim down or even turn off a few, and then turn them bright again when a car comes. The AI could also learn when the sun rises and sets exactly each day and only turn the lights on when it's truly dark, saving energy compared to a fixed timer. This would save a lot of electricity because the lights are only super bright when they are really needed. It's like the AI is a smart energy manager for the whole city!", 'q2': "Yes I'm ready"}, 'levels_probed_and_failed': []}
[2025-06-17 14:30:46,019] WARNING - __main__ - main.py:5810 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🔍   4. Final phase to save: 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 14:30:46,021] WARNING - __main__ - main.py:5813 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 💾 FINAL STATE APPLICATION:
[2025-06-17 14:30:46,022] WARNING - __main__ - main.py:5814 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 💾   - Current phase input: 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 14:30:46,024] WARNING - __main__ - main.py:5815 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 💾   - State updates from AI: {'new_phase': 'diagnostic_probing_L5_eval_q1_ask_q2', 'current_probing_level_number': 5, 'current_question_index': 0, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': "That's a great question, especially here in Lagos where we use so much electricity!  Imagine all the streetlights in Lagos. Instead of them being on super bright all night long, even when no one is around, AI could make them smarter!  Smart Streetlights!  AI could be connected to the streetlights and use special sensors.  If there are no cars or people on a street late at night, the AI could tell the lights to dim down or even turn off a few, and then turn them bright again when a car comes. The AI could also learn when the sun rises and sets exactly each day and only turn the lights on when it's truly dark, saving energy compared to a fixed timer. This would save a lot of electricity because the lights are only super bright when they are really needed. It's like the AI is a smart energy manager for the whole city!", 'q2': "Yes I'm ready"}, 'levels_probed_and_failed': []}
[2025-06-17 14:30:46,026] WARNING - __main__ - main.py:5816 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 💾   - Final phase to save: 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 14:30:46,027] WARNING - __main__ - main.py:5817 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 💾   - Phase change: False
[2025-06-17 14:30:46,028] INFO - __main__ - main.py:3929 - [f39b5939-ce54-467e-ba4c-332a63a09fff] DIAGNOSTIC_FLOW_METRICS:
[2025-06-17 14:30:46,028] INFO - __main__ - main.py:3930 - [f39b5939-ce54-467e-ba4c-332a63a09fff]   Phase transition: diagnostic_probing_L5_eval_q1_ask_q2 -> diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:30:46,029] INFO - __main__ - main.py:3931 - [f39b5939-ce54-467e-ba4c-332a63a09fff]   Current level: 5
[2025-06-17 14:30:46,030] INFO - __main__ - main.py:3932 - [f39b5939-ce54-467e-ba4c-332a63a09fff]   Question index: 1
[2025-06-17 14:30:46,030] INFO - __main__ - main.py:3933 - [f39b5939-ce54-467e-ba4c-332a63a09fff]   First encounter: True
[2025-06-17 14:30:46,031] INFO - __main__ - main.py:3938 - [f39b5939-ce54-467e-ba4c-332a63a09fff]   Answers collected: 2
[2025-06-17 14:30:46,031] INFO - __main__ - main.py:3939 - [f39b5939-ce54-467e-ba4c-332a63a09fff]   Levels failed: 0
[2025-06-17 14:30:46,032] INFO - __main__ - main.py:3897 - [f39b5939-ce54-467e-ba4c-332a63a09fff] AI state update validation passed: diagnostic_probing_L5_eval_q1_ask_q2 → diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:30:46,032] INFO - __main__ - main.py:3943 - [f39b5939-ce54-467e-ba4c-332a63a09fff]   State update valid: True
[2025-06-17 14:30:46,033] INFO - __main__ - main.py:3950 - [f39b5939-ce54-467e-ba4c-332a63a09fff]   Diagnostic complete: False
[2025-06-17 14:30:46,034] WARNING - __main__ - main.py:5829 - [f39b5939-ce54-467e-ba4c-332a63a09fff] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 0, current_q_index_for_prompt = NOT_FOUND
[2025-06-17 14:30:46,521] WARNING - __main__ - main.py:5856 - [f39b5939-ce54-467e-ba4c-332a63a09fff] ✅ SESSION STATE SAVED TO FIRESTORE:
[2025-06-17 14:30:46,523] WARNING - __main__ - main.py:5857 - [f39b5939-ce54-467e-ba4c-332a63a09fff] ✅   - Phase: diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:30:46,524] WARNING - __main__ - main.py:5858 - [f39b5939-ce54-467e-ba4c-332a63a09fff] ✅   - Probing Level: 5
[2025-06-17 14:30:46,526] WARNING - __main__ - main.py:5859 - [f39b5939-ce54-467e-ba4c-332a63a09fff] ✅   - Question Index: 0
[2025-06-17 14:30:46,528] WARNING - __main__ - main.py:5860 - [f39b5939-ce54-467e-ba4c-332a63a09fff] ✅   - Diagnostic Complete: False
[2025-06-17 14:30:47,333] INFO - __main__ - main.py:5919 - [f39b5939-ce54-467e-ba4c-332a63a09fff] ✅ Updated existing session document: session_b71023f5-62ee-482e-be3a-783fab2fb64c
[2025-06-17 14:30:47,335] WARNING - __main__ - main.py:5920 - [f39b5939-ce54-467e-ba4c-332a63a09fff] ✅ SESSION UPDATE COMPLETE:
[2025-06-17 14:30:47,336] WARNING - __main__ - main.py:5921 - [f39b5939-ce54-467e-ba4c-332a63a09fff] ✅   - Session ID: session_b71023f5-62ee-482e-be3a-783fab2fb64c
[2025-06-17 14:30:47,338] WARNING - __main__ - main.py:5922 - [f39b5939-ce54-467e-ba4c-332a63a09fff] ✅   - Phase transition: diagnostic_probing_L5_eval_q1_ask_q2 → diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:30:47,339] WARNING - __main__ - main.py:5923 - [f39b5939-ce54-467e-ba4c-332a63a09fff] ✅   - Interaction logged successfully
[2025-06-17 14:30:47,341] INFO - __main__ - main.py:9173 - [f39b5939-ce54-467e-ba4c-332a63a09fff] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
[2025-06-17 14:30:47,342] DEBUG - __main__ - main.py:2753 - [f39b5939-ce54-467e-ba4c-332a63a09fff] FINAL_ASSESSMENT_BLOCK not found in AI response.
[2025-06-17 14:30:47,343] DEBUG - __main__ - main.py:5969 - [f39b5939-ce54-467e-ba4c-332a63a09fff] No final assessment data found in AI response
[2025-06-17 14:30:47,348] INFO - __main__ - main.py:6034 - [f39b5939-ce54-467e-ba4c-332a63a09fff] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
[2025-06-17 14:30:47,350] WARNING - __main__ - main.py:625 - High response time detected: 6.52s for enhance_content_api
[2025-06-17 14:30:47,352] INFO - werkzeug - _internal.py:97 - 127.0.0.1 - - [17/Jun/2025 14:30:47] "POST /api/enhance-content HTTP/1.1" 200 -
[2025-06-17 14:31:15,546] INFO - __main__ - main.py:4831 - REQUEST: POST http://localhost:5000/api/enhance-content -> endpoint: enhance_content_api
[2025-06-17 14:31:15,547] INFO - __main__ - main.py:4874 - Incoming request: {"request_id": "1c2d15e7-e385-4f65-95f6-2dbe99150d18", "timestamp": "2025-06-17T13:31:15.547175+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.9.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-AI-001", "content_to_enhance": "That's even smarter! It's like the AI is really thinking about everything!  How it saves energy: If the AI can see when no one is on a street, it means we don't need those lights on full power. So, it can dim them or turn them off. This means the city doesn't use as much electricity. It's like turning off the lights in a room when no one is in it at home \u2013 it saves money on the electricity bill for the whole city! Less energy used means less pollution from making that electricity.  How it protects wildlife (like bats!): This is super important for animals that come out at night, like bats!  Bats use sounds (like echoes!) to find their way around and catch insects in the dark. Very bright streetlights can confuse them, make it hard for them to see their food, and mess up their \"sound maps.\" If the lights are dimmer or off in quiet areas, it creates darker places for bats to fly and hunt naturally. They won't be scared away or get lost because of too much light. It helps them live their normal night lives without bright lights bothering them. So, the AI helps us be kind to both the planet's energy and the animals!", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "Primary 5", "level": "P5", "subject": "Artificial Intelligence", "session_id": "session_b71023f5-62ee-482e-be3a-783fab2fb64c", "chat_history": [{"role": "assistant", "content": "Hi Andrea! I'm your AI tutor, and I'm excited to help you learn about how Artificial Intelligence helps our planet. We'll start with a quick diagnostic assessment to see what you already know. This helps me tailor the lessons perfectly for you. Are you ready to begin with some questions about what AI is, how it saves energy, protects wildlife, and helps with conservation?", "timestamp": "2025-06-17T13:26:37.197Z"}, {"role": "user", "content": "Yes I'm ready", "timestamp": "2025-06-17T13:27:05.374Z"}, {"role": "assistant", "content": "Hi Andrea! Great! Let's start with our first question about how AI helps the planet: Imagine a large national park in Nigeria filled with endangered animals. How could AI be used to help protect these animals and conserve their habitats?", "timestamp": "2025-06-17T13:27:10.398Z"}, {"role": "user", "content": "Hi! This sounds like a superhero job for computers!  Robot Eyes (AI Cameras): AI could use special cameras that are always watching the park. If a poacher (someone who tries to hurt animals illegally) comes into the park, the AI could instantly see them and tell the park rangers! It could also count animals, like elephants or lions, to see if their numbers are going up or down.   Listening Ears (AI Microphones): AI could listen for sounds like gunshots or chainsaws (cutting down trees). If it hears something bad, it can tell the rangers right away so they can go investigate.  Smart Trackers on Animals: They could put tiny trackers on some animals, and AI could watch where they go. If an animal goes somewhere dangerous, or if it stops moving for too long, the AI could alert the rangers that something might be wrong.  Watching the Forest: AI could look at pictures from satellites (like big cameras in space) to see if trees are being cut down where they shouldn't be, or if there's a bushfire starting. It helps keep the animals' homes safe!  So, AI is like having super smart, tireless helpers always watching and listening to protect our animals!", "timestamp": "2025-06-17T13:29:21.480Z"}, {"role": "assistant", "content": "That's a fantastic idea, Andrea! AI cameras could definitely help protect wildlife. Now let's explore how AI saves energy. Can you think of an example of how AI might help reduce energy consumption in a city, like Lagos?", "timestamp": "2025-06-17T13:29:27.852Z"}, {"role": "user", "content": "That's a great question, especially here in Lagos where we use so much electricity!  Imagine all the streetlights in Lagos. Instead of them being on super bright all night long, even when no one is around, AI could make them smarter!  Smart Streetlights!  AI could be connected to the streetlights and use special sensors.  If there are no cars or people on a street late at night, the AI could tell the lights to dim down or even turn off a few, and then turn them bright again when a car comes. The AI could also learn when the sun rises and sets exactly each day and only turn the lights on when it's truly dark, saving energy compared to a fixed timer. This would save a lot of electricity because the lights are only super bright when they are really needed. It's like the AI is a smart energy manager for the whole city!", "timestamp": "2025-06-17T13:30:40.742Z"}, {"role": "assistant", "content": "That's a great example, Andrea! Now let's explore how AI can help save energy even further. Imagine a smart city using AI to control all those streetlights. The AI could analyze traffic patterns and adjust the brightness or even turn off lights in areas with little or no activity during the night. How do you think this would help conserve energy and protect wildlife, like the bats that are disturbed by bright lights at night?", "timestamp": "2025-06-17T13:30:47.383Z"}]}}
[2025-06-17 14:31:15,549] INFO - temporary_auth_bypass - temporary_auth_bypass.py:29 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] Using temporary auth bypass with mock token
[2025-06-17 14:31:15,549] DEBUG - asyncio - proactor_events.py:631 - Using proactor: IocpProactor
[2025-06-17 14:31:15,553] WARNING - __main__ - main.py:5067 - 🔥 ENHANCE-CONTENT ENDPOINT CALLED!
[2025-06-17 14:31:15,558] INFO - __main__ - main.py:5178 - [enhance_content_api] Received request with decoded token.
[2025-06-17 14:31:15,559] INFO - __main__ - main.py:5180 - [enhance_content_api][1c2d15e7-e385-4f65-95f6-2dbe99150d18] Processing enhance content request
[2025-06-17 14:31:15,560] INFO - __main__ - main.py:5226 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18][enhance_content_api] Parsed JSON payload (first 250 bytes): {'student_id': 'andrea_ugono_33305', 'lesson_ref': 'P5-AI-001', 'content_to_enhance': 'That\'s even smarter! It\'s like the AI is really thinking about everything!  How it saves energy: If the AI can see when no one is on a street, it means we don\'t
[2025-06-17 14:31:15,834] INFO - __main__ - main.py:4424 - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
[2025-06-17 14:31:15,836] DEBUG - __main__ - main.py:596 - Cache hit for fetch_lesson_data
[2025-06-17 14:31:15,837] INFO - __main__ - main.py:5272 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] ✅ All required fields present after lesson content parsing and mapping
[2025-06-17 14:31:15,839] INFO - __main__ - main.py:5311 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] Attempting to infer module. GS Subject Slug: 'artificial_intelligence'.
[2025-06-17 14:31:15,841] INFO - __main__ - main.py:2273 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] AI Inference: Inferring module for subject 'artificial_intelligence', lesson 'AI: Saving Our Planet!'.
[2025-06-17 14:31:16,339] INFO - __main__ - main.py:2332 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] AI Inference: Loaded metadata for module 'ai_awareness_and_foundations' ('AI Awareness & Foundations')
[2025-06-17 14:31:16,340] INFO - __main__ - main.py:2332 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] AI Inference: Loaded metadata for module 'ai_tools_and_applications' ('AI Tools & Applications')
[2025-06-17 14:31:16,341] INFO - __main__ - main.py:2332 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] AI Inference: Loaded metadata for module 'computational_thinking_and_coding' ('Computational Thinking & Coding')
[2025-06-17 14:31:16,341] INFO - __main__ - main.py:2332 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] AI Inference: Loaded metadata for module 'data_literacy_and_machine_learning' ('Data Literacy & Machine Learning')
[2025-06-17 14:31:16,342] INFO - __main__ - main.py:2332 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] AI Inference: Loaded metadata for module 'ethics_and_societal_impact' ('Ethics & Societal Impact')
[2025-06-17 14:31:16,342] INFO - __main__ - main.py:2401 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] AI Inference: Starting module inference for subject 'artificial_intelligence' with 5 module options
[2025-06-17 14:31:16,343] DEBUG - __main__ - main.py:2415 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] AI Inference: Sample modules available (first 3):
- ai_awareness_and_foundations: AI Awareness & Foundations
- ai_tools_and_applications: AI Tools & Applications
- computational_thinking_and_coding: Computational Thinking & Coding
[2025-06-17 14:31:16,344] DEBUG - __main__ - main.py:2418 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] AI Inference Prompt (first 500 chars): 
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: AI: Saving Our Planet!. Topic: How AI helps the planet. Learning Objectives: Explore AI’s role in saving energy.; Understand AI’s impact on wildlife conservation.. Key Concepts: Explore; role; saving; energy; Understand; impact; wildlife; conservation; What; Energy Saving. Introducti...
[2025-06-17 14:31:16,344] DEBUG - __main__ - main.py:2419 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] AI Inference Lesson Summary (first 300 chars): Lesson Title: AI: Saving Our Planet!. Topic: How AI helps the planet. Learning Objectives: Explore AI’s role in saving energy.; Understand AI’s impact on wildlife conservation.. Key Concepts: Explore; role; saving; energy; Understand; impact; wildlife; conservation; What; Energy Saving. Introduction...
[2025-06-17 14:31:16,345] DEBUG - __main__ - main.py:2420 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] AI Inference Module Options (first 200 chars): 1. Slug: "ai_awareness_and_foundations", Name: "AI Awareness & Foundations", Description: "Conceptual journey from recognising smart helpers to understanding core AI paradigms (symbolic, statistical, ...
[2025-06-17 14:31:16,346] INFO - __main__ - main.py:2424 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] AI Inference: Calling Gemini API for module inference...
[2025-06-17 14:31:16,935] DEBUG - __main__ - main.py:2196 - extract_gemini_text: Successfully extracted 25 characters
[2025-06-17 14:31:16,936] INFO - __main__ - main.py:2434 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] AI Inference: Gemini API call completed in 0.59s. Raw response: 'ai_tools_and_applications'
[2025-06-17 14:31:16,937] DEBUG - __main__ - main.py:2456 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] AI Inference: Cleaned slug: 'ai_tools_and_applications'
[2025-06-17 14:31:16,940] INFO - __main__ - main.py:2461 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] AI Inference: Successfully matched module by slug. Chosen: 'ai_tools_and_applications' (AI Tools & Applications)
[2025-06-17 14:31:16,941] INFO - __main__ - main.py:5344 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] Successfully inferred module ID via AI: ai_tools_and_applications
[2025-06-17 14:31:16,943] INFO - __main__ - main.py:5370 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] Effective module name for prompt context: 'AI Tools & Applications' (Module ID: ai_tools_and_applications)
[2025-06-17 14:31:17,194] INFO - __main__ - main.py:1995 - No prior student performance document found for Topic: artificial_intelligence_Primary 5_artificial_intelligence_ai_tools_and_applications
[2025-06-17 14:31:17,708] WARNING - __main__ - main.py:5391 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🔍 SESSION STATE DEBUG:
[2025-06-17 14:31:17,710] WARNING - __main__ - main.py:5392 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🔍   - Session exists: True
[2025-06-17 14:31:17,711] WARNING - __main__ - main.py:5393 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🔍   - Current phase: diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:31:17,712] WARNING - __main__ - main.py:5394 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🔍   - State data keys: ['created_at', 'session_id', 'key_concepts_str_for_ai', 'blooms_levels_str_for_ai', 'last_diagnostic_question_text_asked', 'last_updated', 'current_probing_level_number', 'diagnostic_completed_this_session', 'assigned_level_for_teaching', 'lesson_context_snapshot', 'student_name', 'levels_probed_and_failed', 'is_first_encounter_for_module', 'current_phase', 'current_lesson_phase', 'last_update_request_id', 'current_session_working_level', 'current_question_index', 'student_id', 'last_modified', 'latest_assessed_level_for_module', 'student_answers_for_probing_level']
[2025-06-17 14:31:17,714] INFO - __main__ - main.py:5458 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] �🔍 FIRST ENCOUNTER LOGIC:
[2025-06-17 14:31:17,715] INFO - __main__ - main.py:5459 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18]   assigned_level_for_teaching (session): None
[2025-06-17 14:31:17,716] INFO - __main__ - main.py:5460 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18]   latest_assessed_level (profile): None
[2025-06-17 14:31:17,716] INFO - __main__ - main.py:5461 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18]   teaching_level_for_returning_student: None
[2025-06-17 14:31:17,717] INFO - __main__ - main.py:5462 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18]   has_completed_diagnostic_before: False
[2025-06-17 14:31:17,718] INFO - __main__ - main.py:5463 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18]   is_first_encounter_for_module: True
[2025-06-17 14:31:17,718] WARNING - __main__ - main.py:5468 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
[2025-06-17 14:31:17,719] INFO - __main__ - main.py:5474 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🔍 PHASE INVESTIGATION:
[2025-06-17 14:31:17,719] INFO - __main__ - main.py:5475 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18]   Retrieved from Firestore: 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 14:31:17,720] INFO - __main__ - main.py:5476 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'diagnostic_start_probe'
[2025-06-17 14:31:17,721] INFO - __main__ - main.py:5477 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18]   Is first encounter: True
[2025-06-17 14:31:17,723] INFO - __main__ - main.py:5478 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18]   Diagnostic completed: False
[2025-06-17 14:31:17,724] INFO - __main__ - main.py:5484 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] ✅ Using stored phase from Firestore: 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 14:31:17,725] INFO - __main__ - main.py:5498 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
[2025-06-17 14:31:17,726] INFO - __main__ - main.py:5500 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] Final phase for AI logic: diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:31:17,726] INFO - __main__ - main.py:5514 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] NEW SESSION: Using phase-based question index 0 from phase diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:31:17,727] INFO - __main__ - main.py:3692 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] Diagnostic context validation passed
[2025-06-17 14:31:17,727] INFO - __main__ - main.py:3725 - DETERMINE_PHASE: Resuming in-progress diagnostic at phase: 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 14:31:17,728] WARNING - __main__ - main.py:5586 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'diagnostic_probing_L5_eval_q1_ask_q2' for first encounter
[2025-06-17 14:31:17,728] INFO - __main__ - main.py:3802 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] Enhanced diagnostic context with 35 fields
[2025-06-17 14:31:17,728] INFO - __main__ - main.py:5608 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] Robust diagnostic context prepared successfully. Phase: diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:31:17,729] DEBUG - __main__ - main.py:5609 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'diagnostic_context', 'performance_tracking', 'level_specific_guidance', 'current_phase_for_ai']
[2025-06-17 14:31:17,729] WARNING - __main__ - main.py:5617 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🤖 AI PROMPT GENERATION:
[2025-06-17 14:31:17,730] WARNING - __main__ - main.py:5618 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🤖   - Current phase: diagnostic_start_probe
[2025-06-17 14:31:17,730] WARNING - __main__ - main.py:5619 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🤖   - Student query: That's even smarter! It's like the AI is really thinking about everything!  How it saves energy: If ...
[2025-06-17 14:31:17,731] WARNING - __main__ - main.py:5620 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🤖   - Context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'diagnostic_context', 'performance_tracking', 'level_specific_guidance', 'current_phase_for_ai']
[2025-06-17 14:31:17,731] INFO - __main__ - main.py:6493 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] enhance_lesson_content invoked. Query: 'That's even smarter! It's like the AI is really th...'
[2025-06-17 14:31:17,732] INFO - __main__ - main.py:6536 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] CONTEXT DEBUG: lesson_phase from context = 'diagnostic_probing_L5_eval_q1_ask_q2', processed = 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 14:31:17,732] INFO - __main__ - main.py:6547 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18][enhance_lesson_content] Received from context - phase: 'diagnostic_probing_L5_eval_q1_ask_q2', module_id: 'ai_tools_and_applications', gs_subject_slug: 'artificial_intelligence'
[2025-06-17 14:31:17,733] INFO - __main__ - main.py:6577 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] DIAGNOSTIC ANSWER: Storing Q1 answer from phase diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:31:17,733] INFO - __main__ - main.py:6586 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] DIAGNOSTIC ANSWER STORED: q1 = 'That's even smarter! It's like the AI is really th...'
[2025-06-17 14:31:17,733] INFO - __main__ - main.py:6587 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] Total diagnostic answers now: 2/5
[2025-06-17 14:31:17,734] INFO - __main__ - main.py:6628 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 📝 DIAGNOSTIC PROGRESSION: Continue with 2/5 answers, phase: diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:31:17,734] INFO - __main__ - main.py:6766 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18][enhance_lesson_content] Proceeding to main AI prompt generation. Phase: diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:31:17,734] INFO - __main__ - main.py:6773 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18][DIAGNOSTIC_FLOW] Phase: diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:31:17,735] INFO - __main__ - main.py:6774 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18][DIAGNOSTIC_FLOW] Questions asked: 2/5
[2025-06-17 14:31:17,735] INFO - __main__ - main.py:6775 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18][DIAGNOSTIC_FLOW] Question index: 0
[2025-06-17 14:31:17,735] INFO - __main__ - main.py:6776 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18][DIAGNOSTIC_FLOW] Student answers count: 2
[2025-06-17 14:31:17,735] INFO - __main__ - main.py:6781 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18][DIAGNOSTIC_FLOW] Answer q1: That's even smarter! It's like the AI is really th...
[2025-06-17 14:31:17,736] INFO - __main__ - main.py:6781 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18][DIAGNOSTIC_FLOW] Answer q2: Yes I'm ready...
[2025-06-17 14:31:17,736] INFO - __main__ - main.py:6805 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18][DIAGNOSTIC_FLOW] SIMPLIFIED LOGIC: Phase=diagnostic_probing_L5_eval_q1_ask_q2, Answers=2/5
[2025-06-17 14:31:17,736] INFO - __main__ - main.py:6808 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18][DIAGNOSTIC_FLOW] SIMPLIFIED: Trusting AI to handle diagnostic completion naturally
[2025-06-17 14:31:17,737] INFO - __main__ - main.py:6839 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] INTERACTION COUNT: Updated to 1 and saved to context
[2025-06-17 14:31:17,739] INFO - __main__ - main.py:6853 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] DIAGNOSTIC PROGRESSION: Calculating next phase for diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:31:17,739] INFO - __main__ - main.py:6861 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] DIAGNOSTIC PROGRESSION: Running diagnostic logic for phase 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 14:31:17,740] INFO - __main__ - main.py:7120 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🎯 ENHANCED PHASE CALCULATION: diagnostic_probing_L5_eval_q1_ask_q2 → diagnostic_probing_L5_eval_q1_ask_q2 (interaction 1)
[2025-06-17 14:31:17,741] WARNING - __main__ - main.py:7123 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🔍 DIAGNOSTIC FLOW START:
[2025-06-17 14:31:17,741] WARNING - __main__ - main.py:7124 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🔍   - Input phase from context: 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 14:31:17,741] WARNING - __main__ - main.py:7125 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🔍   - User query: 'That's even smarter! It's like the AI is really thinking about everything!  How it saves energy: If the AI can see when no one is on a street, it means we don't need those lights on full power. So, it can dim them or turn them off. This means the city doesn't use as much electricity. It's like turning off the lights in a room when no one is in it at home – it saves money on the electricity bill for the whole city! Less energy used means less pollution from making that electricity.  How it protects wildlife (like bats!): This is super important for animals that come out at night, like bats!  Bats use sounds (like echoes!) to find their way around and catch insects in the dark. Very bright streetlights can confuse them, make it hard for them to see their food, and mess up their "sound maps." If the lights are dimmer or off in quiet areas, it creates darker places for bats to fly and hunt naturally. They won't be scared away or get lost because of too much light. It helps them live their normal night lives without bright lights bothering them. So, the AI helps us be kind to both the planet's energy and the animals!'
[2025-06-17 14:31:17,743] WARNING - __main__ - main.py:7126 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🔍   - Is actual student response: True
[2025-06-17 14:31:17,744] WARNING - __main__ - main.py:7127 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🔍   - Current probing level: 5
[2025-06-17 14:31:17,744] WARNING - __main__ - main.py:7128 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🔍   - Current question index: 0
[2025-06-17 14:31:17,744] WARNING - __main__ - main.py:7129 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🔍   - Student answers count: 2
[2025-06-17 14:31:17,745] INFO - __main__ - main.py:7132 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🔧 NATURAL PROGRESSION DEBUG:
[2025-06-17 14:31:17,745] INFO - __main__ - main.py:7133 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🔧   - lesson_phase_from_context: 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 14:31:17,745] INFO - __main__ - main.py:7134 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🔧   - python_calculated_new_phase_for_block: 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 14:31:17,746] INFO - __main__ - main.py:7135 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🔧   - user_query: 'That's even smarter! It's like the AI is really thinking about everything!  How it saves energy: If the AI can see when no one is on a street, it means we don't need those lights on full power. So, it can dim them or turn them off. This means the city doesn't use as much electricity. It's like turning off the lights in a room when no one is in it at home – it saves money on the electricity bill for the whole city! Less energy used means less pollution from making that electricity.  How it protects wildlife (like bats!): This is super important for animals that come out at night, like bats!  Bats use sounds (like echoes!) to find their way around and catch insects in the dark. Very bright streetlights can confuse them, make it hard for them to see their food, and mess up their "sound maps." If the lights are dimmer or off in quiet areas, it creates darker places for bats to fly and hunt naturally. They won't be scared away or get lost because of too much light. It helps them live their normal night lives without bright lights bothering them. So, the AI helps us be kind to both the planet's energy and the animals!'
[2025-06-17 14:31:17,746] INFO - __main__ - main.py:7136 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🔧   - trusting_ai_state_updates: True
[2025-06-17 14:31:17,746] WARNING - __main__ - main.py:7139 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🎯 PHASE CALCULATION RESULT:
[2025-06-17 14:31:17,747] WARNING - __main__ - main.py:7140 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🎯   - Current phase: 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 14:31:17,747] WARNING - __main__ - main.py:7141 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🎯   - Calculated new phase: 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 14:31:17,747] WARNING - __main__ - main.py:7142 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🎯   - Phase changed: False
[2025-06-17 14:31:17,747] WARNING - __main__ - main.py:7145 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🎯 DIAGNOSTIC SPECIFIC:
[2025-06-17 14:31:17,748] WARNING - __main__ - main.py:7146 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🎯   - In diagnostic phase: True
[2025-06-17 14:31:17,748] WARNING - __main__ - main.py:7147 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🎯   - Student answers stored: {'q1': 'That\'s even smarter! It\'s like the AI is really thinking about everything!  How it saves energy: If the AI can see when no one is on a street, it means we don\'t need those lights on full power. So, it can dim them or turn them off. This means the city doesn\'t use as much electricity. It\'s like turning off the lights in a room when no one is in it at home – it saves money on the electricity bill for the whole city! Less energy used means less pollution from making that electricity.  How it protects wildlife (like bats!): This is super important for animals that come out at night, like bats!  Bats use sounds (like echoes!) to find their way around and catch insects in the dark. Very bright streetlights can confuse them, make it hard for them to see their food, and mess up their "sound maps." If the lights are dimmer or off in quiet areas, it creates darker places for bats to fly and hunt naturally. They won\'t be scared away or get lost because of too much light. It helps them live their normal night lives without bright lights bothering them. So, the AI helps us be kind to both the planet\'s energy and the animals!', 'q2': "Yes I'm ready"}
[2025-06-17 14:31:17,749] WARNING - __main__ - main.py:7148 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🎯   - Expected progression: Should move to next question/phase
[2025-06-17 14:31:17,750] INFO - __main__ - main.py:1829 - ENHANCED lesson content extraction: subject='Artificial Intelligence', topic='How AI helps the planet', key_concepts='What, Energy, Saving, Wildlife, Conservation', examples=0
[2025-06-17 14:31:17,750] INFO - __main__ - main.py:7209 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18][enhance_lesson_content] Python-calculated TARGET values for AI's output state block: new_phase='diagnostic_probing_L5_eval_q1_ask_q2', q_index='1', total_q_asked='2'
[2025-06-17 14:31:17,750] INFO - __main__ - main.py:7215 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18][diagnostic_logic] SIMPLIFIED: Trusting AI to handle diagnostic progression naturally
[2025-06-17 14:31:17,751] INFO - __main__ - main.py:7320 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] NATURAL COMPLETION: Trusting AI to handle diagnostic completion naturally
[2025-06-17 14:31:17,752] INFO - __main__ - main.py:7331 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] SIMPLIFIED: Trusting AI to determine appropriate phase transitions naturally
[2025-06-17 14:31:17,752] INFO - __main__ - main.py:7337 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] ✅ Template placeholder successfully substituted
[2025-06-17 14:31:17,752] INFO - __main__ - main.py:7341 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] ✅ Template contains calculated phase: diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:31:17,755] INFO - __main__ - main.py:7385 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] Prompt truncated from 16553 to 10707 chars for performance
[2025-06-17 14:31:17,757] INFO - __main__ - main.py:7388 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] CONTENT QUALITY DEBUG:
[2025-06-17 14:31:17,758] INFO - __main__ - main.py:7389 -   - Final prompt length: 10707 characters
[2025-06-17 14:31:17,758] INFO - __main__ - main.py:7390 -   - Subject: Artificial Intelligence
[2025-06-17 14:31:17,759] INFO - __main__ - main.py:7391 -   - Topic: How AI helps the planet
[2025-06-17 14:31:17,759] INFO - __main__ - main.py:7392 -   - Key concepts: What, Energy, Saving, Wildlife, Conservation
[2025-06-17 14:31:17,759] INFO - __main__ - main.py:7393 -   - Grade: Primary 5
[2025-06-17 14:31:17,760] INFO - __main__ - main.py:7394 -   - Phase: diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:31:17,760] INFO - __main__ - main.py:7395 -   - Student: Andrea
[2025-06-17 14:31:17,760] DEBUG - __main__ - main.py:7396 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] Prompt preview: 
MANDATORY: End with: // AI_STATE_UPDATE_BLOCK_START {"new_phase": "diagnostic_probing_L5_eval_q1_ask_q2", "interaction_count": 1} // AI_STATE_UPDATE_BLOCK_END

CRITICAL PHASE PROGRESSION: 
• The system has calculated the next phase as: diagnostic_probing_L5_eval_q1_ask_q2
• You MUST use this exact ...
[2025-06-17 14:31:17,761] INFO - __main__ - main.py:7424 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] Gemini API call attempt 1/3
[2025-06-17 14:31:19,184] INFO - __main__ - main.py:7442 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] ✅ Gemini API call succeeded on attempt 1
[2025-06-17 14:31:19,186] INFO - __main__ - main.py:7461 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] Gemini API call completed in 1.42s
[2025-06-17 14:31:19,188] DEBUG - __main__ - main.py:2196 - extract_gemini_text: Successfully extracted 398 characters
[2025-06-17 14:31:19,190] INFO - __main__ - main.py:7684 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18][enhance_lesson_content] AI response: That's a great example of how AI can save energy!  Now let's explore another way AI helps conserve wildlife. Imagine a vast national park in Nigeria with many endangered animals.  How could AI be used...
[2025-06-17 14:31:19,191] INFO - __main__ - main.py:7712 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🔍 STATE DEBUG: Looking for state update block in AI response
[2025-06-17 14:31:19,193] INFO - __main__ - main.py:7713 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🔍 STATE DEBUG: AI response length: 398 chars
[2025-06-17 14:31:19,194] INFO - __main__ - main.py:7725 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🔍 STATE DEBUG: Found state block: {"new_phase": "diagnostic_probing_L5_eval_q1_ask_q2", "interaction_count": 1}
[2025-06-17 14:31:19,195] INFO - __main__ - main.py:7727 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] ✅ Found AI state update: {'new_phase': 'diagnostic_probing_L5_eval_q1_ask_q2', 'interaction_count': 1}
[2025-06-17 14:31:19,196] INFO - __main__ - main.py:7791 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] SIMPLIFIED: Trusting AI state updates without aggressive diagnostic forcing
[2025-06-17 14:31:19,197] INFO - __main__ - main.py:7797 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] SIMPLIFIED: Using AI response without hardcoded fallbacks
[2025-06-17 14:31:19,198] INFO - __main__ - main.py:7817 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] Auto-corrected: added missing current_probing_level_number
[2025-06-17 14:31:19,198] INFO - __main__ - main.py:7821 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] Auto-corrected: added missing current_question_index
[2025-06-17 14:31:19,199] INFO - __main__ - main.py:7847 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] ✅ AI state validation completed: {'new_phase': 'diagnostic_probing_L5_eval_q1_ask_q2', 'interaction_count': 1, 'current_probing_level_number': 5, 'current_question_index': 1}
[2025-06-17 14:31:19,200] INFO - __main__ - main.py:7859 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] ✅ AI PROVIDED VALID STATE: Using AI's state update proposal
[2025-06-17 14:31:19,200] INFO - __main__ - main.py:7902 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] Placeholder apply_phase_transition_fixes detected or no fix made. Prioritizing AI's originally proposed state.
[2025-06-17 14:31:19,201] INFO - __main__ - main.py:7911 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] State updates after apply_fixes and potential AI prioritization: {'new_phase': 'diagnostic_probing_L5_eval_q1_ask_q2', 'interaction_count': 1, 'current_probing_level_number': 5, 'current_question_index': 1}
[2025-06-17 14:31:19,201] INFO - __main__ - main.py:7969 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 📝 DIAGNOSTIC PROGRESSION: Continue diagnostic - eval_phase=False, answers=2/5
[2025-06-17 14:31:19,202] INFO - __main__ - main.py:7972 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 📝 DIAGNOSTIC PROGRESSION: Need 3 more answers
[2025-06-17 14:31:19,203] INFO - __main__ - main.py:7974 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 📝 DIAGNOSTIC PROGRESSION: Not in eval_q5_decide_level phase
[2025-06-17 14:31:19,203] INFO - __main__ - main.py:7978 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] NATURAL COMPLETION: Trusting AI to handle diagnostic completion naturally
[2025-06-17 14:31:19,205] INFO - __main__ - main.py:8006 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] DIAGNOSTIC PROGRESSION: AI stuck in same phase - applying calculated fallback: diagnostic_probing_L5_eval_q1_ask_q2 → diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:31:19,206] INFO - __main__ - main.py:8055 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] DIAGNOSTIC FALLBACK: Applying calculated progression only when AI fails to provide updates: diagnostic_probing_L5_eval_q1_ask_q2 → diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:31:19,207] WARNING - __main__ - main.py:8070 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🔍 DIAGNOSTIC FALLBACK STATE UPDATE:
[2025-06-17 14:31:19,208] WARNING - __main__ - main.py:8071 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🔍   - Phase: diagnostic_probing_L5_eval_q1_ask_q2 → diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:31:19,209] WARNING - __main__ - main.py:8072 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🔍   - Question Index: 0
[2025-06-17 14:31:19,209] WARNING - __main__ - main.py:8073 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🔍   - Probing Level: 5
[2025-06-17 14:31:19,210] INFO - __main__ - main.py:8077 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] DIAGNOSTIC FALLBACK: System provided fallback state updates when AI failed to respond
[2025-06-17 14:31:19,210] INFO - __main__ - main.py:8078 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] DIAGNOSTIC ENFORCEMENT: Original AI phase: diagnostic_probing_L5_eval_q1_ask_q2, Enforced phase: diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:31:19,211] INFO - __main__ - main.py:8098 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] [OK] State update block guaranteed by system enforcement
[2025-06-17 14:31:19,211] DEBUG - __main__ - main.py:8164 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] Phase unchanged: diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:31:19,212] INFO - __main__ - main.py:8180 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 📋 FINAL STATE UPDATE: {'new_phase': 'diagnostic_probing_L5_eval_q1_ask_q2', 'current_probing_level_number': 5, 'current_question_index': 0, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': 'That\'s even smarter! It\'s like the AI is really thinking about everything!  How it saves energy: If the AI can see when no one is on a street, it means we don\'t need those lights on full power. So, it can dim them or turn them off. This means the city doesn\'t use as much electricity. It\'s like turning off the lights in a room when no one is in it at home – it saves money on the electricity bill for the whole city! Less energy used means less pollution from making that electricity.  How it protects wildlife (like bats!): This is super important for animals that come out at night, like bats!  Bats use sounds (like echoes!) to find their way around and catch insects in the dark. Very bright streetlights can confuse them, make it hard for them to see their food, and mess up their "sound maps." If the lights are dimmer or off in quiet areas, it creates darker places for bats to fly and hunt naturally. They won\'t be scared away or get lost because of too much light. It helps them live their normal night lives without bright lights bothering them. So, the AI helps us be kind to both the planet\'s energy and the animals!', 'q2': "Yes I'm ready"}, 'levels_probed_and_failed': []}
[2025-06-17 14:31:19,213] INFO - __main__ - main.py:8216 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] ✅ Clean response prepared for user, state updates handled internally
[2025-06-17 14:31:19,214] INFO - __main__ - main.py:8227 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🔧 FORCED personalization injection
[2025-06-17 14:31:19,214] INFO - __main__ - main.py:8238 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] [OK] State updates processed: {'new_phase': 'diagnostic_probing_L5_eval_q1_ask_q2', 'current_probing_level_number': 5, 'current_question_index': 0, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': 'That\'s even smarter! It\'s like the AI is really thinking about everything!  How it saves energy: If the AI can see when no one is on a street, it means we don\'t need those lights on full power. So, it can dim them or turn them off. This means the city doesn\'t use as much electricity. It\'s like turning off the lights in a room when no one is in it at home – it saves money on the electricity bill for the whole city! Less energy used means less pollution from making that electricity.  How it protects wildlife (like bats!): This is super important for animals that come out at night, like bats!  Bats use sounds (like echoes!) to find their way around and catch insects in the dark. Very bright streetlights can confuse them, make it hard for them to see their food, and mess up their "sound maps." If the lights are dimmer or off in quiet areas, it creates darker places for bats to fly and hunt naturally. They won\'t be scared away or get lost because of too much light. It helps them live their normal night lives without bright lights bothering them. So, the AI helps us be kind to both the planet\'s energy and the animals!', 'q2': "Yes I'm ready"}, 'levels_probed_and_failed': []}
[2025-06-17 14:31:19,215] INFO - __main__ - main.py:8241 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] SIMPLIFIED: Diagnostic enforcement disabled to prevent backward transitions
[2025-06-17 14:31:19,215] WARNING - __main__ - main.py:8262 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🔄 STATE UPDATE PROCESSING:
[2025-06-17 14:31:19,216] WARNING - __main__ - main.py:8263 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🔄   - AI provided state update: True
[2025-06-17 14:31:19,216] WARNING - __main__ - main.py:8265 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🔄   - AI proposed phase: 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 14:31:19,217] WARNING - __main__ - main.py:8266 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🔄   - Current lesson phase: 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 14:31:19,217] WARNING - __main__ - main.py:8267 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🔄   - Python calculated phase: 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 14:31:19,217] INFO - __main__ - main.py:8275 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] ⚡ PERFORMANCE METRICS:
[2025-06-17 14:31:19,218] INFO - __main__ - main.py:8276 -   - Total execution time: 1.486s
[2025-06-17 14:31:19,218] WARNING - __main__ - main.py:5643 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🤖 AI RESPONSE RECEIVED:
[2025-06-17 14:31:19,219] WARNING - __main__ - main.py:5644 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🤖   - Content length: 268 chars
[2025-06-17 14:31:19,219] WARNING - __main__ - main.py:5645 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🤖   - State updates: {'new_phase': 'diagnostic_probing_L5_eval_q1_ask_q2', 'current_probing_level_number': 5, 'current_question_index': 0, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': 'That\'s even smarter! It\'s like the AI is really thinking about everything!  How it saves energy: If the AI can see when no one is on a street, it means we don\'t need those lights on full power. So, it can dim them or turn them off. This means the city doesn\'t use as much electricity. It\'s like turning off the lights in a room when no one is in it at home – it saves money on the electricity bill for the whole city! Less energy used means less pollution from making that electricity.  How it protects wildlife (like bats!): This is super important for animals that come out at night, like bats!  Bats use sounds (like echoes!) to find their way around and catch insects in the dark. Very bright streetlights can confuse them, make it hard for them to see their food, and mess up their "sound maps." If the lights are dimmer or off in quiet areas, it creates darker places for bats to fly and hunt naturally. They won\'t be scared away or get lost because of too much light. It helps them live their normal night lives without bright lights bothering them. So, the AI helps us be kind to both the planet\'s energy and the animals!', 'q2': "Yes I'm ready"}, 'levels_probed_and_failed': []}
[2025-06-17 14:31:19,219] WARNING - __main__ - main.py:5646 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🤖   - Raw state block: None...
[2025-06-17 14:31:19,221] INFO - __main__ - main.py:5662 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
[2025-06-17 14:31:19,222] INFO - __main__ - main.py:5663 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] CURRENT PHASE DETERMINATION: AI=diagnostic_probing_L5_eval_q1_ask_q2, Session=diagnostic_probing_L5_eval_q1_ask_q2, Final=diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:31:19,493] INFO - __main__ - main.py:5772 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] AI processing completed in 1.76s
[2025-06-17 14:31:19,494] WARNING - __main__ - main.py:5783 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🔍 STATE UPDATE VALIDATION: current_phase='diagnostic_probing_L5_eval_q1_ask_q2', new_phase='diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 14:31:19,495] INFO - __main__ - main.py:3897 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] AI state update validation passed: diagnostic_probing_L5_eval_q1_ask_q2 → diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:31:19,496] WARNING - __main__ - main.py:5792 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] ✅ STATE UPDATE VALIDATION PASSED
[2025-06-17 14:31:19,497] WARNING - __main__ - main.py:5799 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] � PHASE MAINTAINED: diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:31:19,498] WARNING - __main__ - main.py:5806 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🔍 COMPLETE PHASE FLOW DEBUG:
[2025-06-17 14:31:19,499] WARNING - __main__ - main.py:5807 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🔍   1. Input phase: 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 14:31:19,500] WARNING - __main__ - main.py:5808 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🔍   2. Python calculated next phase: 'NOT_FOUND'
[2025-06-17 14:31:19,502] WARNING - __main__ - main.py:5809 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🔍   3. AI state updates: {'new_phase': 'diagnostic_probing_L5_eval_q1_ask_q2', 'current_probing_level_number': 5, 'current_question_index': 0, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': 'That\'s even smarter! It\'s like the AI is really thinking about everything!  How it saves energy: If the AI can see when no one is on a street, it means we don\'t need those lights on full power. So, it can dim them or turn them off. This means the city doesn\'t use as much electricity. It\'s like turning off the lights in a room when no one is in it at home – it saves money on the electricity bill for the whole city! Less energy used means less pollution from making that electricity.  How it protects wildlife (like bats!): This is super important for animals that come out at night, like bats!  Bats use sounds (like echoes!) to find their way around and catch insects in the dark. Very bright streetlights can confuse them, make it hard for them to see their food, and mess up their "sound maps." If the lights are dimmer or off in quiet areas, it creates darker places for bats to fly and hunt naturally. They won\'t be scared away or get lost because of too much light. It helps them live their normal night lives without bright lights bothering them. So, the AI helps us be kind to both the planet\'s energy and the animals!', 'q2': "Yes I'm ready"}, 'levels_probed_and_failed': []}
[2025-06-17 14:31:19,504] WARNING - __main__ - main.py:5810 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🔍   4. Final phase to save: 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 14:31:19,505] WARNING - __main__ - main.py:5813 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 💾 FINAL STATE APPLICATION:
[2025-06-17 14:31:19,506] WARNING - __main__ - main.py:5814 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 💾   - Current phase input: 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 14:31:19,507] WARNING - __main__ - main.py:5815 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 💾   - State updates from AI: {'new_phase': 'diagnostic_probing_L5_eval_q1_ask_q2', 'current_probing_level_number': 5, 'current_question_index': 0, 'diagnostic_progression_applied': True, 'diagnostic_progression_reason': 'Fallback - AI did not provide state updates', 'student_answers_for_probing_level': {'q1': 'That\'s even smarter! It\'s like the AI is really thinking about everything!  How it saves energy: If the AI can see when no one is on a street, it means we don\'t need those lights on full power. So, it can dim them or turn them off. This means the city doesn\'t use as much electricity. It\'s like turning off the lights in a room when no one is in it at home – it saves money on the electricity bill for the whole city! Less energy used means less pollution from making that electricity.  How it protects wildlife (like bats!): This is super important for animals that come out at night, like bats!  Bats use sounds (like echoes!) to find their way around and catch insects in the dark. Very bright streetlights can confuse them, make it hard for them to see their food, and mess up their "sound maps." If the lights are dimmer or off in quiet areas, it creates darker places for bats to fly and hunt naturally. They won\'t be scared away or get lost because of too much light. It helps them live their normal night lives without bright lights bothering them. So, the AI helps us be kind to both the planet\'s energy and the animals!', 'q2': "Yes I'm ready"}, 'levels_probed_and_failed': []}
[2025-06-17 14:31:19,508] WARNING - __main__ - main.py:5816 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 💾   - Final phase to save: 'diagnostic_probing_L5_eval_q1_ask_q2'
[2025-06-17 14:31:19,509] WARNING - __main__ - main.py:5817 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 💾   - Phase change: False
[2025-06-17 14:31:19,510] INFO - __main__ - main.py:3929 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] DIAGNOSTIC_FLOW_METRICS:
[2025-06-17 14:31:19,510] INFO - __main__ - main.py:3930 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18]   Phase transition: diagnostic_probing_L5_eval_q1_ask_q2 -> diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:31:19,510] INFO - __main__ - main.py:3931 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18]   Current level: 5
[2025-06-17 14:31:19,511] INFO - __main__ - main.py:3932 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18]   Question index: 1
[2025-06-17 14:31:19,511] INFO - __main__ - main.py:3933 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18]   First encounter: True
[2025-06-17 14:31:19,511] INFO - __main__ - main.py:3938 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18]   Answers collected: 2
[2025-06-17 14:31:19,512] INFO - __main__ - main.py:3939 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18]   Levels failed: 0
[2025-06-17 14:31:19,512] INFO - __main__ - main.py:3897 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] AI state update validation passed: diagnostic_probing_L5_eval_q1_ask_q2 → diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:31:19,512] INFO - __main__ - main.py:3943 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18]   State update valid: True
[2025-06-17 14:31:19,513] INFO - __main__ - main.py:3950 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18]   Diagnostic complete: False
[2025-06-17 14:31:19,513] WARNING - __main__ - main.py:5829 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 0, current_q_index_for_prompt = NOT_FOUND
[2025-06-17 14:31:20,634] WARNING - __main__ - main.py:5856 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] ✅ SESSION STATE SAVED TO FIRESTORE:
[2025-06-17 14:31:20,636] WARNING - __main__ - main.py:5857 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] ✅   - Phase: diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:31:20,637] WARNING - __main__ - main.py:5858 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] ✅   - Probing Level: 5
[2025-06-17 14:31:20,639] WARNING - __main__ - main.py:5859 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] ✅   - Question Index: 0
[2025-06-17 14:31:20,640] WARNING - __main__ - main.py:5860 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] ✅   - Diagnostic Complete: False
[2025-06-17 14:31:22,166] INFO - __main__ - main.py:5919 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] ✅ Updated existing session document: session_b71023f5-62ee-482e-be3a-783fab2fb64c
[2025-06-17 14:31:22,168] WARNING - __main__ - main.py:5920 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] ✅ SESSION UPDATE COMPLETE:
[2025-06-17 14:31:22,169] WARNING - __main__ - main.py:5921 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] ✅   - Session ID: session_b71023f5-62ee-482e-be3a-783fab2fb64c
[2025-06-17 14:31:22,170] WARNING - __main__ - main.py:5922 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] ✅   - Phase transition: diagnostic_probing_L5_eval_q1_ask_q2 → diagnostic_probing_L5_eval_q1_ask_q2
[2025-06-17 14:31:22,171] WARNING - __main__ - main.py:5923 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] ✅   - Interaction logged successfully
[2025-06-17 14:31:22,173] INFO - __main__ - main.py:9173 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
[2025-06-17 14:31:22,174] DEBUG - __main__ - main.py:2753 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] FINAL_ASSESSMENT_BLOCK not found in AI response.
[2025-06-17 14:31:22,176] DEBUG - __main__ - main.py:5969 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] No final assessment data found in AI response
[2025-06-17 14:31:22,178] INFO - __main__ - main.py:6034 - [1c2d15e7-e385-4f65-95f6-2dbe99150d18] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
[2025-06-17 14:31:22,180] WARNING - __main__ - main.py:625 - High response time detected: 6.63s for enhance_content_api
[2025-06-17 14:31:22,181] INFO - werkzeug - _internal.py:97 - 127.0.0.1 - - [17/Jun/2025 14:31:22] "POST /api/enhance-content HTTP/1.1" 200 -
