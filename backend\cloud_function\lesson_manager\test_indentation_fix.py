#!/usr/bin/env python3
"""
Test script to check if main.py can be imported and started
"""
import sys
import os

# Set environment variables
os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = 'solynta-academy-firebase-adminsdk-fbsvc-4569c5d419.json'
os.environ['USE_TEMP_AUTH_BYPASS'] = 'true'
os.environ['FLASK_DEBUG'] = '1'

print("🔧 Testing main.py import after indentation fix...")

try:
    # Try to import main
    import main
    print("✅ main.py imported successfully!")
    
    # Check if the app was created
    if hasattr(main, 'app'):
        print("✅ Flask app found!")
        print(f"   App name: {main.app.name}")
        
        # Try to get app context
        with main.app.app_context():
            print("✅ App context created successfully!")
            
        print("🎉 All tests passed! The indentation error has been fixed.")
        
    else:
        print("⚠️ Flask app not found in main module")
        
except IndentationError as e:
    print(f"❌ IndentationError still present: {e}")
    sys.exit(1)
except SyntaxError as e:
    print(f"❌ SyntaxError found: {e}")
    sys.exit(1)
except Exception as e:
    print(f"⚠️ Other error during import: {e}")
    print("This might be a runtime error, not a syntax issue.")

print("✅ main.py syntax and indentation are correct!")
