# PowerShell script to start Solynta Backend with Windows compatibility
Write-Host "============================================" -ForegroundColor Green
Write-Host "SOLYNTA BACKEND - WINDOWS SERVER STARTUP" -ForegroundColor Green
Write-Host "============================================" -ForegroundColor Green

# Clear potentially problematic environment variables
$env:WERKZEUG_RUN_MAIN = $null
$env:WERKZEUG_SERVER_FD = $null
$env:FLASK_RUN_FROM_CLI = $null

# Set required environment variables
$env:FLASK_ENV = "development"
$env:GEMINI_API_KEY = "test-key-for-startup"
$env:PORT = "5000"

Write-Host "Environment cleared and configured for Windows" -ForegroundColor Yellow
Write-Host "Starting Solynta Backend Server..." -ForegroundColor Cyan
Write-Host ""

# Change to script directory
Set-Location -Path $PSScriptRoot

# Start the server
try {
    python main.py
} catch {
    Write-Host "Error starting server: $_" -ForegroundColor Red
}

Write-Host ""
Write-Host "Server stopped." -ForegroundColor Yellow
Read-Host "Press Enter to continue"
