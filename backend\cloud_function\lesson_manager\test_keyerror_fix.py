import requests
import json
import time

def test_keyerror_fix():
    """Test that the KeyError in template formatting is fixed"""
    
    print("🔧 Testing KeyError fix for template placeholders...")
    
    url = "http://localhost:5000/api/enhance-content"
    
    payload = {
        "student_id": "test_keyerror_fix",
        "lesson_ref": "P5-MAT-002",
        "content_to_enhance": "I completed all 5 questions",
        "country": "Nigeria",
        "curriculum": "National Curriculum",
        "grade": "primary-5",
        "level": "P5",
        "subject": "Mathematics",
        "session_id": f"test_keyerror_{int(time.time())}",
        "chat_history": []
    }
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer test-token-keyerror-fix"
    }
    
    try:
        print("📤 Sending request to test template formatting...")
        response = requests.post(url, json=payload, headers=headers, timeout=20)
        
        print(f"📥 Status: {response.status_code}")
        
        # Check for the specific KeyError
        if "KeyError: 'max(1, current_probing_level_number-1)'" in response.text:
            print("❌ KEYERROR STILL PRESENT!")
        elif "KeyError" in response.text and "max(" in response.text:
            print("⚠️ DIFFERENT KeyError with max() function found")
        elif "Rule Placeholder Mismatch" in response.text:
            print("⚠️ Template placeholder error still present")
        else:
            print("✅ NO TEMPLATE KEYERROR DETECTED!")
            
        if response.status_code == 200:
            print("✅ Request completed successfully!")
            try:
                resp_data = response.json()
                if isinstance(resp_data, dict) and 'success' in resp_data:
                    print(f"   Success: {resp_data['success']}")
            except:
                pass
        else:
            print(f"Response preview: {response.text[:300]}...")
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect - server may not be running")
    except Exception as e:
        print(f"❌ Request error: {e}")

if __name__ == "__main__":
    test_keyerror_fix()
