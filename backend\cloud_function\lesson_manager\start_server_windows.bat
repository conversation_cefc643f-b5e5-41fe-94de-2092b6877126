@echo off
echo ============================================
echo SOLYNTA BACKEND - WINDOWS SERVER STARTUP
echo ============================================

REM Clear potentially problematic environment variables
set WERKZEUG_RUN_MAIN=
set WERKZEUG_SERVER_FD=
set FLASK_RUN_FROM_CLI=

REM Set required environment variables
set FLASK_ENV=development
set GEMINI_API_KEY=test-key-for-startup
set PORT=5000

echo Starting Solynta Backend Server...
echo Environment cleared and configured for Windows
echo.

cd /d "%~dp0"
python main.py

echo.
echo Server stopped.
pause
