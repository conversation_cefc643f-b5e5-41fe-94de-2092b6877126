#!/usr/bin/env python3
"""
Test script to verify the teaching phase protection fix.
This tests the specific issue where teaching phases were being overridden.
"""

import json
import requests
import time

def test_teaching_phase_protection():
    """Test that teaching phases are protected from being overridden."""
    
    print("🧪 TESTING TEACHING PHASE PROTECTION FIX")
    print("=" * 50)
    
    # Test URL
    url = "http://localhost:5001/api/enhance-content"
    
    # First, let's simulate a session that should be in teaching phase
    # We'll use a session with diagnostic already completed
    test_payload = {
        "sessionId": "test_teaching_protection_001",
        "content": "I'm ready to continue learning",
        "moduleSlug": "fractions-introduction", 
        "subjectSlug": "mathematics",
        "studentName": "TestStudent",
        "gradeLevel": "5",
        "country": "US"
    }
    
    print(f"🚀 Sending test request to: {url}")
    print(f"📝 Payload: {json.dumps(test_payload, indent=2)}")
    
    try:
        # Send the request
        response = requests.post(url, json=test_payload, timeout=30)
        
        print(f"\n📊 RESPONSE STATUS: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            print(f"✅ SUCCESS: {response_data.get('success', 'unknown')}")
            print(f"📄 Message: {response_data.get('message', 'No message')}")
            
            # Check for teaching phase in response
            enhanced_content = response_data.get('data', {}).get('enhanced_content', '')
            session_data = response_data.get('data', {}).get('session_data', {})
            current_phase = session_data.get('current_phase', 'unknown')
            
            print(f"\n🎯 PHASE ANALYSIS:")
            print(f"   Current Phase: {current_phase}")
            print(f"   Is Teaching Phase: {current_phase.startswith('teaching_')}")
            
            # Look for diagnostic override logs in the response (shouldn't happen)
            if 'diagnostic_start_probe' in enhanced_content:
                print("❌ ERROR: Found diagnostic phase content when should be in teaching!")
                return False
            elif current_phase.startswith('teaching_'):
                print("✅ SUCCESS: Correctly in teaching phase")
                return True
            else:
                print(f"⚠️  WARNING: Unexpected phase: {current_phase}")
                return False
        else:
            print(f"❌ ERROR: HTTP {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ CONNECTION ERROR: {e}")
        return False
    except Exception as e:
        print(f"❌ UNEXPECTED ERROR: {e}")
        return False

def main():
    """Main test function."""
    print("Starting Teaching Phase Protection Test...")
    
    # Wait a moment for server to be ready
    print("⏳ Waiting for server to be ready...")
    time.sleep(2)
    
    # Run the test
    success = test_teaching_phase_protection()
    
    if success:
        print("\n🎉 TEACHING PHASE PROTECTION TEST PASSED!")
    else:
        print("\n💥 TEACHING PHASE PROTECTION TEST FAILED!")
    
    return success

if __name__ == "__main__":
    main()
