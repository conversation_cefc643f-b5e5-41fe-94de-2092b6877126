#!/usr/bin/env python3
"""
Simple test to check if main.py can be imported successfully
"""

import sys
import os

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    print("Testing main.py import...")
    import main
    print("✓ main.py imported successfully!")
    
    print("Testing Flask app creation...")
    if hasattr(main, 'app'):
        print("✓ Flask app found!")
        print(f"App name: {main.app.name}")
    else:
        print("✗ Flask app not found")
    
    print("Testing logger...")
    if hasattr(main, 'logger'):
        print("✓ Logger found!")
    else:
        print("✗ Logger not found")
        
    print("Testing database connection...")
    if hasattr(main, 'db') and main.db is not None:
        print("✓ Database connection found!")
    else:
        print("✗ Database connection not found")
        
except ImportError as e:
    print(f"✗ Import error: {e}")
    import traceback
    traceback.print_exc()
except Exception as e:
    print(f"✗ Other error: {e}")
    import traceback
    traceback.print_exc()

print("Import test completed.")
