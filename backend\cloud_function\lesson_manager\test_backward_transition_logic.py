#!/usr/bin/env python3
"""
Test script to verify the backward transition bug fix by testing the core logic directly.
This bypasses authentication and tests the enhance_lesson_content function directly.
"""

import sys
import os

# Add the current directory to Python path to import main module
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def is_forward_transition_valid(current_phase: str, proposed_phase: str) -> bool:
    """
    Validate that a phase transition is forward-only based on lesson progression hierarchy.
    This is the same function as in main.py for testing consistency.
    """
    # Define phase hierarchy levels (lower number = earlier phase)
    phase_hierarchy = {
        'diagnostic_start_probe': 1,
        'diagnostic_probing_L5_ask_q1': 2,
        'diagnostic_probing_L5_eval_q1_ask_q2': 3,
        'diagnostic_probing_L5_eval_q2_ask_q3': 4,
        'diagnostic_probing_L5_eval_q3_ask_q4': 5,
        'diagnostic_probing_L5_eval_q4_ask_q5': 6,
        'diagnostic_probing_L5_eval_q5_decide_level': 7,
        'teaching_start_level_1': 8, 'teaching_start_level_2': 8, 'teaching_start_level_3': 8,
        'teaching_start_level_4': 8, 'teaching_start_level_5': 8, 'teaching_start_level_6': 8,
        'teaching_start_level_7': 8, 'teaching_start_level_8': 8, 'teaching_start_level_9': 8,
        'teaching_start_level_10': 8,  # All teaching start levels are equivalent
        'teaching': 9,
        'teaching_practice': 10,
        'quiz_initiate': 11,
        'quiz_questions': 12,
        'quiz_results': 13,
        'conclusion_summary': 14,
        'final_assessment_pending': 15,
        'completed': 16
    }

    # Handle dynamic diagnostic phases (different levels)
    if current_phase.startswith('diagnostic_probing_L') and current_phase not in phase_hierarchy:
        # Extract level and question info for dynamic diagnostic phases
        if 'ask_q1' in current_phase:
            current_level = 2
        elif 'eval_q1_ask_q2' in current_phase:
            current_level = 3
        elif 'eval_q2_ask_q3' in current_phase:
            current_level = 4
        elif 'eval_q3_ask_q4' in current_phase:
            current_level = 5
        elif 'eval_q4_ask_q5' in current_phase:
            current_level = 6
        elif 'eval_q5_decide_level' in current_phase:
            current_level = 7
        else:
            current_level = 2  # Default to early diagnostic
    else:
        current_level = phase_hierarchy.get(current_phase, 0)

    # Handle dynamic proposed phases
    if proposed_phase.startswith('diagnostic_probing_L') and proposed_phase not in phase_hierarchy:
        if 'ask_q1' in proposed_phase:
            proposed_level = 2
        elif 'eval_q1_ask_q2' in proposed_phase:
            proposed_level = 3
        elif 'eval_q2_ask_q3' in proposed_phase:
            proposed_level = 4
        elif 'eval_q3_ask_q4' in proposed_phase:
            proposed_level = 5
        elif 'eval_q4_ask_q5' in proposed_phase:
            proposed_level = 6
        elif 'eval_q5_decide_level' in proposed_phase:
            proposed_level = 7
        else:
            proposed_level = 2  # Default to early diagnostic
    elif proposed_phase.startswith('teaching_start_level_'):
        proposed_level = 8  # All teaching start levels are equivalent
    else:
        proposed_level = phase_hierarchy.get(proposed_phase, 0)

    # Allow transitions to same level or higher level (forward progression)
    is_forward = proposed_level >= current_level

    # Special case: Allow staying in the same phase (no progression)
    if current_phase == proposed_phase:
        is_forward = True

    return is_forward

def test_backward_transition_prevention_logic():
    """Test the core logic that prevents backward transitions."""
    
    print("🔍 Testing Backward Transition Prevention Logic...")
    print("=" * 60)
    
    # Test scenario: Teaching phase with diagnostic calculated phase
    lesson_phase_from_context = "teaching"
    python_calculated_new_phase_for_block = "diagnostic_probing_L5_ask_q1"
    state_updates = {}
    diagnostic_should_be_complete = False
    
    print(f"📋 Test Scenario:")
    print(f"   Current phase: {lesson_phase_from_context}")
    print(f"   Calculated phase: {python_calculated_new_phase_for_block}")
    print(f"   State updates: {state_updates}")
    
    # Simulate the logic from main.py lines 7971-8003
    should_apply_calculated_progression = False
    
    # CRITICAL FIX: Only apply diagnostic progression when CURRENTLY in diagnostic phase
    # This prevents backward transitions from teaching phases to diagnostic phases
    if ('diagnostic' in lesson_phase_from_context and
        'diagnostic' in python_calculated_new_phase_for_block and
        python_calculated_new_phase_for_block is not None and
        not diagnostic_should_be_complete):

        # Scenario 1: AI provided no state updates
        if not state_updates or not state_updates.get('new_phase'):
            should_apply_calculated_progression = True
            print(f"   ✅ DIAGNOSTIC PROGRESSION: AI provided no state updates - applying calculated progression")

        # Scenario 2: AI provided same phase (stuck)
        elif state_updates.get('new_phase') == lesson_phase_from_context:
            should_apply_calculated_progression = True
            print(f"   ✅ DIAGNOSTIC PROGRESSION: AI stuck in same phase - applying calculated fallback")

        # Scenario 3: AI provided different phase - trust it
        else:
            print(f"   ✅ DIAGNOSTIC PROGRESSION: AI provided phase transition - trusting AI")
    
    # CRITICAL: Block diagnostic progression when in non-diagnostic phases
    elif 'diagnostic' not in lesson_phase_from_context and 'diagnostic' in python_calculated_new_phase_for_block:
        print(f"   🚫 BACKWARD TRANSITION BLOCKED: Cannot apply diagnostic progression from non-diagnostic phase")
        print(f"      Current phase: {lesson_phase_from_context}")
        print(f"      Calculated phase: {python_calculated_new_phase_for_block}")
        print(f"      This would cause a backward transition - resetting to current phase")
        python_calculated_new_phase_for_block = lesson_phase_from_context
        should_apply_calculated_progression = False
        
        # This is the expected behavior - the fix should block this
        print(f"   ✅ FIX WORKING: Backward transition was blocked!")
        return True
    
    # Check if the fallback logic would be applied
    if should_apply_calculated_progression:
        print(f"   ❌ FALLBACK LOGIC APPLIED: This would cause a backward transition!")
        print(f"      From: {lesson_phase_from_context}")
        print(f"      To: {python_calculated_new_phase_for_block}")
        print(f"      Reason: Fallback - AI did not provide state updates")
        return False
    else:
        print(f"   ✅ NO FALLBACK APPLIED: Staying in current phase")
        return True

def test_forward_transition_allowed():
    """Test that forward transitions are still allowed."""
    
    print("\n🔍 Testing Forward Transition (Should Be Allowed)...")
    print("=" * 60)
    
    # Test scenario: Diagnostic phase with diagnostic progression
    lesson_phase_from_context = "diagnostic_probing_L5_ask_q1"
    python_calculated_new_phase_for_block = "diagnostic_probing_L5_eval_q1_ask_q2"
    state_updates = {}
    diagnostic_should_be_complete = False
    
    print(f"📋 Test Scenario:")
    print(f"   Current phase: {lesson_phase_from_context}")
    print(f"   Calculated phase: {python_calculated_new_phase_for_block}")
    print(f"   State updates: {state_updates}")
    
    # Simulate the logic
    should_apply_calculated_progression = False
    
    # This should be allowed (diagnostic to diagnostic progression)
    if ('diagnostic' in lesson_phase_from_context and
        'diagnostic' in python_calculated_new_phase_for_block and
        python_calculated_new_phase_for_block is not None and
        not diagnostic_should_be_complete):

        # Scenario 1: AI provided no state updates
        if not state_updates or not state_updates.get('new_phase'):
            should_apply_calculated_progression = True
            print(f"   ✅ DIAGNOSTIC PROGRESSION: AI provided no state updates - applying calculated progression")
            print(f"   ✅ FORWARD TRANSITION ALLOWED: {lesson_phase_from_context} → {python_calculated_new_phase_for_block}")
            return True
    
    # This should not trigger for forward transitions
    elif 'diagnostic' not in lesson_phase_from_context and 'diagnostic' in python_calculated_new_phase_for_block:
        print(f"   ❌ UNEXPECTED: Forward transition was blocked!")
        return False
    
    print(f"   ✅ FORWARD TRANSITION PROCESSED CORRECTLY")
    return True

def test_complete_phase_progression():
    """Test that all 9 phases can progress correctly without backward transitions."""

    print("\n🔍 Testing Complete 9-Phase Progression...")
    print("=" * 60)

    # Define the expected 9-phase progression
    expected_phases = [
        'diagnostic_start_probe',
        'diagnostic_probing_L5_ask_q1',
        'diagnostic_probing_L5_eval_q1_ask_q2',
        'diagnostic_probing_L5_eval_q2_ask_q3',
        'diagnostic_probing_L5_eval_q3_ask_q4',
        'diagnostic_probing_L5_eval_q4_ask_q5',
        'diagnostic_probing_L5_eval_q5_decide_level',
        'teaching_start_level_5',
        'teaching',
        'quiz_initiate',
        'quiz_questions',
        'quiz_results',
        'conclusion_summary',
        'final_assessment_pending',
        'completed'
    ]

    print(f"📋 Testing progression through {len(expected_phases)} phases...")

    # Test transitions between consecutive phases
    all_transitions_valid = True
    backward_transitions_detected = []

    for i in range(len(expected_phases) - 1):
        current_phase = expected_phases[i]
        next_phase = expected_phases[i + 1]

        print(f"\n   Phase {i+1} → {i+2}: {current_phase} → {next_phase}")

        # Test the transition logic
        lesson_phase_from_context = current_phase
        python_calculated_new_phase_for_block = next_phase
        state_updates = {}
        diagnostic_should_be_complete = False

        # Apply the same logic as in main.py
        should_apply_calculated_progression = False

        # Use the same validation logic as the backend
        is_valid_transition = is_forward_transition_valid(current_phase, next_phase)

        if not is_valid_transition:
            print(f"      🚫 BACKWARD TRANSITION DETECTED: {current_phase} → {next_phase}")
            backward_transitions_detected.append((current_phase, next_phase))
            all_transitions_valid = False
        else:
            print(f"      ✅ FORWARD TRANSITION: Valid progression")

    # Test specific problematic transitions that should be blocked
    problematic_transitions = [
        ('teaching', 'diagnostic_probing_L5_ask_q1'),
        ('teaching_start_level_5', 'diagnostic_start_probe'),
        ('quiz_initiate', 'diagnostic_probing_L5_eval_q1_ask_q2'),
        ('quiz_questions', 'teaching'),
        ('conclusion_summary', 'diagnostic_probing_L5_ask_q1'),
        ('completed', 'diagnostic_start_probe')
    ]

    print(f"\n📋 Testing {len(problematic_transitions)} problematic backward transitions...")
    backward_blocks_working = True

    for current_phase, calculated_phase in problematic_transitions:
        print(f"\n   Testing: {current_phase} → {calculated_phase}")

        # Use the same validation logic as the backend
        is_valid_transition = is_forward_transition_valid(current_phase, calculated_phase)

        if not is_valid_transition:
            print(f"      ✅ BLOCKED: Backward transition correctly prevented")
        else:
            print(f"      ❌ NOT BLOCKED: This should have been prevented!")
            backward_blocks_working = False

    return all_transitions_valid and backward_blocks_working, backward_transitions_detected

def test_phase_hierarchy_validation():
    """Test that phase hierarchy is correctly enforced."""

    print("\n🔍 Testing Phase Hierarchy Validation...")
    print("=" * 60)

    # Define phase hierarchy levels (lower number = earlier phase)
    phase_hierarchy = {
        'diagnostic_start_probe': 1,
        'diagnostic_probing_L5_ask_q1': 2,
        'diagnostic_probing_L5_eval_q1_ask_q2': 3,
        'diagnostic_probing_L5_eval_q2_ask_q3': 4,
        'diagnostic_probing_L5_eval_q3_ask_q4': 5,
        'diagnostic_probing_L5_eval_q4_ask_q5': 6,
        'diagnostic_probing_L5_eval_q5_decide_level': 7,
        'teaching_start_level_5': 8,
        'teaching': 9,
        'quiz_initiate': 10,
        'quiz_questions': 11,
        'quiz_results': 12,
        'conclusion_summary': 13,
        'final_assessment_pending': 14,
        'completed': 15
    }

    def is_forward_transition(from_phase, to_phase):
        """Check if transition is forward-only based on hierarchy."""
        from_level = phase_hierarchy.get(from_phase, 0)
        to_level = phase_hierarchy.get(to_phase, 0)
        return to_level >= from_level

    # Test various transition scenarios
    test_transitions = [
        # Valid forward transitions
        ('diagnostic_start_probe', 'diagnostic_probing_L5_ask_q1', True),
        ('teaching', 'quiz_initiate', True),
        ('quiz_questions', 'quiz_results', True),
        ('conclusion_summary', 'final_assessment_pending', True),
        ('final_assessment_pending', 'completed', True),

        # Invalid backward transitions
        ('teaching', 'diagnostic_start_probe', False),
        ('quiz_initiate', 'teaching', False),
        ('completed', 'quiz_questions', False),
        ('conclusion_summary', 'diagnostic_probing_L5_ask_q1', False),
        ('final_assessment_pending', 'teaching_start_level_5', False)
    ]

    hierarchy_validation_passed = True

    for from_phase, to_phase, should_be_valid in test_transitions:
        is_valid = is_forward_transition(from_phase, to_phase)

        if is_valid == should_be_valid:
            status = "✅ PASS"
        else:
            status = "❌ FAIL"
            hierarchy_validation_passed = False

        print(f"   {from_phase} → {to_phase}: {status}")
        print(f"      Expected: {'Valid' if should_be_valid else 'Invalid'}, Got: {'Valid' if is_valid else 'Invalid'}")

    return hierarchy_validation_passed

def main():
    """Main test function."""
    print("🧪 Comprehensive Phase Progression Test")
    print("=" * 80)

    # Test 1: Backward transition should be blocked
    print("TEST 1: Backward Transition Prevention")
    test1_success = test_backward_transition_prevention_logic()

    # Test 2: Forward transition should be allowed
    print("\nTEST 2: Forward Transition Allowance")
    test2_success = test_forward_transition_allowed()

    # Test 3: Complete 9-phase progression
    print("\nTEST 3: Complete Phase Progression")
    test3_success, backward_transitions = test_complete_phase_progression()

    # Test 4: Phase hierarchy validation
    print("\nTEST 4: Phase Hierarchy Validation")
    test4_success = test_phase_hierarchy_validation()

    print("\n" + "=" * 80)
    print("📊 COMPREHENSIVE TEST RESULTS:")
    print(f"   Test 1 (Block Backward): {'✅ PASS' if test1_success else '❌ FAIL'}")
    print(f"   Test 2 (Allow Forward): {'✅ PASS' if test2_success else '❌ FAIL'}")
    print(f"   Test 3 (9-Phase Progression): {'✅ PASS' if test3_success else '❌ FAIL'}")
    print(f"   Test 4 (Phase Hierarchy): {'✅ PASS' if test4_success else '❌ FAIL'}")

    if backward_transitions:
        print(f"\n⚠️  BACKWARD TRANSITIONS DETECTED:")
        for from_phase, to_phase in backward_transitions:
            print(f"      {from_phase} → {to_phase}")

    overall_success = test1_success and test2_success and test3_success and test4_success

    print("\n" + "=" * 80)
    if overall_success:
        print("✅ ALL TESTS PASSED: Complete phase progression system is working correctly!")
        print("   - Backward transitions are blocked")
        print("   - Forward transitions are allowed")
        print("   - All 9 phases can progress correctly")
        print("   - Phase hierarchy is properly enforced")
    else:
        print("❌ SOME TESTS FAILED: Phase progression system needs attention!")
        if not test1_success:
            print("   - Backward transition blocking is not working")
        if not test2_success:
            print("   - Forward transition allowance is not working")
        if not test3_success:
            print("   - 9-phase progression has issues")
        if not test4_success:
            print("   - Phase hierarchy validation failed")

    return 0 if overall_success else 1

if __name__ == "__main__":
    sys.exit(main())
