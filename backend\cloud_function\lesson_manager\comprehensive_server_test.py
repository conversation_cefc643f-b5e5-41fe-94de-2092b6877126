#!/usr/bin/env python3
"""
Comprehensive server startup test with multiple fallback methods
"""

import sys
import os
import time
import threading

# Setup environment
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Clear problematic environment variables
problematic_vars = ['WERKZEUG_RUN_MAIN', 'WERKZEUG_SERVER_FD', 'FLASK_RUN_FROM_CLI']
for var in problematic_vars:
    if var in os.environ:
        print(f"Clearing: {var}")
        del os.environ[var]

# Set required environment variables
os.environ['GEMINI_API_KEY'] = 'test-key-for-startup'
os.environ['FLASK_ENV'] = 'development'

def test_server_startup():
    """Test the actual main.py server startup"""
    print("=" * 60)
    print("COMPREHENSIVE SERVER STARTUP TEST")
    print("=" * 60)
    
    try:
        # Import main module
        print("Step 1: Importing main module...")
        import main
        print("✓ Main module imported successfully")
        
        # Test Flask app availability
        if hasattr(main, 'app'):
            print("✓ Flask app available")
        else:
            print("✗ Flask app not found")
            return False
        
        # Test with test client first
        print("Step 2: Testing endpoints with test client...")
        try:
            with main.app.test_client() as client:
                response = client.get('/api/health')
                print(f"✓ Health endpoint: {response.status_code}")
        except Exception as test_client_error:
            print(f"⚠ Test client error: {test_client_error}")
        
        # Test different server startup methods
        print("Step 3: Testing server startup methods...")
        
        # Method 1: Basic Flask server
        print("Method 1: Basic Flask development server")
        try:
            print("Starting Flask server (will run for 5 seconds)...")
            
            # Create a timer to stop the server
            def stop_server():
                time.sleep(5)
                print("\nStopping test server...")
                os._exit(0)
            
            timer = threading.Thread(target=stop_server, daemon=True)
            timer.start()
            
            main.app.run(
                host='127.0.0.1',
                port=5010,
                debug=False,
                use_reloader=False,
                threaded=True,
                passthrough_errors=True
            )
            
        except KeyboardInterrupt:
            print("Server stopped by user")
            return True
        except OSError as e:
            if "10038" in str(e):
                print(f"✗ Windows socket error (10038): {e}")
                print("This confirms the socket file descriptor issue")
                
                # Method 2: Try Waitress
                print("Method 2: Trying Waitress server...")
                try:
                    from waitress import serve
                    print("Starting Waitress server...")
                    
                    timer = threading.Thread(target=stop_server, daemon=True)
                    timer.start()
                    
                    serve(main.app, host='127.0.0.1', port=5011, threads=4)
                    
                except ImportError:
                    print("✗ Waitress not available")
                except Exception as waitress_error:
                    print(f"✗ Waitress error: {waitress_error}")
                
                return False
            else:
                print(f"✗ Other socket error: {e}")
                return False
        except Exception as e:
            print(f"✗ Server startup error: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    except Exception as e:
        print(f"✗ Test setup error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_server_startup()
    print(f"\nTest result: {'SUCCESS' if success else 'FAILED'}")
    print("=" * 60)
