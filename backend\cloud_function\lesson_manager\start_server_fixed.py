import os
import sys

# Set environment variables
os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = 'solynta-academy-firebase-adminsdk-fbsvc-4569c5d419.json'
os.environ['USE_TEMP_AUTH_BYPASS'] = 'true'
os.environ['FLASK_DEBUG'] = '1'

print("🚀 Starting Flask server with all fixes applied...")

try:
    import main
    print("✅ main.py imported successfully!")
    
    if hasattr(main, 'app'):
        print("✅ Flask app found!")
        print("🎉 Server is ready to start!")
        
        # Start the server
        main.app.run(debug=True, host='127.0.0.1', port=5000)
        
except Exception as e:
    print(f"❌ Error starting server: {e}")
    import traceback
    traceback.print_exc()
