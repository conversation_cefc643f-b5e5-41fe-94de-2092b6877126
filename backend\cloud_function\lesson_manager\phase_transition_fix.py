#!/usr/bin/env python3
"""
Comprehensive Phase Transition Fix for Lesson System
===================================================

This module implements fixes for the lesson system's phase transition logic to ensure
proper sequential progression through all 9 phases without backward transitions or
infinite loops.

Key Fixes:
1. Forward-only phase progression validation
2. Diagnostic completion logic fix (eval_q5_decide_level → teaching)
3. AI instructor state update block generation enforcement
4. Phase freezing prevention (especially "Thanks!" loop)
5. Proper diagnostic scoring with level adjustment
"""

import re
import json
import logging
from typing import Dict, List, Tuple, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class PhaseTransitionManager:
    """Manages phase transitions with strict validation and forward-only progression"""
    
    # Define the exact 9-phase sequence
    PHASE_SEQUENCE = [
        "diagnostic",
        "teaching_start", 
        "teaching",
        "quiz_initiate",
        "quiz_questions", 
        "quiz_results",
        "conclusion_summary",
        "final_assessment_pending",
        "completed"
    ]
    
    # Valid transitions (forward-only)
    VALID_TRANSITIONS = {
        "diagnostic": ["teaching_start", "diagnostic"],
        "teaching_start": ["teaching"],
        "teaching": ["quiz_initiate", "teaching"],
        "quiz_initiate": ["quiz_questions"],
        "quiz_questions": ["quiz_results", "quiz_questions"],
        "quiz_results": ["conclusion_summary"],
        "conclusion_summary": ["final_assessment_pending"],
        "final_assessment_pending": ["completed"],
        "completed": ["completed"]
    }
    
    def __init__(self):
        self.transition_history = []
        self.phase_stuck_count = {}
        
    def get_phase_index(self, phase: str) -> int:
        """Get the index of a phase in the sequence (for ordering validation)"""
        try:
            # Normalize phase name for comparison
            normalized_phase = self.normalize_phase_name(phase)
            
            for i, expected_phase in enumerate(self.PHASE_SEQUENCE):
                if normalized_phase in expected_phase or expected_phase in normalized_phase:
                    return i
            return -1
        except:
            return -1
    
    def normalize_phase_name(self, phase: str) -> str:
        """Normalize phase names for consistent comparison"""
        if not phase:
            return ""
        
        # Remove common prefixes and suffixes
        normalized = phase.lower().strip()
        normalized = re.sub(r'_level_\d+', '', normalized)  # Remove level numbers
        normalized = re.sub(r'_l\d+', '', normalized)       # Remove L1, L2, etc.
        normalized = re.sub(r'_q\d+', '', normalized)       # Remove question numbers
        normalized = re.sub(r'_ask_.*', '', normalized)     # Remove ask suffixes
        normalized = re.sub(r'_eval_.*', '', normalized)    # Remove eval suffixes
        normalized = re.sub(r'_decide_.*', '', normalized)  # Remove decide suffixes
        normalized = re.sub(r'_probing.*', '', normalized)  # Remove probing suffixes
        
        # Map common variations
        phase_mappings = {
            'teaching_start': 'teaching_start',
            'quiz_initiate': 'quiz_initiate',
            'quiz_questions': 'quiz_questions',
            'quiz_results': 'quiz_results',
            'conclusion_summary': 'conclusion_summary',
            'final_assessment_pending': 'final_assessment_pending',
            'summary': 'conclusion_summary',
            'assessment': 'final_assessment_pending',
            'complete': 'completed',
            'finished': 'completed'
        }
        
        return phase_mappings.get(normalized, normalized)
    
    def validate_transition(self, from_phase: str, to_phase: str) -> Tuple[bool, str]:
        """Validate if a phase transition is allowed (forward-only)"""
        try:
            from_normalized = self.normalize_phase_name(from_phase)
            to_normalized = self.normalize_phase_name(to_phase)
            
            # Check if transition is in valid transitions
            if from_normalized in self.VALID_TRANSITIONS:
                valid_next_phases = self.VALID_TRANSITIONS[from_normalized]
                if to_normalized in valid_next_phases:
                    return True, "Valid forward transition"
            
            # Check for backward transition (critical error)
            from_index = self.get_phase_index(from_normalized)
            to_index = self.get_phase_index(to_normalized)
            
            if from_index >= 0 and to_index >= 0:
                if to_index < from_index:
                    return False, f"Backward transition detected: {from_phase} → {to_phase} (index {from_index} → {to_index})"
                elif to_index > from_index + 1:
                    return False, f"Phase skip detected: {from_phase} → {to_phase} (skipped {to_index - from_index - 1} phases)"
            
            return False, f"Invalid transition: {from_phase} → {to_phase}"
            
        except Exception as e:
            logger.error(f"Error validating transition {from_phase} → {to_phase}: {e}")
            return False, f"Transition validation error: {e}"
    
    def detect_phase_stuck(self, current_phase: str, max_stuck_count: int = 5) -> bool:
        """Detect if a phase is stuck (repeated too many times)"""
        try:
            normalized_phase = self.normalize_phase_name(current_phase)
            
            # Increment stuck count
            self.phase_stuck_count[normalized_phase] = self.phase_stuck_count.get(normalized_phase, 0) + 1
            
            # Check if stuck
            if self.phase_stuck_count[normalized_phase] >= max_stuck_count:
                logger.warning(f"Phase stuck detected: {current_phase} repeated {self.phase_stuck_count[normalized_phase]} times")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error detecting phase stuck for {current_phase}: {e}")
            return False
    
    def force_next_phase(self, current_phase: str) -> str:
        """Force progression to the next logical phase"""
        try:
            normalized_phase = self.normalize_phase_name(current_phase)
            current_index = self.get_phase_index(normalized_phase)
            
            if current_index >= 0 and current_index < len(self.PHASE_SEQUENCE) - 1:
                next_phase = self.PHASE_SEQUENCE[current_index + 1]
                logger.info(f"Forcing phase progression: {current_phase} → {next_phase}")
                return next_phase
            elif current_index == len(self.PHASE_SEQUENCE) - 1:
                # Already at final phase
                return "completed"
            else:
                # Unknown phase, default to diagnostic
                logger.warning(f"Unknown phase {current_phase}, defaulting to diagnostic")
                return "diagnostic"
                
        except Exception as e:
            logger.error(f"Error forcing next phase for {current_phase}: {e}")
            return "diagnostic"

class DiagnosticCompletionFix:
    """Fixes the diagnostic completion logic and eval_q5_decide_level transition"""
    
    def __init__(self):
        self.diagnostic_answers = []
        self.evaluation_triggered = False
    
    def should_complete_diagnostic(self, student_answers: List[Dict], current_phase: str, ai_response: str, context: Dict = None) -> Tuple[bool, str, int]:
        """Determine if diagnostic should complete and transition to teaching"""
        try:
            num_answers = len(student_answers)
            
            # Primary completion criteria
            completion_reasons = []
            
            # 1. Exactly 5 questions completed
            if num_answers >= 5:
                completion_reasons.append(f"5 questions completed ({num_answers} answers)")
            
            # 2. In evaluation phase (eval_q5_decide_level)
            if 'eval_q5_decide_level' in current_phase.lower():
                completion_reasons.append("In evaluation phase (eval_q5_decide_level)")
            
            # 3. AI response indicates completion
            completion_indicators = [
                'ready to begin teaching',
                'start teaching',
                'move to teaching',
                'teaching phase',
                'assigned level',
                'level for teaching'
            ]
            
            if any(indicator in ai_response.lower() for indicator in completion_indicators):
                completion_reasons.append("AI response indicates teaching readiness")
            
            # 4. Detect "Thanks!" loop (critical fix)
            if self.detect_thanks_loop(ai_response, num_answers):
                completion_reasons.append("Thanks loop detected - forcing completion")
            
            # Determine completion
            should_complete = len(completion_reasons) > 0
            
            if should_complete:
                # Calculate level based on diagnostic scoring
                # Extract current level from context or phase name
                current_level = 5  # Default
                if 'current_probing_level_number' in context:
                    current_level = context.get('current_probing_level_number', 5)
                elif 'diagnostic_probing_L' in current_phase:
                    # Extract level from phase name like "diagnostic_probing_L5_eval_q3_ask_q4"
                    import re
                    level_match = re.search(r'diagnostic_probing_L(\d+)', current_phase)
                    if level_match:
                        current_level = int(level_match.group(1))

                assigned_level = self.calculate_diagnostic_level(student_answers, current_level)
                reason = "; ".join(completion_reasons)
                return True, reason, assigned_level
            
            return False, "Diagnostic not ready for completion", 1
            
        except Exception as e:
            logger.error(f"Error in diagnostic completion check: {e}")
            return False, f"Error: {e}", 1
    
    def detect_thanks_loop(self, ai_response: str, num_answers: int) -> bool:
        """Detect the problematic 'Thanks!' loop in diagnostic evaluation"""
        try:
            # Clean response for analysis
            clean_response = ai_response.strip().lower()
            
            # Thanks loop indicators
            thanks_patterns = [
                r'^thanks!?$',
                r'^thank you!?$',
                r'^thanks\s*[.!]*$',
                r'^thank you\s*[.!]*$'
            ]
            
            # Check if response is just "Thanks!" or similar
            is_thanks_only = any(re.match(pattern, clean_response) for pattern in thanks_patterns)
            
            # Additional check: short response with "thanks" and sufficient answers
            is_short_thanks = (
                len(clean_response) < 30 and
                'thanks' in clean_response and
                num_answers >= 3
            )
            
            if is_thanks_only or is_short_thanks:
                logger.warning(f"Thanks loop detected: '{ai_response.strip()}' with {num_answers} answers")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error detecting thanks loop: {e}")
            return False
    
    def calculate_diagnostic_level(self, student_answers: List[Dict], current_level: int = 5) -> int:
        """
        Calculate appropriate level based on diagnostic scoring logic.

        CRITICAL FIX: Updated to support full 1-10 level range with proper progression logic.

        Args:
            student_answers: List of student answer dictionaries
            current_level: Current diagnostic level being tested (default: 5)

        Returns:
            Assigned level (1-10) based on diagnostic performance
        """
        try:
            if not student_answers:
                return max(1, min(10, current_level))  # Return current level within bounds

            # Analyze answer quality (simplified scoring)
            correct_count = 0
            total_questions = len(student_answers)

            for answer in student_answers:
                answer_text = answer.get('answer', '').lower()

                # Simple quality indicators
                quality_indicators = [
                    'correct', 'right', 'yes', 'understand', 'know',
                    'fraction', 'numerator', 'denominator', 'equal',
                    'part', 'whole', 'divide', 'multiply'
                ]

                if any(indicator in answer_text for indicator in quality_indicators):
                    correct_count += 1

            # CRITICAL FIX: Apply diagnostic scoring rules with proper level progression (1-10)
            if total_questions >= 5:
                success_rate = correct_count / total_questions

                if success_rate >= 1.0:  # 5/5 correct = +1 level
                    assigned_level = min(10, current_level + 1)  # Move up one level, max 10
                elif success_rate >= 0.8:  # 4/5 correct = same level
                    assigned_level = current_level  # Stay at current level
                elif success_rate >= 0.4:  # 2-3/5 correct = same level
                    assigned_level = current_level  # Stay at current level
                else:  # 0-1/5 correct = -1 level
                    assigned_level = max(1, current_level - 1)  # Move down one level, min 1

                # Ensure level is within valid bounds
                return max(1, min(10, assigned_level))
            else:
                # Partial evaluation - estimate based on current performance
                if total_questions > 0:
                    partial_success_rate = correct_count / total_questions
                    if partial_success_rate >= 0.8:
                        # Good partial performance - stay at current level
                        return max(1, min(10, current_level))
                    elif partial_success_rate >= 0.5:
                        # Moderate performance - slight adjustment down
                        return max(1, min(10, current_level - 1))
                    else:
                        # Poor performance - move down
                        return max(1, min(10, current_level - 2))
                else:
                    # No answers - return current level
                    return max(1, min(10, current_level))

        except Exception as e:
            logger.error(f"Error calculating diagnostic level: {e}")
            return max(1, min(10, current_level if 'current_level' in locals() else 5))

class StateUpdateBlockEnforcer:
    """Enforces proper AI instructor state update block generation"""
    
    @staticmethod
    def extract_state_update_block(ai_response: str) -> Tuple[Dict, str]:
        """Extract state update block from AI response"""
        try:
            # Pattern to match state update blocks
            pattern = r'//\s*AI_STATE_UPDATE_BLOCK_START\s*(\{.*?\})\s*//\s*AI_STATE_UPDATE_BLOCK_END'
            match = re.search(pattern, ai_response, re.DOTALL)
            
            if match:
                json_str = match.group(1).strip()
                try:
                    state_updates = json.loads(json_str)
                    # Remove block from response text
                    clean_response = re.sub(pattern, '', ai_response, flags=re.DOTALL).strip()
                    return state_updates, clean_response
                except json.JSONDecodeError as e:
                    logger.error(f"Invalid JSON in state update block: {json_str}, error: {e}")
                    return {}, ai_response
            
            return {}, ai_response
            
        except Exception as e:
            logger.error(f"Error extracting state update block: {e}")
            return {}, ai_response
    
    @staticmethod
    def generate_mandatory_state_block(new_phase: str, additional_state: Dict = None) -> str:
        """Generate mandatory state update block in correct format"""
        try:
            state_data = {"new_phase": new_phase}
            if additional_state:
                state_data.update(additional_state)
            
            json_str = json.dumps(state_data, separators=(',', ':'))
            
            return f"""
// AI_STATE_UPDATE_BLOCK_START
{json_str}
// AI_STATE_UPDATE_BLOCK_END"""
            
        except Exception as e:
            logger.error(f"Error generating state block: {e}")
            return f"""
// AI_STATE_UPDATE_BLOCK_START
{{"new_phase": "{new_phase}"}}
// AI_STATE_UPDATE_BLOCK_END"""
    
    @staticmethod
    def enforce_state_update_block(ai_response: str, required_phase: str, additional_state: Dict = None) -> str:
        """Ensure AI response contains proper state update block"""
        try:
            # Extract existing state block
            existing_state, clean_response = StateUpdateBlockEnforcer.extract_state_update_block(ai_response)
            
            # Check if valid state block exists
            if existing_state and 'new_phase' in existing_state:
                # Valid block exists, return as-is
                return ai_response
            
            # Generate and append mandatory state block
            mandatory_block = StateUpdateBlockEnforcer.generate_mandatory_state_block(required_phase, additional_state)
            
            # Append to clean response
            enhanced_response = clean_response + mandatory_block
            
            logger.info(f"Enforced state update block for phase: {required_phase}")
            return enhanced_response
            
        except Exception as e:
            logger.error(f"Error enforcing state update block: {e}")
            # Fallback: append basic state block
            return ai_response + f"""

// AI_STATE_UPDATE_BLOCK_START
{{"new_phase": "{required_phase}"}}
// AI_STATE_UPDATE_BLOCK_END"""

def apply_phase_transition_fixes(current_phase: str, ai_response: str, student_answers: List[Dict],
                                context: Dict) -> Tuple[str, str, Dict]:
    """
    CRITICAL FIX: Allow diagnostic progression while preventing backward transitions

    Returns:
        Tuple[str, str, Dict]: (fixed_response, next_phase, state_updates)
    """
    try:
        # Extract existing state updates without forcing changes
        existing_state, clean_response = StateUpdateBlockEnforcer.extract_state_update_block(ai_response)

        # CRITICAL FIX: Don't default to current_phase - let the main logic handle progression
        proposed_next_phase = existing_state.get('new_phase')

        # If AI didn't provide a phase, check diagnostic_complete flag before fallback
        if not proposed_next_phase:
            diagnostic_complete = context.get('diagnostic_complete', False)
            if diagnostic_complete and 'teaching' in current_phase.lower():
                # Diagnostic is complete and we're in teaching - stay in current phase
                logger.warning(f"Phase transition fix: No AI state update but diagnostic complete - staying in {current_phase}")
                return clean_response, current_phase, {'new_phase': current_phase}
            else:
                logger.info(f"Phase transition fix: No AI state update - letting main logic handle progression from {current_phase}")
                return clean_response, current_phase, {}

        # CRITICAL FIX: Prevent backwards transition if diagnostic is complete
        diagnostic_complete = context.get('diagnostic_complete', False)
        if diagnostic_complete and 'diagnostic' in proposed_next_phase.lower() and 'teaching' in current_phase.lower():
            logger.warning(f"🚫 BLOCKED BACKWARD TRANSITION: {current_phase} → {proposed_next_phase} (diagnostic already complete)")
            # Stay in current phase or progress forward
            if current_phase == 'teaching':
                proposed_next_phase = 'quiz_initiate'  # Progress forward
                logger.info(f"🚀 FORWARD PROGRESSION: Advancing to {proposed_next_phase} instead")
            else:
                proposed_next_phase = current_phase  # Maintain teaching phase
                logger.info(f"🔒 PHASE MAINTAINED: Staying in {current_phase}")

            existing_state['new_phase'] = proposed_next_phase
            existing_state['backward_transition_blocked'] = True
            existing_state['blocked_reason'] = "Diagnostic already complete - preventing reversion"

        # CRITICAL FIX: Only apply diagnostic completion logic, no other overrides
        if 'diagnostic' in current_phase.lower() and len(student_answers) >= 5:
            # Only force completion if we have 5+ answers and are in eval_q5_decide_level
            if 'eval_q5_decide_level' in current_phase:
                # Calculate level based on diagnostic scoring
                substantial_answers = 0
                for answer in student_answers:
                    if isinstance(answer, dict):
                        answer_text = answer.get('answer', '')
                    else:
                        answer_text = str(answer)

                    if len(answer_text.strip()) > 10:
                        substantial_answers += 1

                # Apply diagnostic scoring logic
                current_level = context.get('current_probing_level_number_from_state', 5)
                if substantial_answers >= 5:  # 5/5 = +1 level
                    assigned_level = min(10, current_level + 1)
                elif substantial_answers >= 4:  # 4/5 = same level
                    assigned_level = current_level
                elif substantial_answers >= 2:  # 2-3/5 = same level
                    assigned_level = current_level
                else:  # 0-1/5 = -1 level
                    assigned_level = max(1, current_level - 1)

                proposed_next_phase = f"teaching_start_level_{assigned_level}"
                logger.info(f"Diagnostic completion triggered: 5 questions completed ({len(student_answers)} answers); In evaluation phase (eval_q5_decide_level), assigned level {assigned_level}")

                # Update state with diagnostic completion
                existing_state.update({
                    'new_phase': proposed_next_phase,
                    'assigned_level_for_teaching': assigned_level,
                    'diagnostic_completed_this_session': True,
                    'diagnostic_completion_reason': f"5 questions completed ({len(student_answers)} answers); In evaluation phase (eval_q5_decide_level)"
                })

        logger.info(f"Phase transition fix applied: {current_phase} → {proposed_next_phase}")
        return clean_response, proposed_next_phase, existing_state

    except Exception as e:
        logger.error(f"Error applying phase transition fixes: {e}")
        # Enhanced fallback: check diagnostic_complete flag
        diagnostic_complete = context.get('diagnostic_complete', False)
        if diagnostic_complete:
            if 'teaching' in current_phase.lower():
                # Progress forward from teaching to quiz
                fallback_phase = 'quiz_initiate'
                logger.info(f"🚀 EXCEPTION FALLBACK: Progressing from {current_phase} to {fallback_phase}")
                _, clean_fallback_response = StateUpdateBlockEnforcer.extract_state_update_block(ai_response)
                return clean_fallback_response, fallback_phase, {'new_phase': fallback_phase}
            else:
                # Stay in current phase
                logger.info(f"🔒 EXCEPTION FALLBACK: Staying in {current_phase}")
                _, clean_fallback_response = StateUpdateBlockEnforcer.extract_state_update_block(ai_response)
                return clean_fallback_response, current_phase, {'new_phase': current_phase}
        else:
            # Default fallback for diagnostic phases
            _, clean_fallback_response = StateUpdateBlockEnforcer.extract_state_update_block(ai_response)
            return clean_fallback_response, current_phase, {'new_phase': current_phase}
