#!/usr/bin/env python3
"""
Test fix for UnboundLocalError in enhance_lesson_content function
"""

import sys
import os
import json

# Setup environment
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Set required environment variables
os.environ['GEMINI_API_KEY'] = 'test-key-for-testing'
os.environ['FLASK_ENV'] = 'development'

def test_unboundlocalerror_fix():
    """Test that the UnboundLocalError is fixed for current_probing_level_number_for_prompt"""
    print("=" * 60)
    print("TESTING UNBOUNDLOCALERROR FIX")
    print("=" * 60)
    
    try:
        # Import main module
        print("Importing main module...")
        import main
        print("✓ Main module imported successfully")
        
        # Create test client
        with main.app.test_client() as client:
            print("✓ Test client created")
            
            print("=" * 40)
            print("TEST CASE: Transition from diagnostic to teaching phase")
            print("=" * 40)
            
            # Test the specific scenario that was causing the error:
            # User completes diagnostic and transitions to teaching phase
            test_data = {
                "content": "I'm ready, let's start the lesson",  # This triggered the transition
                "user_id": "andrea_ugono_33305",  # Same user from error log
                "lesson_id": "P5-BST-005",        # Same lesson from error log
                "student_name": "Andrea"
            }
            
            print("Step 1: Sending request that caused UnboundLocalError...")
            print(f"User: {test_data['user_id']}")
            print(f"Lesson: {test_data['lesson_id']}")
            print(f"Content: {test_data['content']}")
            
            # This should NOT raise UnboundLocalError anymore
            response = client.post('/api/enhance-content', 
                                 json=test_data,
                                 headers={'Content-Type': 'application/json'})
            
            print(f"Response status: {response.status_code}")
            
            if response.status_code == 500:
                # Check if it's still the same UnboundLocalError
                response_text = response.get_data(as_text=True)
                if 'current_probing_level_number_for_prompt' in response_text:
                    print("✗ UNBOUNDLOCALERROR STILL EXISTS")
                    print("The fix didn't work - same variable error")
                    print(f"Response: {response_text[:500]}...")
                    return False
                elif 'UnboundLocalError' in response_text:
                    print("⚠ DIFFERENT UNBOUNDLOCALERROR")
                    print("Fixed the original error, but there's a different UnboundLocalError")
                    print(f"Response: {response_text[:500]}...")
                    return None
                else:
                    print("⚠ DIFFERENT 500 ERROR")
                    print("Not an UnboundLocalError, but still a 500 error")
                    print(f"Response: {response_text[:300]}...")
                    return None
                    
            elif response.status_code == 200:
                print("✓ UNBOUNDLOCALERROR FIXED")
                print("Request processed successfully")
                
                try:
                    response_data = response.get_json()
                    if response_data and 'content' in response_data:
                        content_preview = response_data['content'][:200]
                        print(f"Response content preview: {content_preview}...")
                        print("✓ Response contains valid content")
                        return True
                    else:
                        print("⚠ Response missing content but no error")
                        return None
                except:
                    print("⚠ Could not parse response JSON but no error")
                    return None
                    
            else:
                print(f"⚠ UNEXPECTED STATUS CODE: {response.status_code}")
                response_text = response.get_data(as_text=True)
                print(f"Response: {response_text[:300]}...")
                return None
                
    except Exception as e:
        print(f"✗ Test setup error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_diagnostic_variables_initialization():
    """Test that diagnostic variables are properly initialized for all phases"""
    print("\n" + "=" * 60)
    print("TESTING DIAGNOSTIC VARIABLES INITIALIZATION")
    print("=" * 60)
    
    try:
        import main
        
        with main.app.test_client() as client:
            # Test different phase scenarios
            test_scenarios = [
                {
                    "name": "Teaching Phase",
                    "data": {
                        "content": "Let's continue with the lesson",
                        "user_id": "test_teaching_phase",
                        "lesson_id": "test_lesson_teaching",
                        "student_name": "Test Student"
                    }
                },
                {
                    "name": "Diagnostic Phase", 
                    "data": {
                        "content": "start lesson",
                        "user_id": "test_diagnostic_phase",
                        "lesson_id": "test_lesson_diagnostic", 
                        "student_name": "Test Student"
                    }
                },
                {
                    "name": "Transition Phase",
                    "data": {
                        "content": "I understand, let's move on",
                        "user_id": "test_transition_phase",
                        "lesson_id": "test_lesson_transition",
                        "student_name": "Test Student"
                    }
                }
            ]
            
            for scenario in test_scenarios:
                print(f"\nTesting {scenario['name']}...")
                
                response = client.post('/api/enhance-content',
                                     json=scenario['data'],
                                     headers={'Content-Type': 'application/json'})
                
                if response.status_code == 500:
                    response_text = response.get_data(as_text=True)
                    if 'current_probing_level_number_for_prompt' in response_text:
                        print(f"✗ {scenario['name']}: Still has UnboundLocalError")
                        return False
                    elif 'UnboundLocalError' in response_text:
                        print(f"⚠ {scenario['name']}: Different UnboundLocalError")
                    else:
                        print(f"⚠ {scenario['name']}: Different 500 error")
                elif response.status_code == 200:
                    print(f"✓ {scenario['name']}: Working correctly")
                else:
                    print(f"? {scenario['name']}: Status {response.status_code}")
            
            print("✓ All scenarios tested")
            return True
            
    except Exception as e:
        print(f"✗ Test error: {e}")
        return False

def main():
    """Run all UnboundLocalError fix tests"""
    print("UNBOUNDLOCALERROR FIX VERIFICATION")
    print("=" * 60)
    
    # Test 1: Specific error scenario
    result1 = test_unboundlocalerror_fix()
    
    # Test 2: Various phase scenarios
    result2 = test_diagnostic_variables_initialization()
    
    print("\n" + "=" * 60)
    print("TEST RESULTS SUMMARY")
    print("=" * 60)
    
    if result1 is True:
        print("✓ Original UnboundLocalError: FIXED")
    elif result1 is False:
        print("✗ Original UnboundLocalError: STILL EXISTS")
    else:
        print("? Original UnboundLocalError: UNCLEAR")
    
    if result2 is True:
        print("✓ Variable initialization: WORKING")
    elif result2 is False:
        print("✗ Variable initialization: FAILED")
    else:
        print("? Variable initialization: UNCLEAR")
    
    overall_success = (result1 is True and result2 is True)
    print(f"\nOverall test result: {'PASS' if overall_success else 'NEEDS REVIEW'}")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
