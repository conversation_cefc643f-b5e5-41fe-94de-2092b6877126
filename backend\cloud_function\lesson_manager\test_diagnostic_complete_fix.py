#!/usr/bin/env python3
"""
Comprehensive Test for Diagnostic Complete Flag Fix
==================================================

Tests the enhanced backward transition prevention system that uses the diagnostic_complete flag
to prevent any reversion from teaching phases back to diagnostic phases.
"""

import sys
import os
import json
import logging

# Add current directory to path for imports
sys.path.append('.')

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_diagnostic_complete_flag_initialization():
    """Test that diagnostic_complete flag is properly initialized"""
    print("🧪 TEST 1: Diagnostic Complete Flag Initialization")
    print("=" * 60)
    
    try:
        from main import update_lesson_state_from_phase
        
        # Test state without diagnostic_complete flag
        test_state = {
            "current_lesson_phase": "diagnostic_start_probe",
            "student_name": "<PERSON>"
        }
        
        # Update state - should initialize diagnostic_complete
        updated_state = update_lesson_state_from_phase(test_state, "diagnostic_probing_L5_ask_q1")
        
        # Check if flag was initialized
        if "diagnostic_complete" in updated_state:
            if updated_state["diagnostic_complete"] == False:
                print("   ✅ diagnostic_complete flag properly initialized to False")
                return True
            else:
                print(f"   ❌ diagnostic_complete flag initialized to wrong value: {updated_state['diagnostic_complete']}")
                return False
        else:
            print("   ❌ diagnostic_complete flag not initialized")
            return False
            
    except Exception as e:
        print(f"   ❌ Test failed with error: {e}")
        return False

def test_diagnostic_completion_marking():
    """Test that diagnostic_complete flag is set when entering teaching phase"""
    print("\n🧪 TEST 2: Diagnostic Completion Marking")
    print("=" * 60)
    
    try:
        from main import update_lesson_state_from_phase
        
        # Test state in diagnostic phase
        test_state = {
            "current_lesson_phase": "diagnostic_probing_L5_eval_q5_decide_level",
            "diagnostic_complete": False,
            "student_name": "Andrea"
        }
        
        # Transition to teaching phase
        updated_state = update_lesson_state_from_phase(test_state, "teaching_start_level_5")
        
        # Check if diagnostic_complete was set to True
        if updated_state.get("diagnostic_complete") == True:
            print("   ✅ diagnostic_complete flag set to True when entering teaching phase")
            print(f"   📝 Assigned level: {updated_state.get('assigned_level', 'N/A')}")
            return True
        else:
            print(f"   ❌ diagnostic_complete flag not set correctly: {updated_state.get('diagnostic_complete')}")
            return False
            
    except Exception as e:
        print(f"   ❌ Test failed with error: {e}")
        return False

def test_backward_transition_prevention():
    """Test that backward transitions are prevented when diagnostic_complete=True"""
    print("\n🧪 TEST 3: Backward Transition Prevention")
    print("=" * 60)
    
    try:
        from phase_transition_fix import apply_phase_transition_fixes
        
        # Test context with diagnostic_complete=True
        test_context = {
            "diagnostic_complete": True,
            "student_info": {"first_name": "Andrea"},
            "current_probing_level_number_from_state": 5
        }
        
        # Simulate AI response that would cause backward transition
        ai_response_with_backward_transition = """
        Let me ask you a diagnostic question to assess your understanding.
        
        // AI_STATE_UPDATE_BLOCK_START
        {"new_phase": "diagnostic_probing_L5_ask_q1"}
        // AI_STATE_UPDATE_BLOCK_END
        """
        
        # Apply phase transition fixes
        clean_response, final_phase, state_updates = apply_phase_transition_fixes(
            current_phase="teaching",
            ai_response=ai_response_with_backward_transition,
            student_answers=[],
            context=test_context
        )
        
        # Check if backward transition was blocked
        if final_phase != "diagnostic_probing_L5_ask_q1":
            print(f"   ✅ Backward transition blocked successfully")
            print(f"   📝 Original AI phase: diagnostic_probing_L5_ask_q1")
            print(f"   📝 Final phase: {final_phase}")
            
            if state_updates.get('backward_transition_blocked'):
                print(f"   📝 Block reason: {state_updates.get('blocked_reason', 'N/A')}")
            
            return True
        else:
            print(f"   ❌ Backward transition NOT blocked - final phase: {final_phase}")
            return False
            
    except Exception as e:
        print(f"   ❌ Test failed with error: {e}")
        return False

def test_fallback_logic_enhancement():
    """Test enhanced fallback logic in main.py"""
    print("\n🧪 TEST 4: Enhanced Fallback Logic")
    print("=" * 60)
    
    try:
        # Test the fallback logic that would normally set diagnostic_progression_reason
        test_context = {
            "diagnostic_complete": True,
            "student_info": {"first_name": "Andrea"},
            "current_probing_level_number_from_state": 5,
            "current_question_index_from_state": 0
        }
        
        # Simulate scenario where AI fails to provide state updates
        # and system would normally fall back to diagnostic phase
        current_phase = "teaching"
        calculated_diagnostic_phase = "diagnostic_probing_L5_ask_q1"
        
        # Check if our logic would prevent the backward transition
        diagnostic_complete = test_context.get('diagnostic_complete', False)
        
        if diagnostic_complete and 'diagnostic' in calculated_diagnostic_phase.lower() and 'teaching' in current_phase.lower():
            print("   ✅ Enhanced fallback logic correctly identifies backward transition scenario")
            print(f"   📝 Current phase: {current_phase}")
            print(f"   📝 Calculated phase: {calculated_diagnostic_phase}")
            print(f"   📝 diagnostic_complete: {diagnostic_complete}")
            print("   📝 Backward transition would be blocked")
            return True
        else:
            print("   ❌ Enhanced fallback logic failed to identify backward transition scenario")
            return False
            
    except Exception as e:
        print(f"   ❌ Test failed with error: {e}")
        return False

def test_forward_progression_allowed():
    """Test that forward progression is still allowed"""
    print("\n🧪 TEST 5: Forward Progression Still Allowed")
    print("=" * 60)
    
    try:
        from phase_transition_fix import apply_phase_transition_fixes
        
        # Test context with diagnostic_complete=True
        test_context = {
            "diagnostic_complete": True,
            "student_info": {"first_name": "Andrea"},
            "current_probing_level_number_from_state": 5
        }
        
        # Simulate AI response with forward progression
        ai_response_with_forward_transition = """
        Great work! Let's move on to the quiz.
        
        // AI_STATE_UPDATE_BLOCK_START
        {"new_phase": "quiz_initiate"}
        // AI_STATE_UPDATE_BLOCK_END
        """
        
        # Apply phase transition fixes
        clean_response, final_phase, state_updates = apply_phase_transition_fixes(
            current_phase="teaching",
            ai_response=ai_response_with_forward_transition,
            student_answers=[],
            context=test_context
        )
        
        # Check if forward transition was allowed
        if final_phase == "quiz_initiate":
            print(f"   ✅ Forward transition allowed successfully")
            print(f"   📝 Current phase: teaching")
            print(f"   📝 Final phase: {final_phase}")
            return True
        else:
            print(f"   ❌ Forward transition blocked incorrectly - final phase: {final_phase}")
            return False
            
    except Exception as e:
        print(f"   ❌ Test failed with error: {e}")
        return False

def run_comprehensive_test():
    """Run all tests and provide summary"""
    print("🎯 COMPREHENSIVE DIAGNOSTIC COMPLETE FLAG TEST")
    print("=" * 80)
    print("Testing enhanced backward transition prevention using diagnostic_complete flag")
    print("=" * 80)
    
    tests = [
        test_diagnostic_complete_flag_initialization,
        test_diagnostic_completion_marking,
        test_backward_transition_prevention,
        test_fallback_logic_enhancement,
        test_forward_progression_allowed
    ]
    
    results = []
    for test_func in tests:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"   ❌ Test {test_func.__name__} failed with exception: {e}")
            results.append(False)
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 TEST RESULTS SUMMARY:")
    print("=" * 80)
    
    passed = sum(results)
    total = len(results)
    
    for i, (test_func, result) in enumerate(zip(tests, results), 1):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   Test {i}: {status} - {test_func.__name__}")
    
    print(f"\n📈 OVERALL RESULT: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED: Diagnostic complete flag fix is working correctly!")
        print("🚫 Backward transitions are now completely prevented")
        print("✅ Forward progression is preserved")
        return True
    else:
        print("⚠️  SOME TESTS FAILED: Review the implementation")
        return False

if __name__ == "__main__":
    success = run_comprehensive_test()
    sys.exit(0 if success else 1)
