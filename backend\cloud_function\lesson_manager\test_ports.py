#!/usr/bin/env python3
"""
Test port availability and system socket functionality
"""

import socket
import sys
import os

def test_port_availability(host, port):
    """Test if a port is available for binding"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex((host, port))
        sock.close()
        return result != 0  # True if port is available (connection failed)
    except Exception as e:
        print(f"Error testing port {port}: {e}")
        return False

def test_socket_binding(host, port):
    """Test if we can bind to a specific port"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        sock.bind((host, port))
        sock.listen(1)
        actual_port = sock.getsockname()[1]
        sock.close()
        print(f"✓ Successfully bound to {host}:{actual_port}")
        return True
    except Exception as e:
        print(f"✗ Failed to bind to {host}:{port} - {e}")
        return False

def main():
    print("=" * 50)
    print("WINDOWS SOCKET AND PORT TEST")
    print("=" * 50)
    
    # Test common hosts
    hosts = ['127.0.0.1', 'localhost', '0.0.0.0']
    ports = [5000, 5001, 5002, 5003, 8000, 8080]
    
    print("Testing port availability...")
    for host in hosts:
        for port in ports:
            available = test_port_availability(host, port)
            status = "available" if available else "in use"
            print(f"{host}:{port} - {status}")
    
    print("\nTesting socket binding...")
    for host in hosts:
        print(f"\nTesting {host}:")
        for port in [5001, 5002, 5003]:  # Avoid 5000 in case it's in use
            test_socket_binding(host, port)
    
    print("\nTesting system socket functionality...")
    try:
        # Test basic socket operations
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.bind(('127.0.0.1', 0))  # Let system choose port
        port = sock.getsockname()[1]
        sock.listen(1)
        print(f"✓ System-assigned port {port} works correctly")
        sock.close()
    except Exception as e:
        print(f"✗ Basic socket functionality failed: {e}")
    
    print("\nSocket test completed")

if __name__ == "__main__":
    main()
