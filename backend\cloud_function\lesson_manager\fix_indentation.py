#!/usr/bin/env python3
"""
Quick script to fix the indentation issues in the diagnostic progression logic
"""

import re

def fix_indentation():
    """Fix the indentation issues in main.py"""
    
    # Read the file
    with open('main.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find the problematic section and fix it
    # The issue is that the elif blocks need proper indentation
    
    # Pattern to find the diagnostic progression section
    pattern = r'(if \'diagnostic\' in lesson_phase_from_context:.*?)(else:\s*# CRITICAL FIX: For non-diagnostic phases)'
    
    match = re.search(pattern, content, re.DOTALL)
    if match:
        diagnostic_section = match.group(1)
        
        # Fix the indentation by replacing incorrect indentation patterns
        fixed_section = diagnostic_section
        
        # Fix elif blocks that should be indented under the main if
        fixed_section = re.sub(
            r'\n        elif \'eval_q\' in lesson_phase_from_context',
            r'\n            elif \'eval_q\' in lesson_phase_from_context',
            fixed_section
        )
        
        fixed_section = re.sub(
            r'\n        elif lesson_phase_from_context\.endswith\(\'_ask_q1\'\):',
            r'\n            elif lesson_phase_from_context.endswith(\'_ask_q1\'):',
            fixed_section
        )
        
        fixed_section = re.sub(
            r'\n        elif \'eval_q5_decide_level\' in lesson_phase_from_context:',
            r'\n            elif \'eval_q5_decide_level\' in lesson_phase_from_context:',
            fixed_section
        )
        
        # Fix content inside elif blocks (should be indented 4 more spaces)
        lines = fixed_section.split('\n')
        fixed_lines = []
        in_elif_block = False
        elif_indent_level = 0
        
        for line in lines:
            # Check if this is an elif line
            if re.match(r'(\s*)elif ', line):
                in_elif_block = True
                elif_indent_level = len(line) - len(line.lstrip())
                fixed_lines.append(line)
            elif re.match(r'(\s*)else:', line) and 'CRITICAL FIX: For non-diagnostic phases' in line:
                in_elif_block = False
                fixed_lines.append(line)
            elif in_elif_block and line.strip():
                # This is content inside an elif block
                current_indent = len(line) - len(line.lstrip())
                if current_indent <= elif_indent_level:
                    # Need to increase indentation
                    new_indent = elif_indent_level + 4
                    fixed_line = ' ' * new_indent + line.lstrip()
                    fixed_lines.append(fixed_line)
                else:
                    fixed_lines.append(line)
            else:
                fixed_lines.append(line)
        
        fixed_section = '\n'.join(fixed_lines)
        
        # Replace the section in the content
        new_content = content.replace(diagnostic_section, fixed_section)
        
        # Write back to file
        with open('main.py', 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print("✅ Indentation fixed successfully!")
        return True
    else:
        print("❌ Could not find the diagnostic section to fix")
        return False

if __name__ == "__main__":
    fix_indentation()
